# How to create a perovskite composition
In NOMAD it is possible to utilize the perovskite ions database for creating perovskite composition entries.

The documentation below contains instructions for the following tasks:

- Create a new perovskite composition in NOMAD
- Create a new perovskite composition from an existing JSON file
- Download a JSON file with a perovskite composition from your own upload
- Upload and publish a Perovskite composition to the NOMAD Database



<!-- A standardized perovskite composition entry can be created using the ions database.
To create one you would need to follow the steps below in which we will create a Cs<sub>0.05</sub>FA<sub>0.76</sub>MA<sub>0.16</sub>PbBr<sub>1.5</sub>I<sub>1.5</sub>
composition entry: -->

<!-- <iframe src="https://scribehow.com/embed/How_to_Create_a_New_Perovskite_Composition_Upload__HdNr9pn5S_2jFM5Ii4FYkA?removeLogo=true" width="100%" height="640" allowfullscreen frameborder="0"></iframe> -->

## Create a new perovskite composition in NOMAD
<iframe src="https://scribehow.com/shared/Create_a_perovskite_composition_file_in_NOMAD__vKN4StPRTfSEHLIDEYB2Dg" width="100%" height="640" allowfullscreen frameborder="0"></iframe>

## Upload a Perovskite composition created elsewhere to the NOMAD Database
<iframe src="https://scribehow.com/shared/Create_a_perovskite_composition_from_file__4eYs7X3_Seili_MUsb7v2w" width="100%" height="640" allowfullscreen frameborder="0"></iframe>

## Download a JSON file with a perovskite composition from your own upload
<iframe src="https://scribehow.com/shared/Download_a_JSON_file_with_a_perovksite_composition_from_own_upload__sT5TFXMWQw-XOn9zacWOzA" width="100%" height="640" allowfullscreen frameborder="0"></iframe>


## Publish a Perovskite composition in NOMAD
<iframe src="https://scribehow.com/shared/How_To_Publish_Data_on_NOMAD__RZKTrs-1RcmQ49VcsJ1dRQ" width="100%" height="640" allowfullscreen frameborder="0"></iframe>




