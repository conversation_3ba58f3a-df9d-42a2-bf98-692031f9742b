---
hide: toc
---

# NOMAD How-to guides

## Users

These how-to guides target NOMAD users and cover data management, exploration, analysis
with NOMAD graphical web-interface and APIs.

<div markdown="block" class="home-grid">
<div markdown="block">

### Manage and find data

Use NOMAD to manage, explore, and analyze data.

- [Upload and publish data for supported formats](manage/upload.md)
- [Use ELNs](manage/eln.md)
- [Explore data](manage/explore.md)
- [Use NORTH](manage/north.md)

</div>
<div markdown="block">

### Programmatic use

Use NOMAD's functions programmatically and via its APIs.

- [Use the API](programmatic/api.md)
- [Publish data using python](programmatic/publish_python.md)
- [Install nomad-lab](programmatic/pythonlib.md)
- [Access processed data](programmatic/archive_query.md)
- [Transform data](programmatic/json_transformer.md)

</div>
</div>

## Data stewards, administrators, and developers

These how-to guides allow advanced users, NOMAD administrators, data stewards, and
developers to customize and operate NOMAD and NOMAD Oasis or contribute to NOMAD's
development.

<div markdown="block" class="home-grid">
<div markdown="block">

### NOMAD Oasis — self-hosting

Self-hosting NOMAD for your lab or institution.

- [Configure an Oasis](oasis/configure.md)
- [Deploy an Oasis](oasis/deploy.md)
- [Update an Oasis](oasis/update.md)
- [Perform admin tasks](oasis/admin.md)

</div>
<div markdown="block">

### Plugins

Learn how to write NOMAD plugins.

- [Introduction to plugins](plugins/plugins.md)
- [Write an API](plugins/apis.md)
- [Write an app](plugins/apps.md)
- [Write an example upload](plugins/example_uploads.md)
- [Write a normalizer](plugins/normalizers.md)
- [Write a parser](plugins/parsers.md)
- [Write a schema packages](plugins/schema_packages.md)

</div>
<div markdown="block">

### Customization

Customize NOMAD and tailor NOMAD Oasis.

- [Write a schema](customization/basics.md)
- [Define ELNs](customization/elns.md)
- [Use base sections](customization/base_sections.md)
- [Parse tabular data](customization/tabular.md)
- [Define workflows](customization/workflows.md)
- [Work with units](customization/units.md)
- [Use HDF5 to handle large quantities](customization/hdf5.md)
- [Use Mapping parser to write data on archive](customization/mapping_parser.md)

</div>
<div markdown="block">

### Development

Become a NOMAD developer and contribute to the source code.

- [Get started](develop/setup.md)
- [Navigate the code](develop/code.md)
- [Contribute](develop/contrib.md)
- [Extend the search](develop/search.md)

</div>
</div>

<h2>One last thing</h2>

If you can't find what you're looking for in our guides,
[contact our team](mailto:<EMAIL>) for personalized help and assistance.
Don't worry, we're here to help and learn what we're doing wrong!
