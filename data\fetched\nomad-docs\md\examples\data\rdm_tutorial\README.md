# Tailored Research Data Management (RDM) with NOMAD

This notebook will teach you how you can build tailored research data management
(RDM) solutions using NOMAD. It uses existing thermally activated delayed
fluorescent (TADF) molecule data to teach you how we can use the NOMAD to turn
research data into datasets that are FAIR: Findable, Accessible, Interoperable
and Reusable. The end-result can be distributed as a NOMAD plugin: a
self-contained Python package that can be installed on a NOMAD Oasis.

- Click on the **FILES** tab and open `tutorial.ipynb`.
- Launch notebook in *jupyter*. If jupyter is already running, please press stop and start a new one.
- Follow the notebook for the rest of the tutorial.
