# Combinatorial Synthesis
Each combinatorial library contains samples wich are their own archives.

The library contains references to the samples.

The samples contain a `sample_number` within the library.

## Continuous Combinatorial
### Library
The library has a references to the samples together with their `sample_number` and their
`x`, `y`, `z` position.

### Sample
The sample has a:
1. ID
2. Elemental composition
3. Bandgap
4. Optical transmission (R,T arrays)
5. Thickness
6. Resistivity
7. Conductivity
8. Sheet resistance
9. XRD (q array)
10. Annealing temperature
11. Deposition methods
12. Measurement methods

#### HZB Unold Sample
Hide: Electrical
1. TRPL
2. PLQY

## Discrete Combinatorial Synthesis
*E.g. well plates*

To be specified.
