# Introduction

This example presents the capabilities of the NOMAD platform to convert and and standardize data stored with Wavemetrics [Igor Pro](https://www.wavemetrics.com/) into the [NeXus](https://www.nexusformat.org/) format.

It contains two examples:
- Converting Igor Binary Wave (.ibw) and packed experiment  (.pxp) into NeXus files.
- An example conversion of time-resolved resonant diffraction data into the standardized
NeXus format for X-ray diffraction
([NXxrd](https://fairmat-nfdi.github.io/nexus_definitions/classes/contributed_definitions/NXxrd.html)).

# Viewing uploaded data

Below, you find an overview of your uploaded data.
Click on the `> /` button to get a list of your data or select **FILES** from the top menu of this upload.

# Running the examples

The examples work can be run using the Jupyter Notebook in the NOMAD Remote Tools Hub (NORTH). To start it, note your upload id (which you find on top of this explanation) and select **ANALYZE** from the top menu, then **NOMAD Remote Tools Hub**. In the appearing list you'll find the `jupyter` container, click on it and click **LAUNCH**.

After a few moments a new tab will open which displays a jupyter environment providing the required analysis tools.
To find the examples navigate to uploads inside the jupyter hub browser and select the folder with your noted upload id.
There you'll find the example `ipynb` notebooks.
Double-clicking one of the notebooks will open the example in the jupyter main window.
From here you find detailed instructions inside each of the notebooks.

# Where to go from here?

If you're interested in using this pipeline and NOMAD in general you'll find support at [FAIRmat](https://www.fairmat-nfdi.eu/fairmat/consortium).

For general questions regarding the Igor pipeline and if you're interested in building one for your
own research workflow, you may contact [Laurenz Rettig](https://www.fair-di.eu/fairmat/about-fairmat/team-fairmat) and [Lukas Pielsticker](https://www.fair-di.eu/fairmat/about-fairmat/team-fairmat) from the FAIRmat consortium.
