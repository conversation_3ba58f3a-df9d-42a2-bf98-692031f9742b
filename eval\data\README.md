# Promt to chatGPT for converting gold_candidates_XX.jsonl to gold_discord.jsonl

I now have the following candidates from discord. I don't really want to go through these by hand using the widget that we created. Also, for proper evaluation we should not use direct quotations that we harvested by rather some sort of derivations of these Q&A's. Could you take all this data and try to compile 25-50 well-defined Q&A's, relatively concise 1-2 sentence question, 1 paragraph max answer, and then return that in a jsonl with source labeled as "discord-issues"
