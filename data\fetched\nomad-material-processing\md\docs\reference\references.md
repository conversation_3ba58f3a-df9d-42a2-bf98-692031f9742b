# Technical References

## NOMAD Metainfo Browser

Allows users to explore all available schemas, including their sections, values, and references, in a detailed and interactive manner.

It can be accessed through the *Analyze* tab in any NOMAD instance. To explore the schemas specific to the NOMAD-material-processing plugin, you can visit the [Metainfo Browser on the Example Oasis](https://nomad-lab.eu/prod/v1/oasis/gui/analyze/metainfo/nomad_material_processing) and review all the details of the plugin's structures and components.

# Glossary

## [subclass](../reference/references.md#subclass)

**Inheritance** of classes. Whenever a Python class **inherits** from another one, this is called [subclass](../reference/references.md#subclass), meaning that it is semantically a children of the **inherited** one.

## subsection

**Composition** of classes. A class can contain some subsection **nested** (or **composed**) inside it. These classes are not related by inheritance relationship.

!!! info
    Please also refer to the [glossary in the NOMAD documentation](https://nomad-lab.eu/prod/v1/staging/docs/glossary.html) to find definitions and explanations for specific technical terms used throughout the NOMAD software.