# References
!!! danger "Work in progress"
<!-- To get the full benefits of the reader, you can visit and utilize the following tools and platforms. 

## NOMAD
The reader is fully integrated into the [NOMAD](https://nomad-lab.eu/prod/v1/gui/about/information) research data management platform (a free and open sorce project for data management). In NOMAD, you can find a full example for both the `STS` as well as the `STM` reader. The example can be resused to create new uploads and later the uploads can be compared, analyzed, publihsed, and shared with the different colaborators and communities. You can have a look at the [NOMAD documentation](https://nomad-lab.eu/prod/v1/util/docs/index.html) as well.

## NeXus
The reader is using the [NXsts](https://fairmat-nfdi.github.io/nexus_definitions/classes/contributed_definitions/NXsts.html#nxsts) ([GitHub page](https://github.com/FAIRmat-NFDI/nexus_definitions/blob/fairmat/contributed_definitions/NXsts.nxdl.xml)) application definition (as a standardized schema) which is developed using the [NeXus ontology](https://www.nexusformat.org/) ([GitHub page](https://github.com/FAIRmat-NFDI/nexus_definitions/tree/fairmat)). To understand the application definition, properly understanding NeXus ontology can be helpful.
 -->
