# Welcome to the `nomad-utility-workflows` documentation

A module with utilities for interacting with NOMAD programmatically, e.g., via a workflow manager.

## Introduction

NOMAD's API and workflow functionalities provide users with a route to FAIR data management of complex and highthroughput processes. However, learning how to fully utilize these capabilities can be time consuming, or even intimidating depending on your programming background. This utility module aims to lower the barrier to using these tools, currently with 2 distinct sets of tools: 1. python API wrappers, and 2. automated workflow yaml generation for building custom workflows.

<div markdown="block" class="home-grid">

<div markdown="block">

### Tutorial

Prototypical usage of the tool within a broader project and workflow management context:

- [NOMAD Tutorial Workflow](https://fairmat-nfdi.github.io/nomad-tutorial-workflows/latest){:target="\_blank"}

</div>

<div markdown="block">

### How-to guides

How-to guides provide step-by-step instructions for a wide range of tasks, with the overarching topics:

- [Install this plugin](how_to/install_this_plugin.md)
- [Use API functions](how_to/use_api_functions.md)
- [Create Custom Workflows](how_to/create_custom_workflows.md)
- [Add Custom Tasks](how_to/add_custom_tasks.md)


</div>
<!-- <div markdown="block">

### Explanation

The explanation provides background knowledge on functionalities of this plugin:

- [Workflows](explanation/workflows.md)

</div> -->

<div markdown="block">

### Reference

The reference section includes specifications for the relevant user-callable functions:

- [Workflows](reference/workflows.md)

</div>
</div>

## Main contributors
| Name | E-mail     |
|------|------------|
| Joseph F. Rudzinski | [<EMAIL>](mailto:<EMAIL>)
| Andrea Albino |
| Tristan Bereau |
| Nathan Daelman |
| Alvin N. Ladines |
| Bernadette Mohr |
| Jesper Pedersen |
| Luis J. Walter |