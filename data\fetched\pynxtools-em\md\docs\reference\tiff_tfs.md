# ThermoFisher Tagged Image File Format TIFF

The pynxtools-em parser and normalizer reads the following content and maps them on respective NeXus concepts that are defined in the NXem application definition:

| Concept | NeXus/HDF5 |
| --------------- | --------------  |
| Beam/Aperture | :heavy_check_mark: |
| Beam/ImageMode | :heavy_check_mark: |
| Beam/Spot | :heavy_check_mark: |
| Beam/StigmatorX | :heavy_check_mark: |
| Beam/StigmatorY | :heavy_check_mark: |
| Detectors/Mode | :heavy_check_mark: |
| Detectors/Name | :heavy_check_mark: |
| EBeam/Aperture | :heavy_check_mark: |
| EBeam/ApertureDiameter | :heavy_check_mark: |
| EBeam/BeamCurrent | :heavy_check_mark: |
| EBeam/BeamMode | :heavy_check_mark: |
| EBeam/DynamicFocusIsOn | :heavy_check_mark: |
| EBeam/EmissionCurrent | :heavy_check_mark: |
| EBeam/HV | :heavy_check_mark: |
| EBeam/UseCase | :heavy_check_mark: |
| EBeam/WD | :heavy_check_mark: |
| ETD/Signal | :heavy_check_mark: |
| Scan/Dwelltime | :heavy_check_mark: |
| Stage/StageR | :heavy_check_mark: |
| Stage/StageTa | :heavy_check_mark: |
| Stage/StageTb | :heavy_check_mark: |
| Stage/StageX | :heavy_check_mark: |
| Stage/StageY | :heavy_check_mark: |
| Stage/StageZ | :heavy_check_mark: |
| System/BuildNr | :heavy_check_mark: |
| System/Scan | :heavy_check_mark: |
| System/Source | :heavy_check_mark: |
| System/SystemType | :heavy_check_mark: |
| T1/Signal | :heavy_check_mark: |
| T2/Signal | :heavy_check_mark: |
| T3/Signal | :heavy_check_mark: |
