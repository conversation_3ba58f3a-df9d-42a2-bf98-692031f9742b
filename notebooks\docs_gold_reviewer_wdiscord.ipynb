{"cells": [{"cell_type": "markdown", "id": "4a0b835c", "metadata": {}, "source": ["# NOMAD RAG — Gold Q&A Reviewer (v3)\n", "\n", "A cleaner UI for curating candidate Q&As into your gold set.\n", "\n", "**Improvements over v2**\n", "- Two-column layout (selector on the left, preview on the right) — no overlap.\n", "- Preview panel scrolls independently (won’t block the selector).\n", "- Adjustable preview length + \"Show full answers\" toggle.\n", "- Clear success banner with appended items.\n", "- Gold summary refreshes automatically.\n", "\n", "> Expected input: `eval/data/gold_review.csv` with columns: `question, proposed_answer, source_url, title, section, method, score, id`.\n"]}, {"cell_type": "code", "execution_count": null, "id": "2bcf24ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1092 total candidates from [PosixPath('../eval/gold_review.csv'), PosixPath('../eval/gold_candidates_discord.csv'), <PERSON>six<PERSON><PERSON>('../eval/gold_candidates_discord_authored.csv')]\n", "Existing gold entries across sinks: 0\n"]}], "source": ["import os, json, re\n", "from pathlib import Path\n", "import pandas as pd\n", "from IPython.display import display, Markdown, HTML\n", "import ipywidgets as W\n", "\n", "# ---- Paths (edit if needed) ----\n", "CANDIDATES = {\n", "    \"docs\":    Path(\"../eval/data/gold_review.csv\"),               # from docs harvester\n", "    \"discord\": Path(\"../eval/data/gold_candidates_discord.csv\"),   # from discord harvester\n", "    \"discord_authored\": Path(\"../eval/data/gold_candidates_discord_authored.csv\"), # from discord authored harvester\n", "}\n", "GOLD_FILES = {\n", "    \"docs\":    Path(\"../eval/data/gold_nomad_docs.jsonl\"),\n", "    \"discord\": Path(\"../eval/data/gold_nomad_discord.jsonl\"),\n", "    \"discord_authored\": Path(\"../eval/data/gold_nomad_discord_authored.jsonl\"),\n", "    \"all\":     Path(\"../eval/data/gold_nomad_all.jsonl\"),          # unified optional sink\n", "}\n", "for p in GOLD_FILES.values():\n", "    p.parent.mkdir(parents=True, exist_ok=True)\n", "\n", "# ---- Load candidates with a 'source' column ----\n", "frames = []\n", "required = {\"question\",\"proposed_answer\",\"source_url\",\"title\",\"section\",\"method\",\"score\",\"id\"}\n", "for src, csv_path in CANDIDATES.items():\n", "    if not csv_path.exists():\n", "        print(f\"⚠️ Missing candidate CSV for {src}: {csv_path}\")\n", "        continue\n", "    df = pd.read_csv(csv_path)\n", "    miss = required - set(df.columns)\n", "    if miss:\n", "        raise ValueError(f\"{src} CSV missing columns: {miss}\")\n", "    df[\"__source__\"] = src\n", "    frames.append(df)\n", "\n", "if not frames:\n", "    raise FileNotFoundError(\"No candidate CSVs found. Check CANDIDATES paths.\")\n", "\n", "df_all = pd.concat(frames, ignore_index=True)\n", "\n", "def load_gold(path: Path):\n", "    if not path.exists(): return []\n", "    with path.open(\"r\", encoding=\"utf-8\") as f:\n", "        return [json.loads(line) for line in f if line.strip()]\n", "\n", "def norm_question(q: str) -> str:\n", "    q = (q or \"\").lower().strip()\n", "    q = re.sub(r\"\\s+\", \" \", q)\n", "    q = re.sub(r\"[^\\w\\s\\?\\-]\", \"\", q)  # keep ? and -\n", "    return q\n", "\n", "# Build an in-memory dedup index across all gold files\n", "existing_keys = set()\n", "for sink in GOLD_FILES.values():\n", "    for rec in load_gold(sink):\n", "        existing_keys.add((norm_question(rec.get(\"question\",\"\")), rec.get(\"source_url\",\"\")))\n", "\n", "print(f\"Loaded {len(df_all)} total candidates from {list(CANDIDATES.values())}\")\n", "print(f\"Existing gold entries across sinks: {len(existing_keys)}\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e2f58e0b", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "49af8c1b325b40f4923b379561ea5451", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(Dropdown(description='Source', layout=Layout(width='160px'), options=('all', 'discord', 'discor…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ecd228f4237d4629996609510316aeee", "version_major": 2, "version_minor": 0}, "text/plain": ["GridBox(children=(VBox(children=(SelectMultiple(description='Select', layout=Layout(height='560px', overflow='…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "26baddbfec8145a582e5bfedb90428f1", "version_major": 2, "version_minor": 0}, "text/plain": ["HTML(value='<hr/>')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c7028d53c31a41d0b5506db9c8a69bd9", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# ---------- Widgets & Layout ----------\n", "# Filters\n", "source_opts = [\"all\"] + sorted(df_all[\"__source__\"].unique().tolist())\n", "\n", "source_dd = W.Dropdown(options=source_opts, value=\"all\", description=\"Source\", layout=W.Layout(width=\"160px\"))\n", "\n", "min_score = <PERSON><PERSON>(value=0.55, min=0.0, max=1.0, step=0.05,\n", "                          description=\"Min score\", continuous_update=False, layout=W.Layout(width=\"240px\"))\n", "method_opts = [\"(any)\"] + sorted(df_all[\"method\"].dropna().unique().tolist())\n", "method_dd = W.Dropdown(options=method_opts, value=\"(any)\", description=\"Method\", layout=W.Layout(width=\"220px\"))\n", "search_box = W.Text(value=\"\", description=\"Search\", placeholder=\"text in q/a/title/section\", layout=W.Layout(width=\"360px\"))\n", "refresh_btn = <PERSON><PERSON>(description=\"Refresh\", layout=<PERSON><PERSON>Layout(width=\"120px\"))\n", "top_bar = <PERSON><PERSON>([source_dd, min_score, method_dd, search_box, refresh_btn])\n", "\n", "# Sinks: where to write gold\n", "sink_opts = [\"auto (by source)\", \"unified (all)\"]\n", "sink_dd = W.Dropdown(options=sink_opts, value=\"auto (by source)\", description=\"Write to\", layout=W.Layout(width=\"240px\"))\n", "\n", "# Selector (left panel)\n", "select_box = <PERSON><PERSON>(options=[], rows=22, description=\"Select\",\n", "                              layout=W.Layout(width=\"560px\", height=\"560px\", overflow=\"auto\"))\n", "add_btn = <PERSON><PERSON>(description=\"Add to Gold\", button_style=\"success\", layout=<PERSON>.Layout(width=\"160px\"))\n", "status_out = W.Output(layout=W.Layout(width=\"560px\"))\n", "\n", "left_col = <PERSON><PERSON>([select_box, <PERSON><PERSON>([sink_dd, add_btn]), status_out],\n", "                  layout=W.Layout(width=\"580px\", min_width=\"580px\", max_width=\"580px\"))\n", "\n", "# Preview (right panel)\n", "preview_len = W.Int<PERSON>lide<PERSON>(value=1200, min=200, max=6000, step=100, description=\"Preview chars\",\n", "                          readout=True, layout=W.Layout(width=\"360px\"))\n", "full_toggle = W.Checkbox(value=False, description=\"Show full answers\")\n", "preview_header = <PERSON><PERSON>([preview_len, full_toggle])\n", "\n", "preview_out = W.Output(layout=W.Layout(border=\"1px solid #ddd\", padding=\"8px\",\n", "                                       height=\"560px\", overflow=\"auto\", width=\"100%\"))\n", "right_col = <PERSON>.VBox([preview_header, preview_out])\n", "\n", "# Layout\n", "main_area = <PERSON><PERSON>(children=[left_col, right_col],\n", "                      layout=W.Layout(grid_template_columns=\"580px 1fr\", grid_gap=\"12px\", width=\"100%\"))\n", "gold_summary_out = W.Output()\n", "\n", "display(top_bar, main_area, W.HTML(\"<hr/>\"), gold_summary_out)\n", "\n", "def format_option(row):\n", "    q_short = (row[\"question\"][:96] + \"…\") if len(row[\"question\"]) > 100 else row[\"question\"]\n", "    tag = row[\"__source__\"]\n", "    return f\"[{row['score']:.2f}] ({tag}) {row['method']}: {q_short} — ({row['title']} › {row['section']})\"\n", "\n", "def filtered_df():\n", "    df = df_all.copy()\n", "    if source_dd.value != \"all\":\n", "        df = df[df[\"__source__\"] == source_dd.value]\n", "    df = df[df[\"score\"] >= float(min_score.value)]\n", "    if method_dd.value != \"(any)\":\n", "        df = df[df[\"method\"] == method_dd.value]\n", "    q = search_box.value.strip().lower()\n", "    if q:\n", "        mask = (\n", "            df[\"question\"].str.lower().str.contains(q, na=False) |\n", "            df[\"proposed_answer\"].str.lower().str.contains(q, na=False) |\n", "            df[\"title\"].str.lower().str.contains(q, na=False) |\n", "            df[\"section\"].str.lower().str.contains(q, na=False)\n", "        )\n", "        df = df[mask]\n", "    return df\n", "\n", "def apply_filter(_=None):\n", "    df = filtered_df()\n", "    select_box.options = [(format_option(row), row[\"id\"]) for _, row in df.iterrows()]\n", "    with status_out:\n", "        status_out.clear_output()\n", "        print(f\"Filtered candidates: {len(df)} (source={source_dd.value})\")\n", "    update_preview()\n", "\n", "def shorten(text: str, limit: int) -> str:\n", "    return text if len(text) <= limit else text[:limit] + \" …\"\n", "\n", "def update_preview(_=None):\n", "    df = filtered_df()\n", "    idx = {row[\"id\"]: row for _, row in df.iterrows()}\n", "    chosen = list(select_box.value)[:8]\n", "    with preview_out:\n", "        preview_out.clear_output()\n", "        if not chosen:\n", "            display(Markdown(\"> Select candidates on the left to preview…\"))\n", "            return\n", "        for cid in chosen:\n", "            row = idx.get(cid)\n", "            if row is None: continue\n", "            ans = row[\"proposed_answer\"] if full_toggle.value else shorten(row[\"proposed_answer\"], int(preview_len.value))\n", "            html = f\"\"\"\n", "            <div style='border:1px solid #eee; padding:10px; margin:8px 0; border-radius:8px'>\n", "              <div style='font-weight:600'>[{row['__source__']}] Question:</div>\n", "              <div style='margin:4px 0 8px 0'>{row['question']}</div>\n", "              <div style='font-weight:600'>Proposed answer:</div>\n", "              <div style='margin:4px 0 8px 0'>{ans}</div>\n", "              <div style='margin-top:6px'>\n", "                <b>Source</b>: <a href='{row['source_url']}' target='_blank'>{row['title']} › {row['section']}</a>\n", "                &nbsp; | &nbsp; <i>{row['method']}</i> &nbsp; score={row['score']:.2f}\n", "              </div>\n", "            </div>\n", "            \"\"\"\n", "            display(HTML(html))\n", "\n", "def show_gold_summary():\n", "    # Show small summary per sink\n", "    with gold_summary_out:\n", "        gold_summary_out.clear_output()\n", "        lines = []\n", "        for name, path in GOLD_FILES.items():\n", "            n = sum(1 for _ in path.open(\"r\", encoding=\"utf-8\")) if path.exists() else 0\n", "            lines.append(f\"- **{name}** → {path} — **{n}** entries\")\n", "        display(Markdown(\"### Gold sinks\\n\" + \"\\n\".join(lines)))\n", "\n", "def write_gold_records(records, sink_name: str):\n", "    path = GOLD_FILES[sink_name]\n", "    with path.open(\"a\", encoding=\"utf-8\") as f:\n", "        for r in records:\n", "            f.write(json.dumps(r, ensure_ascii=False) + \"\\n\")\n", "\n", "def add_selected_to_gold(_):\n", "    df = filtered_df()\n", "    by_id = {row[\"id\"]: row for _, row in df.iterrows()}\n", "    picked = list(select_box.value)\n", "    if not picked:\n", "        with status_out:\n", "            status_out.clear_output()\n", "            display(Markdown(\"⚠️ **No candidates selected.**\"))\n", "        return\n", "\n", "    # Build records, dedup, and route to sinks\n", "    added = []\n", "    new_by_sink = {\"docs\": [], \"discord\": [], \"discord_authored\": []}\n", "\n", "    unified_new = []\n", "\n", "    for cid in picked:\n", "        row = by_id.get(cid)\n", "        if row is None: continue\n", "        key = (norm_question(row[\"question\"]), row[\"source_url\"])\n", "        if key in existing_keys:\n", "            continue\n", "\n", "        rec = {\n", "            \"question\": row[\"question\"],\n", "            \"gold_answer\": row[\"proposed_answer\"],\n", "            \"gold_urls\": [row[\"source_url\"]],\n", "            \"title\": row.get(\"title\",\"\"),\n", "            \"section\": row.get(\"section\",\"\"),\n", "            \"source_url\": row[\"source_url\"],\n", "            \"meta\": {\"method\": row.get(\"method\",\"\"), \"score\": float(row.get(\"score\",0)), \"id\": row.get(\"id\",\"\"), \"source\": row[\"__source__\"]},\n", "        }\n", "\n", "        src = row[\"__source__\"]\n", "        new_by_sink[src].append(rec)\n", "        unified_new.append(rec)\n", "        added.append(f\"[{src}] {row['question']}\")\n", "        existing_keys.add(key)\n", "\n", "    # write to chosen sinks\n", "    target = sink_dd.value\n", "    if target == \"unified (all)\":\n", "        if unified_new:\n", "            write_gold_records(unified_new, \"all\")\n", "    else:  # auto by source\n", "        if new_by_sink[\"docs\"]:\n", "            write_gold_records(new_by_sink[\"docs\"], \"docs\")\n", "        if new_by_sink[\"discord\"]:\n", "            write_gold_records(new_by_sink[\"discord\"], \"discord\")\n", "        if new_by_sink[\"discord_authored\"]:\n", "            write_gold_records(new_by_sink[\"discord_authored\"], \"discord_authored\")\n", "\n", "\n", "    with status_out:\n", "        status_out.clear_output()\n", "        if added:\n", "            items = \"<br/>\".join(\"• \" + q for q in added[:8])\n", "            more = \"\" if len(added) <= 8 else f\"<br/>… and {len(added)-8} more\"\n", "            display(HTML(f\"<div style='background:#e9f7ef;border-left:6px solid #2ecc71;padding:10px'>\"\n", "                         f\"<b>Added {len(added)}</b> entries to <code>{sink_dd.value}</code>.\"\n", "                         f\"<div style='margin-top:6px'>{items}{more}</div></div>\"))\n", "        else:\n", "            display(HTML(\"<div style='background:#fdecea;border-left:6px solid #e74c3c;padding:10px'>Nothing added (all duplicates).</div>\"))\n", "\n", "    show_gold_summary()\n", "\n", "# wire events\n", "refresh_btn.on_click(apply_filter)\n", "select_box.observe(update_preview, names=\"value\")\n", "add_btn.on_click(add_selected_to_gold)\n", "preview_len.observe(update_preview, names=\"value\")\n", "full_toggle.observe(update_preview, names=\"value\")\n", "\n", "# initial render\n", "apply_filter()\n", "show_gold_summary()\n"]}, {"cell_type": "code", "execution_count": null, "id": "87e40020", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON> (nomad-ragbot)", "language": "python", "name": "nomad-ragbot"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}