# /// script
# requires-python = ">=3.10"
# dependencies = [
#     "openai",
#     "chromadb",
#     "requests",
# ]
# ///

import logging
import os
import sys
from typing import List, Dict, Tuple

# Add the src directory to the Python path to import our custom modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from nomad_ragbot.query.query import RAGQueryEngine

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConversationWorkflow:
    """
    Main conversational workflow that integrates the RAG query engine with memory and conversation management.
    """
    
    def __init__(self, 
                 chromadb_host: str = "localhost",
                 chromadb_port: int = 8000,
                 openai_api_key: str = "not-needed",
                 openai_base_url: str = "http://**************:11434/v1",
                 embedding_url: str = "http://**************:11434/api/embed",
                 embedding_model: str = "nomic-embed-text",
                 generator_model: str = "gpt-oss:20b"):
        """
        Initialize the conversation workflow with RAG capabilities.
        """
        logger.info("Initializing Conversation Workflow...")
        
        # Initialize the RAG query engine
        try:
            self.rag_engine = RAGQueryEngine(
                chromadb_host=chromadb_host,
                chromadb_port=chromadb_port,
                openai_api_key=openai_api_key,
                openai_base_url=openai_base_url,
                embedding_url=embedding_url,
                embedding_model=embedding_model,
                generator_model=generator_model
            )
            logger.info("RAG Query Engine initialized successfully.")
        except Exception as e:
            logger.error(f"Failed to initialize RAG Query Engine: {e}")
            raise
        
        # Initialize conversation memory
        self.conversation_history = []
        self.system_message = {
            "role": "system", 
            "content": """You are NOMAD Bot, a helpful and friendly assistant specializing in NOMAD, a platform for managing and sharing materials science data. 

You have access to a comprehensive knowledge base about NOMAD and can provide detailed, accurate answers about:
- How to use NOMAD platform features
- Materials science data management
- NOMAD's APIs and tools
- Best practices for data organization
- Technical documentation and tutorials

Always be conversational, helpful, and provide practical guidance. If you don't have enough information to answer a question, say so honestly."""
        }
        
        logger.info("Conversation Workflow initialized successfully.")
    
    def _should_use_rag(self, user_input: str) -> bool:
        """
        Determine if the user input requires RAG retrieval or can be handled with general conversation.
        This is a simple heuristic - you can make it more sophisticated later.
        """
        # Keywords that suggest the user needs specific NOMAD information
        rag_keywords = [
            'how', 'what', 'where', 'when', 'why', 'explain', 'tutorial', 'guide',
            'nomad', 'upload', 'download', 'api', 'schema', 'metadata', 'entry',
            'dataset', 'archive', 'parser', 'workflow', 'calculation', 'dft',
            'materials', 'structure', 'property', 'search', 'filter', 'query'
        ]
        
        user_lower = user_input.lower()
        return any(keyword in user_lower for keyword in rag_keywords)
    
    def _format_response_with_citations(self, answer: str, citations: str) -> str:
        """
        Format the response to include citations in a user-friendly way.
        """
        if citations and citations != "No sources found.":
            return f"{answer}\n\n📚 **Sources:**\n{citations}"
        return answer
    
    def process_user_input(self, user_input: str) -> str:
        """
        Process user input and generate an appropriate response.
        """
        logger.info(f"Processing user input: '{user_input[:50]}...'")
        
        try:
            if self._should_use_rag(user_input):
                # Use RAG for knowledge-based queries
                logger.info("Using RAG engine for knowledge-based query...")
                answer, citations, retrieved_chunks = self.rag_engine.query(user_input, top_k=5)
                response = self._format_response_with_citations(answer, citations)
                
                # Store the interaction in conversation history
                self.conversation_history.append({"role": "user", "content": user_input})
                self.conversation_history.append({"role": "assistant", "content": response})
                
            else:
                # Use general conversation for greetings, thanks, etc.
                logger.info("Using general conversation mode...")
                
                # Build messages for the conversation
                messages = [self.system_message]
                messages.extend(self.conversation_history[-10:])  # Keep last 10 exchanges for context
                messages.append({"role": "user", "content": user_input})
                
                # Generate response using the chat model
                chat_response = self.rag_engine.client.chat.completions.create(
                    model=self.rag_engine.generator_model_name,
                    messages=messages,
                    temperature=0.7  # Slightly higher temperature for more natural conversation
                )
                
                response = chat_response.choices[0].message.content.strip()
                
                # Store the interaction in conversation history
                self.conversation_history.append({"role": "user", "content": user_input})
                self.conversation_history.append({"role": "assistant", "content": response})
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing user input: {e}")
            return "I'm sorry, I encountered an error while processing your request. Please try again."
    
    def get_conversation_summary(self) -> str:
        """
        Get a summary of the current conversation for debugging or logging.
        """
        return f"Conversation has {len(self.conversation_history)} messages."
    
    def clear_conversation_history(self):
        """
        Clear the conversation history (useful for starting fresh).
        """
        self.conversation_history = []
        logger.info("Conversation history cleared.")
    
    def chat_loop(self):
        """
        Interactive chat loop similar to the original chatbot.py but with enhanced capabilities.
        """
        print("🤖 Hello! I'm NOMAD Bot, your assistant for all things related to NOMAD.")
        print("💡 I can help you with:")
        print("   • How to use NOMAD platform features")
        print("   • Materials science data management")
        print("   • Technical questions about NOMAD")
        print("   • General conversation")
        print("\n💬 Type 'exit' to quit, 'clear' to clear conversation history.\n")
        
        while True:
            try:
                user_input = input("You: ").strip()
                
                if user_input.lower() in ['exit', 'quit']:
                    print("👋 Thanks for chatting! Goodbye!")
                    break
                
                if user_input.lower() == 'clear':
                    self.clear_conversation_history()
                    print("🧹 Conversation history cleared!")
                    continue
                
                if not user_input:
                    continue
                
                print("🤖 NOMAD Bot: ", end="", flush=True)
                response = self.process_user_input(user_input)
                print(response)
                print()  # Add a blank line for readability
                
            except KeyboardInterrupt:
                print("\n👋 Chat interrupted. Goodbye!")
                break
            except Exception as e:
                logger.error(f"Error in chat loop: {e}")
                print("❌ Sorry, something went wrong. Please try again.")


def main():
    """
    Main function to run the conversation workflow.
    """
    # Configuration - matches the original chatbot.py settings
    config = {
        'chromadb_host': 'localhost',
        'chromadb_port': 8000,
        'openai_api_key': 'not-needed',
        'openai_base_url': 'http://**************:11434/v1',
        'embedding_url': 'http://**************:11434/api/embed',
        'embedding_model': 'nomic-embed-text',
        'generator_model': 'gpt-oss:20b'
    }
    
    try:
        # Initialize the conversation workflow
        workflow = ConversationWorkflow(**config)
        
        # Start the interactive chat loop
        workflow.chat_loop()
        
    except Exception as e:
        logger.error(f"Failed to start conversation workflow: {e}")
        print(f"❌ Error: {e}")
        print("Please ensure that:")
        print("  • ChromaDB is running on localhost:8000")
        print("  • The local LLM server is running on **************:11434")
        print("  • The 'nomad_knowledge' collection exists in ChromaDB")


if __name__ == "__main__":
    main()
