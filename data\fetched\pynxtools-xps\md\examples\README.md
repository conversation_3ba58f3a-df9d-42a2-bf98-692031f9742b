# Examples for the XPS reader

Here, you can find working examples of XPS-to-NeXus conversion using the latest [NXmpes](https://fairmat-nfdi.github.io/nexus_definitions/classes/contributed_definitions/NXmpes.html#nxmpes) application definition and the latest development version of [pynxtools-xps](https://github.com/FAIRmat-NFDI/pynxtools-xps).

This reader supports converting X-ray photoelectron spectroscopy into a NeXus formatted file. You can find examples for the supported vendors and file formats in the individual subfolders.

## Contact person in FAIRmat for these examples
<PERSON><PERSON>