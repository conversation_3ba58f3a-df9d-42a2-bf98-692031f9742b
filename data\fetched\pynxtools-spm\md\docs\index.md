---
hide: toc
---
<!-- A single sentence that says what the product is, succinctly and memorably -->
<!-- A paragraph of one to three short sentences, that describe what the product does. -->
<!-- A third paragraph of similar length, this time explaining what need the product meets -->
<!-- Finally, a paragraph that describes whom the product is useful for. -->
# STS Reader Documentation
!!! danger "Work in progress"
<!-- # STS Reader Documentation
***Note: Even though the reader name is STS reader, it also supports data from STM experiments. This is the first version of the reader according to the NeXus application definition [NXsts](https://fairmat-nfdi.github.io/nexus_definitions/classes/contributed_definitions/NXsts.html#nxsts) ([GitHub page](https://github.com/FAIRmat-NFDI/nexus_definitions/blob/fairmat/contributed_definitions/NXsts.nxdl.xml)) which is a generic template of concept definitions for STS and STM experiments. Later on, application definitions and readers specific to STM, STS, and AFM will be available. To stay up-to-date, keep visiting this page from time to time. From now onwards, when we mention STS, we are referring to both STM and STS techniques.***

<div markdown="block" class="home-grid"> 
<div markdown="block">

###  Explanation
  
  - [Reader Explanation](explanation/reader-explanation.md)
    - [Supproted File Formats and File Versions](explanation/reader-explanation.md#supported-file-formats-and-file-versions) 
    - [NeXus Application Definition](explanation/reader-explanation.md#nexus-application-definition)
    - [Introduction to Reader Input Files](explanation/reader-explanation.md#introduction-to-reader-input-files)
    - [Useful Functions](explanation/reader-explanation.md#useful-functions)
</div>
<div markdown="block">

### Tutorial

  - [Reader Tutorial](tutorial/reader-tutorial.md)
  - [Nomad Tutorial](tutorial/nomad-tutorial.md)

</div>
<div markdown="block">

### How-To-Guide

  - [How to Use Reader](how-to-guides/how-to-interact-with-reader.md)

</div>
<div markdown="block">

### Reference
  - [Reader in Nomad](reference/reference.md#nomad)
  - [NeXus application definition in Reader](reference/reference.md#nexus)

</div>
</div>


## Project and Community
The reader is the part of project [FAIRmat](https://www.fairmat-nfdi.eu/fairmat) a FAIR data infrastructure for condensed-matter physics and the chemical physics of solids. 

- [FAIRmat project](https://gepris.dfg.de/gepris/projekt/460197019?language=en) which is funded by [NFDI](https://www.nfdi.de/)
- Reach NOMAD via [MATSCI community discourse](https://matsci.org/c/nomad/32)
- Reach reader developers via [GitHub issue tracker](https://github.com/FAIRmat-NFDI/pynxtools-spm/issues)
- Reach pynxtools developers via [GitHub issue tracker](https://github.com/FAIRmat-NFDI/pynxtools/issues)
- Reach the NeXus-FAIRmat community via the [webpage](https://fairmat-nfdi.github.io/nexus_definitions/) or the [GitHub issue tracker](https://fairmat-nfdi.github.io/nexus_definitions/)  -->
