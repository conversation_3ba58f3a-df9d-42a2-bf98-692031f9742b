{"cells": [{"cell_type": "code", "execution_count": null, "id": "7e48e154", "metadata": {}, "outputs": [], "source": ["from nomad_ragbot import generate_response"]}, {"cell_type": "code", "execution_count": null, "id": "d38d853a", "metadata": {}, "outputs": [], "source": ["print(generate_response(\"Why does the sun shine?\"))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}