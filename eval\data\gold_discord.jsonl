{"id": "discord-1", "question": "Has something changed about the way to prepare a spezialized scatterplot in the apps? At the moment there is an error message when I try to specify the name for a quantity in a repeating subsection, w...", "answer": "The error suggests that the wrong quantity name is being parsed from your JMESPath. Can you double check that the JMESPath syntax is valid? Maybe there is an extra whitespace? You may also need to use single quotes: `'hex-1-ene'` instead of `\"hex-1-ene\"` I can confirm, now I managed to get some plots work. The errors are not reproducible, maybe there was a timeout or something similar.", "source": "discord-issues"}
{"id": "discord-2", "question": "could you have a quick look on it, please?", "answer": "You need to open the interactive terminal on your nomad-app container and reset the processing.", "source": "discord-issues"}
{"id": "discord-3", "question": "Is it possible to set a 'defaultDisplayUnit' for an array that was read in from a file?", "answer": "I know I can set a 'defaultDisplayUnit' for quantities for which I define the component with `a_eln = ELNAnnotation(component='NumberEditQuantity', defaultDisplayUnit='hour')`", "source": "discord-issues"}
{"id": "discord-4", "question": "You can try renaming the containers inside the docker compose file perfect. Thanks Ahmed! Next question, the `uv add packages/nomad-parser-plugins-electronic` command does not work. I have added multi...", "answer": "``` uv add packages/nomad-parser-plugins-electronic/ × Failed to build `nomad-measurements @ file:///home/<USER>/nomad-distro-dev/packages/nomad-measurements` ├─▶ Failed to parse entry: `nomad-material-processing` ╰─▶ `nomad-material-processing` is included as a workspace member, but is missing an entry in `tool.uv.sources` (e.g., `nomad-material-processing = { workspace = true }`) ``` This is the same error message for the problem that I was discussing today in the cafe. Though I am still tr...", "source": "discord-issues"}
{"id": "discord-5", "question": "Well, it's not too urgent. Is your fork up to date and one I could also try for testing?", "answer": "I think it should be up to date now: https://github.com/hampusnasstrom/nomad-distro-dev", "source": "discord-issues"}
{"id": "discord-6", "question": "What's the output for ` docker image inspect ghcr.io/paulogithb/nomad-oasis-cemes:main`?", "answer": "Error: No such image: ghcr.io/paulogithb/nomad-oasis-cemes:main", "source": "discord-issues"}
{"id": "discord-7", "question": "Unfortunately I am no longer on site with the users so will take a bit of time to check this but I'll ask the IT admin. For my understanding, is it that it tries to mount these folders in the jupyter ...", "answer": "You can also run the `nomad admin uploads integrity --missing-storage` command in the container to check if there are uploads with missing (or inaccesible) directories.", "source": "discord-issues"}
{"id": "discord-8", "question": "It looks like these were updated a week ago. But the atomistic parsers has several versions above this, e.g., v1.0.3 from 3 weeks ago. Why is this not updating in the requirements and how can I fix th...", "answer": "The toml gives a valid set of distributions, the `requirements.txt` is generated using the `scripts/generate_python_dependencies.sh` script. But if you want a specific version of nomad-parser-plugins-atomistic in the requirements file, you can bump up the version in the toml file and generate the requirements txt file.", "source": "discord-issues"}
{"id": "discord-9", "question": "I have an issue with uploading from my local oasis to central NOMAD. I clicked on the button upload to central nomad, but then I got the following error: \"Processing failed KeycloakAuthenticationError...", "answer": "The error is related to authentication. My first guess would be that the user you logged into with on the Oasis does not exist on our central installation. Could this be the case? Otherwise the issue could very well also be on our side since data migration to the central installation is a fairly experimental feature at the moment. After discussing this internally, we realize that there are indeed problems with our current implementation that need to be sorted out. Could I ask more about your use...", "source": "discord-issues"}
{"id": "discord-10", "question": "Can you share a screenshot of the terminal you are using and the error message?", "answer": "ah you'll have to delete the venv folder and try again", "source": "discord-issues"}
{"id": "discord-11", "question": "The `Use this template` in the [plugin template](https://github.com/FAIRmat-NFDI/nomad-plugin-template) is no longer showing up. This seems to affect other [GitHub templates](https://github.com/orgs/c...", "answer": "I think this will get fixed soon. Forking should work but I worry about how github automatically targets PRs from forked repository to the original repository. It might be worth just running cruft locally and creating a git repo from the folder? Okay, I'll wait and see then. Will fall back to the local approach if it takes too long. Btw, this bug has been up for 14 hrs now", "source": "discord-issues"}
{"id": "discord-12", "question": "to check if it also works for menues or other parts i dont know i would however consider if the formula of one of the processes is the defining one to move this formula to the result section Ok thanks...", "answer": "currently no way to change the default query mode. This is mostly an oversight from our part. Let me create a feature request out of this and we try to get this into release 1.3.15. a lot to all of you for the help and useful discussions", "source": "discord-issues"}
{"id": "discord-13", "question": "```It appears that when the child section is populated, it does not contain the annotations from the parent. Here's a screenshot of a similar situation, where the child section (NIRGain) misses the se...", "answer": "https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2049 it is about the order right? yes this was never inherrited it needs to specified on the lowest level, you can hower change the order of the quantities in the parent class it self, sth like this: ``` quantity3 = Quantity(type=np.float64)", "source": "discord-issues"}
{"id": "discord-14", "question": "what the expierence with nomad updates is. When i am updating plugins reloads are not helping but we have to do strg+shift+R. Is it possible to force a proper reload of nomad also when plugins have ch...", "answer": "There are two potential causes for things not updating. 1. The actual GUI code is only updated when the GUI has changed. There is a hash based on the GUI used as the name for the Javascript file that the browser is loading. Those are basically cached indefinetely. But this should not be a problem, has the GUI code has not changed. 2. The GUI uses a resource called `artifacts.js` and `env.js`. Those are dynamically generated by the nomad app at start-up time. The `artifacts.js` contains all the s...", "source": "discord-issues"}
{"id": "discord-15", "question": "Can you share a zoom link?", "answer": "https://hu-berlin.zoom-x.de/j/?pwd=VUZxOGtCOGo0Vm1WdFczMk1waVk5Zz09", "source": "discord-issues"}
{"id": "discord-16", "question": "nomad_entries_v1 nomad_materials_v1 So the connection to Elasticsearch seems to be working, what could be the problem then? What could I check during the server startup process?", "answer": "The url could possibly be different, in `packages/nomad-FAIR/infrastructure.py` there's a function called `setup_elastic` Can you try printing out the auth and host? ``` def setup_elastic(): \"\"\"Creates connection to elastic search.\"\"\" http_auth = None if config.elastic.username and config.elastic.password: http_auth = (config.elastic.username, config.elastic.password) global elastic_client print(http_auth, '%s:%d' % (config.elastic.host, config.elastic.port)) elastic_client = connections.create_...", "source": "discord-issues"}
{"id": "discord-17", "question": "[:26:20,663: ERROR/MainProcess] Process 'ForkPoolWorker-1' pid:33 exited with 'signal 9 (SIGKILL)' ERROR celery.worker.reques -28T11:26:20 Task handler raised error: WorkerLostError('Worker exited pre...", "answer": "It seems to also be a problem when curling a single upload We don't get the issue when we create the upload in the GUI and use the file dialog to upload the zip file. Running `nomad admin reset-processing` it needed to reset 13 Entry processes in and 14 Upload processes Also it works when curling to the central production nomad", "source": "discord-issues"}
{"id": "discord-18", "question": "If you have updated your nomad-distro version, there might be a discrepancy somewhere. You need to check the logs in your app container to get a better view. do you see any error?", "answer": "There are other options as well, please check documentation on : `https://nomad-lab.eu/prod/v1/docs/reference/cli.html#nomad-admin`", "source": "discord-issues"}
{"id": "discord-19", "question": "What did you have as the original code? The below version?", "answer": "`ruff format .` also \"fixes\" it in the same way as `ruff check --fix` The formatter isn't going to `sort` imports according to this: https://docs.astral.sh/ruff/formatter/#sorting-imports", "source": "discord-issues"}
{"id": "discord-20", "question": "Is it possible that the names `nomad-simulation-parsers` and `nomad-simulations` clash somehow?", "answer": "By the way, I just had to manually install `nptyping` and `toposort` just now, they apparently are missing from the general installation requirements.", "source": "discord-issues"}
{"id": "discord-21", "question": "or is there a way to fix a \"path\", https://nomad-lab.eu/prod/v1/staging/docs/howto/customization/hdf5.html#:~:text=path%20%3D%20%27external.h5%23path/to/data%27, in the definition of the Quantity? Lik...", "answer": "1. `HDF5Reference` points to a specific dataset within a hdf5 filepath you have to pass when parsing / reading.", "source": "discord-issues"}
{"id": "discord-22", "question": "So the right way is python3 merge.py --host localhost?", "answer": "Don't forget the other options from the example, `--db-name nomad_v1` and `--action ..`", "source": "discord-issues"}
{"id": "discord-23", "question": "I'm not an experienced developer — I’m still learning — so it's very likely that I've made some mistakes. When I try to start the server with the command uv run poe setup, after the first two INFO lin...", "answer": "Thank you very much for your help! All the best! 😊 which version of `nomad` is used in your distro dev?", "source": "discord-issues"}
{"id": "discord-24", "question": "Not very hard. Could you give an example of the use case that you have in mind? I would just want to understand the use case better to see if a separate app or parallel dashboards makes more sense. th...", "answer": "Let me know if I can explain better in a meeting I think I get the idea. How would you prefer the switching between the different dashborads to happen, and would swithincg the dashboard affect anything else, e.g. would it toggle on/off some filters? Would you also use this feature if it was available? I would want to get an idea of how relevant this feature is for users. switching the dashboards I would want to leave the applied filters on as selected. I wrote this basic issue with some ideas a ...", "source": "discord-issues"}
{"id": "discord-25", "question": "Is there some reason for this discrepancy? Can we stick with one form?", "answer": "I came across this because I am comparing the `m_proxy_values` to identify identical references, and because of this discrepancy, I get different results before and after the `MetainfoNormalizer` is run.", "source": "discord-issues"}
{"id": "discord-26", "question": "Might be, but hard to tell. Is that the name of the folder in the upload? In any case, I also refer this to I think he knows better if something was off with some nexus related stuff in NOMAD can you ...", "answer": "You can also try this `develop` version [here](https://nomad-lab.eu/prod/v1/develop/gui/search/solarcells) Any HDF5 file we upload fails in the same way We did not have this problem a few weeks ago Are you uploading to the same `upload` or a fresh `upload`?", "source": "discord-issues"}
{"id": "discord-27", "question": "for your answers, but I am still not really sure what I should do. I only have the 3 infrastructure containers (elastic, rabbitmq, mongo) running and don't see anything special in their logs. The rest...", "answer": "You can check the status of the services by running `docker ps`. This will show \"STATUS\" column that you can check.", "source": "discord-issues"}
{"id": "discord-28", "question": "```should return the string of the OBO ref. But the screenshot is cut, I am just guessing that you are using a base class for `data` which contains that link 🙂 > access section definitions via the api...", "answer": "be my individual nomad server. My API-call looks like this````def get_entry_data(id,token): data = requests.get(f'{url}/entries/{id}', headers={'Authorization': f'Bearer {token}'}) data = data.json() return data```` . I hope these are the informationd that you asked for😅 (Im still a beginner in the IT-field) > Im still a beginner in the IT-field No prob, we all gotta start from somewhere So, when you deserialize your JSON response into a Python `dict`, can you access `response['data']['archive']...", "source": "discord-issues"}
{"id": "discord-29", "question": "What do you mean I tried \"on my oasis\", in north jupyter? Yes, North Jupyter. Queries using requests works without any problems Would you expect something, even if the authentication does not work?", "answer": "I cannot reach the oasis from home unfortunately. Its like the domain `nomad.support.ikz-berlin.de` does not exist. I am also off my computer right now. But there should be public uploads This also happens on the example oasis. So I can at least reproduce this. The quick fix for now, put a `required='*'` into your calls, e.g. `ArchiveQuery(required='*')` gives you results. For some reason the default value for required is `{'run': '*'}`. The query is automatically narrowed to entries that can fu...", "source": "discord-issues"}
{"id": "discord-30", "question": "Yes no problem here is my repo : https://github.com/PauloGitHB/nomad-oasis.git did u ever get to see your plugins in your Oasis with some previous version of this Oasis image generation repo?", "answer": "Yes theses plugins worked very well with the previous version and i didn't change anything okay, I'll check it out. Do you see any visible issues there nothing obvious to me. https://github.com/PauloGitHB/nomad-oasis/actions/runs//job/ The plugin gets definitely installed here.", "source": "discord-issues"}
{"id": "discord-31", "question": "nomad parse works in 1.3.4 but for some reason requires mongoengine, and some other packages in version 1.3.10 are you using `nomad parse`? while developing a plugin? Or in an oasis?", "answer": "Do you have some logs with the error message? parse in the context of testing a custom plugin. Please find attached the error message log. Thank you In your pyproject.toml file, do you have `nomad-lab[\"parsing, infrastructure\"]`, I think infrastructure might be missing here The only mention of nomad-lab is in the check for dependencies version. Thanks for you help.", "source": "discord-issues"}
{"id": "discord-32", "question": "Could you check which version of `typing-extensions `you have? Can be done e.g. with `pip list`. The nomad package requires `typing-extensions==4.12.2`, but it could be that e.g. some of the plugins d...", "answer": "Created a MR for this https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2181 Yep, that's `4.4.0` for me. I'll keep that in mind, we might need to do some updating on the `nomad-parser-plugins-atomistic`.", "source": "discord-issues"}
{"id": "discord-33", "question": "Maybe there were some changes affecting nxs entries (I can see my uploads no problem, so this is a particular bug for your type of mainfiles). What happens if you go to the [staging](https://nomad-lab...", "answer": "Might be, but hard to tell. Is that the name of the folder in the upload? In any case, I also refer this to I think he knows better if something was off with some nexus related stuff in NOMAD can you try uploading a new file just to test? H5web works fine for my old uploads as well. It could also be what mentioned with the plus in the name. You can also try this `develop` version [here](https://nomad-lab.eu/prod/v1/develop/gui/search/solarcells)", "source": "discord-issues"}
{"id": "discord-34", "question": "Ahmed! Changed the channel name: questions about nomad-distro-dev the command `uv run poe gen-gui-test-artifacts` does not run in my nomad-distro-dev. Is there a mistake in the readme?", "answer": "how do I rebase my local nomad-FAIR branch? Can I do this in the nomad-distro-dev? Or do I need to open nomad-FAIR folder in an extra VSC window? Ah yeah the readme needs to be updated. that command is deleted, since there are no test artifacts anymore in nomad-FAIR You can use VSCode to make changes specifically to the nomad-FAIR repo this way. Or you can `cd packages/nomad-FAIR` and run `git` commands and it should work for the nomad repo", "source": "discord-issues"}
{"id": "discord-35", "question": "Did you open any issue that I can create a MR?", "answer": "[here](https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2087)", "source": "discord-issues"}
{"id": "discord-36", "question": "I am trying to run local nomad (nomad-lab v1.3.16.dev138+g93fbbea31) with pynxtools (pynxtools v0.10.0.post1.dev12+g0c8c0051) (these are current master/develop branches). I get the following error: `E...", "answer": "Update: fresh installation of nomad seems to work. I am unsure if I did something wrong while updating previous installation, or if there is an issue with nomad This quantity has been removed in recent commits.", "source": "discord-issues"}
{"id": "discord-37", "question": "+ \"pyxem>0.19.1\", ] ``` Shouldn't this be done automatically?", "answer": "Ah, no it's fixed in the toml In your distro, nomad-simulations was pinned to 0.0.1 What version should I go for? `>=0.3.2`", "source": "discord-issues"}
{"id": "discord-38", "question": "Can you try pinning directly to 3.12.8 and see if that works?", "answer": "`uv python pin 3.12.8` followed by `uv run poe setup`", "source": "discord-issues"}
{"id": "discord-39", "question": "1. As of now, is it possible (as admin) to publish a complete entry from an Oasis to central NOMAD using the dedicated button or do we have to go directly in central NOMAD and make upload there ? We h...", "answer": "You are right, we are still developing this and what we currently have is just a basic first version. We still need to learn from users like you what is actually need and wanted from this feature. Instead of giving you a half baked explanation right now, let me work on some documentation that needed to be written anyways. I come back to you shortly. Ok Thanks ! I added some more information to your docs. It is not yet perfect. You can see it already on our develop deployment here: <https://nomad...", "source": "discord-issues"}
{"id": "discord-40", "question": "``` [W :50:14.767 JupyterHub user:876] santapile's server failed to start in 300 seconds, giving up. ```Can you try the develop URL?", "answer": "Replace `staging` with `develop` on it first it said that spawn failed, I pressed relaunch server, not sure what's happening now, it's empty screen seems to work, but", "source": "discord-issues"}
