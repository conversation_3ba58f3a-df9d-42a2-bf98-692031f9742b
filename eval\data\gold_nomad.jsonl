{"question": "What is a NOMAD Oasis?¶?", "gold_answer": "The software that runs NOMAD is Open-Source and can be used independently of the NOMAD central installation at http://nomad-lab.eu . We call any NOMAD installation that is not the central one a NOMAD Oasis.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/explanation/oasis.html#what-is-a-nomad-oasis"], "title": "Federation and Oasis¶", "section": "What is a NOMAD Oasis?¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/explanation/oasis.html#what-is-a-nomad-oasis", "meta": {"method": "heading_q", "score": 0.7, "id": "60a8893c3e1cde00"}}
{"question": "What is a release¶?", "gold_answer": "NOMAD is a public service, a Git repository, a Python package, and a docker image. What exactly is a NOMAD release? It is all of the following: a version tag on the main NOMAD git project , e.g. v1.3.0 a gitlab release based on a tag with potential release notes a version of the nomad-lab Python package released to pypi.org, e.g. nomad-lab==1.3.0 . a docker image tag, e.g. gitlab-registry.mpcdf.mpg.de/nomad-lab/nomad-fair:v1.3.0 the docker image tag stable points to the image with the latest release tag", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/howto/develop/release.html#what-is-a-release"], "title": "How to release a new NOMAD version¶", "section": "What is a release¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/howto/develop/release.html#what-is-a-release", "meta": {"method": "heading_q", "score": 0.7, "id": "20bb31008c2f5c52"}}
{"question": "How to deal with hotfixes¶?", "gold_answer": "This depends on the current develop branch and requires a judgement call. There are two opposing scenarios: The develop branch only contains minor fixes or fix/features that are not likely to effect the released functionality. In this case, a new release with an increased patch version is the right call. The develop branch adds major refactorings and commits that likely effect the released functionality. In this case, a v1.3.0-hotfix branch should be created. After adding commits with the hotfix, the release process can be applied to the hotfix branch in order to create a v1.3.1 release that only contains the hotfixes and not the changes on develop. After the v1.3.1 release, the v1.3.0-hotfix branch is merged back into develop. Hotfix branches should not live longer than a week.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/howto/develop/release.html#how-to-deal-with-hotfixes"], "title": "How to release a new NOMAD version¶", "section": "How to deal with hotfixes¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/howto/develop/release.html#how-to-deal-with-hotfixes", "meta": {"method": "heading_q", "score": 0.7, "id": "ec00ea45f4dfb0dc"}}
{"question": "Why a federated data infrastructure?¶?", "gold_answer": "There are several benefits for using multiple NOMAD installations: Sovereignty : Data is stored and managed locally. This is important for data privacy and security. Resource : Local resources can be used to manage and analyse data. This is important for large data sets. Customization : Local installations can be customized (plugins). This is important to support local workflows and special file formats.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/explanation/oasis.html#why-a-federated-data-infrastructure"], "title": "Federation and Oasis¶", "section": "Why a federated data infrastructure?¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/explanation/oasis.html#why-a-federated-data-infrastructure", "meta": {"method": "heading_q", "score": 0.7, "id": "0b718536fdb39125"}}
{"question": "What does it mean to \"break an entry\"?", "gold_answer": "If every potential section instance that followed a section definition in the old schema still follows the same section definition in the new schema, we can safely replace the schema. If not, we will need to migrate data to implement the logical transition we intended. Otherwise, we will break existing data.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/howto/plugins/schema_packages.html#versioning"], "title": "How to write a schema package¶", "section": "Versioning¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/howto/plugins/schema_packages.html#versioning", "meta": {"method": "inline_q", "score": 0.55, "id": "9c7604c42b15a700"}}
{"question": "Contents of NOMAD's Explore Menu", "gold_answer": "The following explore pages are currently available: Entries : Search entries across all domains. Theory : Focus on calculations and materials data derived from theoretical models. Experiment : Explore data from experimental sources, such as ELNs (Electronic Lab Notebooks) or characterization techniques e.g., EELS (Electron Energy Loss Spectroscopy). Tools : Explore among several AI toolkit notebooks. Use Cases : Search data tailored to specific use cases, such Metal-Organic Frameworks (MOFs).", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/explore.html#navigate-to-nomads-explore-entries-page"], "title": "Exploring Data in NOMAD¶", "section": "Navigate to NOMAD's Explore Entries Page¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/explore.html#navigate-to-nomads-explore-entries-page", "meta": {"method": "faq_block", "score": 0.8, "id": "8ea4d15ab1a6f02c"}}
{"question": "Input fields offered by the built-in schema Substance ELN", "gold_answer": "The built-in schema Substance ELN provides the following fields for input: substance name: Automatically used as the entry name. tags: User selected tags to improve searchability. datetime: Allows entry of a date/time stamp. substance ID: A unique, human-readable ID for the substance. detailed substance description: A free text field for additional information. Additional subsections available in the data subsection include: elemental composition: Define the chemical composition with atomic and mass fractions. pure substance: Specify if the material is a pure substance purchased from an external vendor, with fields like: Substance name IUPAC name Molecular formula CAS number Inchi Key, SMILES, and more. substance identifier: Add identifiers for specific substances.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#create-a-substance-entry"], "title": "Using NOMAD as an Electronic Lab Notebook¶", "section": "Create a Substance Entry¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#create-a-substance-entry", "meta": {"method": "faq_block", "score": 0.8, "id": "5488a45bb11d06f4"}}
{"question": "Input fields offered by the built-in schema Generic Sample ELN", "gold_answer": "The built-in schema Generic Sample ELN provides the following fields for input: name: Automatically used as the entry name. tags: User selected tags to improve searchability. datetime: Allows entry of a date/time stamp. ID: A unique, human-readable ID for the sample. description: A free text field for additional information. Additional subsections available in the data subsection include: elemental composition: Define the chemical composition with atomic and mass fractions. components: Specify the components used to create the sample, including raw materials or system components. sample identifier: Add unique identifiers for the sample.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#create-a-sample-entry"], "title": "Using NOMAD as an Electronic Lab Notebook¶", "section": "Create a Sample Entry¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#create-a-sample-entry", "meta": {"method": "faq_block", "score": 0.8, "id": "1be9266893d512c7"}}
{"question": "Input fields offered by the built-in schema Instrument ELN", "gold_answer": "The built-in schema Instrument ELN provides the following fields for input: name: Automatically used as the entry name. tags: User selected tags to improve searchability. datetime: Allows entry of a date/time stamp. ID: A unique, human-readable ID for the. description: A free text field for additional information. Additional subsections available in the data subsection include: instrument identifiers: Specify the type of instrument and additional metadata, if applicable.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#create-an-instrument-entry"], "title": "Using NOMAD as an Electronic Lab Notebook¶", "section": "Create an Instrument Entry¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#create-an-instrument-entry", "meta": {"method": "faq_block", "score": 0.8, "id": "b866fabec948e1c9"}}
{"question": "Input fields offered by the built-in schema Material Processing ELN", "gold_answer": "The Material Processing ELN schema provides the following fields for input: name: Automatically used as the entry name. starting time and ending time: Allows entry of a date/time stamp for the process duration. tags: User selected tags to improve searchability. ID: A unique, human-readable ID for the process. location: A text field specifying the location where the process took place. description: A free text field for additional information about the process. Additional subsections available in the data subsection include: steps: Define the step-by-step procedure for the material processing. process identifier: Add unique identifiers for the process. instruments: List the instruments used in the process. samples: Specify the samples that are created or used in the process.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#create-a-process-entry"], "title": "Using NOMAD as an Electronic Lab Notebook¶", "section": "Create a Process Entry¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#create-a-process-entry", "meta": {"method": "faq_block", "score": 0.85, "id": "5bd4a64456a8439a"}}
{"question": "Input fields offered by the built-in schema Measurement ELN", "gold_answer": "name: Automatically used as the entry name. starting time Allows entry of a date/time stamp for the measurement. tags: User selected tags to improve searchability. ID: A unique, human-readable ID for the process. location: A text field specifying the location where the process took place. description: A free text field for additional information about the process. Additional subsections available in the data subsection include: steps: Define the step-by-step procedure for the material processing. samples: Specify the samples that are being measured. measurement identifier: Add unique identifiers for the measurement. instruments: List the instruments used in the measurement. results: Provide information about the results of the measurements (text and images).", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#create-a-measurement-entry"], "title": "Using NOMAD as an Electronic Lab Notebook¶", "section": "Create a Measurement Entry¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#create-a-measurement-entry", "meta": {"method": "faq_block", "score": 0.85, "id": "951dfe9e58fa2dc4"}}
{"question": "Input fields offered by the built-in schema Experiment ELN", "gold_answer": "name: Automatically used as the entry name. starting time Allows entry of a date/time stamp for the measurement. tags: User selected tags to improve searchability. ID: A unique, human-readable ID for the process. location: A text field specifying the location where the process took place. description: A free text field for additional information about the process. Additional subsections available in the data subsection include: steps: Define the step-by-step procedure for the material processing. experiment identifiers: Specify the additional metadata for the experiment.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#integrate-your-experiment"], "title": "Using NOMAD as an Electronic Lab Notebook¶", "section": "Integrate Your Experiment¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#integrate-your-experiment", "meta": {"method": "faq_block", "score": 0.85, "id": "1465bcc583f2d877"}}
{"question": "Organizing your ELN upload", "gold_answer": "NOMAD is a file-based system. You can access, organize, and download your files within each upload. You can also create folders to categorize entries into materials, samples, instruments, processes, and results, as well as upload additional documents, such as relevant PDFs. If you plan to organize your entries into separate folders, do so before you reference them to each other. Moving them afterward may break the reference links. You can follow these steps to organize your ELN entries: Navigate to the FILES tab in your upload. This view functions like a file explorer, allowing you to view and manage files. Add new folders and organize them according to your needs. Drag and drop files into the desired folder. A prompt will appear, asking if you want to copy or move the files—choose according to your needs. Once all files are sorted, take a moment to review the structure. Here’s an example of an organized ELN", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#exploring-and-searching-your-eln"], "title": "Using NOMAD as an Electronic Lab Notebook¶", "section": "Exploring and Searching Your ELN¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#exploring-and-searching-your-eln", "meta": {"method": "faq_block", "score": 0.85, "id": "8727b4f8b588ed98"}}
{"question": "Filtering entries in NOMAD", "gold_answer": "NOMAD provides various filters that can be used to efficiently find your ELN entries, but the following two filters are particularlly effective: Filter by built-in schema used to create the entry. For example, ELNInstrument, ELNSubstances, ELNSample, etc. Filter by custom tags, where you assign common tags to related entries for easy grouping. For example, tag all solvents as \"my_solvent\" or all samples as \"my_samples\". Using these filters helps you quickly locate specific entries in your ELN.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#exploring-and-searching-your-eln"], "title": "Using NOMAD as an Electronic Lab Notebook¶", "section": "Exploring and Searching Your ELN¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#exploring-and-searching-your-eln", "meta": {"method": "faq_block", "score": 0.85, "id": "1f8ad14f1f138bb8"}}
{"question": "Create a custom widget for ELN sections and custom tags", "gold_answer": "To create a custom widget for filtering your ELN, follow these steps: Click on the + TERMS button to open the Edit terms widget menu. In the Search quantity field, type eln . A list of available filters will appear. Select results.eln.sections from the list. This will enable filtering based on the built-in ELN sections available in your ELN upload. Write a descriptive title for the custom widget in Title field . Click DONE! The new ELN sections widget now appears at the top of your EXPLORE page and displays ELN entry types along with their corresponding counts. You can now follow the same steps to create a custom widget for filtering by custom tags. In Step 3, instead of selecting results.eln.sections , choose results.eln.tags . This will create a widget that filters your ELN entries based on the custom tags you have assigned. This widget will then appear on your EXPLORE page, allowing you to quickly view and filter entries by their associated tags.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#exploring-and-searching-your-eln"], "title": "Using NOMAD as an Electronic Lab Notebook¶", "section": "Exploring and Searching Your ELN¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/NOMAD_ELN.html#exploring-and-searching-your-eln", "meta": {"method": "faq_block", "score": 0.85, "id": "83484bef5c16ec96"}}
{"question": "Two different views of the upload page", "gold_answer": "At the very top of the upload page, you can toggle between two different views for this page: Overview: This view includes all the tools needed to manage your upload, along with guided steps to start uploading your files to NOMAD. It will also show all the processed files (entries) that you will upload. Files: This view shows all the files included in upload, whether they are raw files or processed files. You can also organize these files into folders as needed.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#create-new-upload"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Create New Upload¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#create-new-upload", "meta": {"method": "faq_block", "score": 0.8, "id": "647f113c03fbe5c9"}}
{"question": "Icons on the upload overview", "gold_answer": "At the top of the OVERVIEW tab, you will find several icons that help you to manage your upload: The name of the upload can be modified by clicking on the pen icon . The other icons are used as follows: Manage members: Allows users to invite collaborators by assigning co-authors and reviewers roles. Download files: Downloads all files present in the upload. Reload: Reloads the uploads page. Reprocess: Triggers the uploaded data to be processed again. API: Displays a GET request url and corresponding JSON response demonstrating how to access the entries of the upload via the NOMAD API and the expected result, respectively. Delete the upload: Deletes the upload permanently.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#create-new-upload"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Create New Upload¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#create-new-upload", "meta": {"method": "faq_block", "score": 0.8, "id": "e46b8f5969675138"}}
{"question": "Components of the upload overview", "gold_answer": "The remainder of the uploads page is divided into five segments, each presenting a step in the uploading and publishing process: 1. Prepare and upload your files: displays the files and folder structure of the upload. You can add a README.md file to the root directory and its contents will be shown above this section 2. Process data: shows the processed data and the generated entries in NOMAD. 3. Edit visibility and access: allows users to make the upload public or share it with specific users before publishing. 4. Edit author metadata: allows users to edit certain metadata fields from all entries recognized in the upload. This includes comments , where you can add as much extra information as you want, references , where you can add a URL to your upload (e.g., an article DOI), and datasets , where you can link the uploaded data to other uploads to define a larger-scale organizational structure (see Group entries into a dataset below.) 5. Publish: lets users publish data with or without an embargo (i.e., a waiting period before the data are publicly visible).", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#create-new-upload"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Create New Upload¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#create-new-upload", "meta": {"method": "faq_block", "score": 0.85, "id": "0df0b95adeea8b02"}}
{"question": "What is the difference between sharing and publishing an Upload?", "gold_answer": "Sharing an upload allows you to grant access to colleagues or collaborators while working on it. This facilitates collaboration on projects or enables progress reviews during the project. You can invite individuals with either read/write access (coauthors) or read-only access (reviewers). Publishing an upload makes it searchable, findable, and accessible on NOMAD for everyone. Once published, the upload becomes immutable, meaning its contents can no longer be modified. You also have the option to publish with an embargo period, temporarily restricting public access until the embargo expires.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#sharing-and-publishing-uploads"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Sharing and Publishing Uploads¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#sharing-and-publishing-uploads", "meta": {"method": "faq_block", "score": 0.85, "id": "2f4bb848fdc2fbc8"}}
{"question": "Share your upload", "gold_answer": "Step 1: Open the Edit upload members window, but clicking on the EDIT UPLOAD MEMBERS button. Step 2: Start typing the name of the NOMAD user you want to share the upload with. A list of matching users will appear—select the correct name from the list. Step 3: Assign a role to the user by selecting either Coauthor or Reviewer from the dropdown menu. - Coauthors can edit and add files to the upload. - Reviewers can only view the contents of the upload (no editing rights). Step 4: Click on submit .", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#sharing-and-publishing-uploads"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Sharing and Publishing Uploads¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#sharing-and-publishing-uploads", "meta": {"method": "faq_block", "score": 0.85, "id": "4968e03cacd18e70"}}
{"question": "Make your upload visible to everyone", "gold_answer": "To make your upload visible to everyone on NOMAD, simply check the box in the Edit visibility and access section, located under the list of your entries. This setting allows all users, including guests without an account, to view the upload even before it is published. You can still modify the upload’s access settings and edit its contents while it remains visible.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#sharing-and-publishing-uploads"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Sharing and Publishing Uploads¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#sharing-and-publishing-uploads", "meta": {"method": "faq_block", "score": 0.8, "id": "d10f5ccbd1d62b32"}}
{"question": "Publish your upload", "gold_answer": "Once an upload is published, it cannot be deleted, and the files and entries cannot be changed Step 1: Select an embargo period (if needed) from the dropdown menu, located in the publish section located at the bottom of the upload page. If you would like to publish immediately, select No embargo . Step 2: Click on the PUBLISH button. Step 3: A prompt for confirmation appears on the screen. Click on PUBLISH. Having an embargo on your data means: Raw files and entries remain hidden, except to you and users you share the data with. Some metadata (e.g., chemical formula, system type, spacegroup) will be public. Embargoed entries can be added to datasets and be assigned DOIs. The embargo can be lifted earlier than the assigned duration by the user. The following image shows an example of an embargoed upload and the option to lift the embargo by clicking the LIFT EMBARGO button.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#sharing-and-publishing-uploads"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Sharing and Publishing Uploads¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#sharing-and-publishing-uploads", "meta": {"method": "faq_block", "score": 0.85, "id": "0aac2e4ddcf452be"}}
{"question": "Different ways and scenarios to upload files to NOMAD", "gold_answer": "In NOMAD, files can be uploaded in different ways, but whether they are processed into Entries or not depends on their format and NOMAD’s built-in parsers. Here’s how it works: Files that are processed by NOMAD These are files that are recognized by NOMAD, meaning that a built-in parser exists for the file you uploaded, the file will be processed according to a data schemas. It means that NOMAD reads the file, extracts data and organizes them based on the data schema which allows for generating visualizations, and analysis automations. Raw files that are processed in NOMAD become Entries This is common for simulation files since they follow standardized structures. Also a variety of experimental data organized in NeXus format .nxs are recognized by NOMAD. Files that are not processed by NOMAD These are files that do not have a built-in parser , meaning NOMAD cannot automatically extract and structure their data. They are still stored in NOMAD as part of your upload, and can be downloaded or shared, but they do not become entries in the database. NOMAD allows you to preview common file formats that are not automatically parsed. This means you can view the contents of the file directly within the platform without having to download it. Examples include: .txt , .csv , .pdf , .png , .jpg .", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#add-files-to-an-upload"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Add Files to an Upload¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#add-files-to-an-upload", "meta": {"method": "faq_block", "score": 0.8, "id": "de3fed3158ed6ff9"}}
{"question": "Uploading images, pdf files, text files, and tabular data", "gold_answer": "Drag and drop Start with uploading the file FAIRmat_graphics.zip . Let's use the drag and drop method as shown in the animation below. When a compressed file is uploaded to NOMAD, it will be extracted automatically and the included files will be added to your upload. Open the file browser dialog Upload the remaining files using the file browser dialog as shown in the animation below.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-miscellaneous-files"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Uploading Miscellaneous Files¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-miscellaneous-files", "meta": {"method": "faq_block", "score": 0.8, "id": "0a8af1a52bf7586f"}}
{"question": "Uploading input and output files .zip of a DFT calculation", "gold_answer": "Uploading the files Start with uploading the file FHI-aims.zip . Let's use the drag and drop method as shown in the animation below. After uploading files, processing is automatically triggered. This involves identifying supported file formats in NOMAD and extracting relevant (meta)data. The exact processing steps depend on the use case. For example, you can learn more about the processing of computational data on this link . Once processing is complete, NOMAD generates an entry page that presents the data in a structured, hierarchical format based on the NOMAD metainfo schema. Opening and exploring your entry To go to the entry page, click on the icon next to the entry. Here you can view the metadata and useful visualization of your data, check the uploaded files, and explore your entry in details. OVERVIEW tab: 1- On the left, core metadata are displayed. 2- On the right, various cards present the available information. The cards you see depend on the properties or data available for that entry. For FHI-aims files, three main cards appear in the overview page: Materials card: Shows key information and visulization of the composition and conventional cell. Electronic properties card: Shows the calculated band structure, density of states, and Brillouin zone. Workflow Graph card: Illustrates the various steps in the calculation, including their inputs and outputs. FILES tab: This tab shows the uploaded files. NOMAD lists all files in the same directory, as they usually belong together. DATA tab: Also known as the \"processed data\" tab, this shows the results of the parsing and normalization process done by NOMAD. NOMAD puts all the data in a unified, hierarchical, and machine-processable format, following the NOMAD metainfo.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-computations-data"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Uploading Computations Data¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-computations-data", "meta": {"method": "faq_block", "score": 0.85, "id": "18bb4b05b66dbad3"}}
{"question": "Uploading experimental data in the .nxs format", "gold_answer": "Uploading the file Start with uploading the file PBTTT_XPS_SPECS.nxs . Let's use the drag and drop method as shown in the animation below. After uploading files, processing is automatically triggered. This involves identifying supported file formats in NOMAD and extracting relevant (meta)data. The exact processing steps depend on the use case. Once processing is complete, NOMAD generates an entry page that presents the data in a structured, hierarchical format based on the NOMAD metainfo schema. Opening and exploring the entry Click on the icon next to an entry navigates you to the respective entry page. Here you can view the metadata and useful visualization of your data, check the uploaded files, and explore your entry in details. OVERVIEW Tab: 1- On the left, core metadata are displayed. 2- On the right, various cards present the available information. The cards you see depend on the properties or data available for that entry. For .nxs files, two main cards appear in the overview page: the data viewer and the materials card. FILES Tab: This tab shows the uploaded files. NOMAD lists all files in the same directory, as they usually belong together. DATA Tab: Also known as the \"processed data\" tab, this shows the results of the parsing and normalization process done by NOMAD. NOMAD puts all the data in a unified, hierarchical, and machine-processable format, following the NOMAD metainfo.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-experimental-data"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Uploading Experimental Data¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-experimental-data", "meta": {"method": "faq_block", "score": 0.85, "id": "cc44105c63745598"}}
{"question": "NexusDataConverter readers and the NeXuS application definitions", "gold_answer": "A Reader is a program designed to interpret and extract data from a specific experimental technique or file format. The reader understands the structure and encoding of the particular data format and provides methods for accessing its contents in a programmatically friendly way. It acts as a bridge between raw experimental data and NOMAD by converting the data into the structured file format according to domain-specific application definitions. A list of available readers can be found here A NeXus application definition provides a structured specification of the terms and metadata required in an .nxs data file for a particular scientific application. These definitions outline the minimum set of terms that must be included in the data file for it to be considered valid according to the NeXus format. A list of NeXuS application definitions developed by FAIRmat can be found here NexusDataConverter uses readers to interpret the raw data files, and then structures them according to the outlines of the application definitions", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-experimental-data"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Uploading Experimental Data¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-experimental-data", "meta": {"method": "faq_block", "score": 0.85, "id": "f0323acde0e1d578"}}
{"question": "Uploading experimental data in the .xml format", "gold_answer": "Step 1: Click on the CREATE FROM SCHEMA button in your upload page. Step 2: In the create new entry from schema window, click on the drop-down menu of the built-in schema, and select NexusDataConverter Step 3: Give a descriptive name for the entry. Step 4: Click on CREATE. This will take you the NexusDataConverter entry page. Step 5: From the reader drop-down menu, choose the appropriate reader for your files. For this exercise select xps . Step 6: From the nxdl drop-down menu, choose the appropriate application definition for your experiment. For this exercise select NXxps Step 7: Upload the raw data file PBTTT_XPS_SPECS_raw.xml . Step 8: Give a descriptive name for the generated .nxs file. Step 9: Click on the save icon to start the conversion process. Check the overview page of your upload. There you will find two newly created entries; one for the NexusDataConverter and one for the generated .nxs file from your raw file. NOMAD still stores your .xml raw file in the upload directory.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-experimental-data"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Uploading Experimental Data¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-experimental-data", "meta": {"method": "faq_block", "score": 0.85, "id": "76c69a14d9368b61"}}
{"question": "Uploading experimental data in the .xml format with additional ELN data", "gold_answer": "Often, raw files generated by instruments from various vendors lack complete metadata and essential details about the experiment. To address this, scientists document all experimental details in an Electronic Lab Notebook (ELN). Combining data from raw files with information from ELNs ensures that experiments are comprehensively described with rich metadata and conform to community standards. NOMAD’s NexusDataConverter allows you to merge experimental raw data files with ELN entries in .yaml format, producing .nxs files that meet community standards and ensure your data are richly-described. The ELN .yaml file can be generated by various ELN software tools or created manually using a text editor. For each supported experimental raw file format, FAIRmat provides a template ELN .yaml file containing all necessary attributes and parameters to complement the raw file’s data. These templates can be found here While these files can be edited with any text editor, we recommend using VS Code for an optimized editing experience. Open the eln_data_xml.yaml file and edit its contents Modify the start_time and end_time of your experiment. Write your information in the users section. Explore the other fields available in the ELN file. Save the file. Upload your data file .xml and your ELN data .yaml using NexusDataConverter Step 1: Click on the CREATE FROM SCHEMA button in your upload page. Step 2: In the create new entry from schema window, click on the drop-down menu of the built-in schema, and select NexusDataConverter Step 3: Give a descriptive name for the entry. Step 4: Click on CREATE. This will take you the NexusDataConverter entry page. Step 5: From the reader drop-down menu, choose the approperiate reader for your files. For this exercise select xps . Step 6: From the nxdl drop-down menu, choose the approperiate application definition for your experiment. For this exercise select NXxps Step 7: Upload the raw data file PBTTT_XPS_SPECS_raw.xml as well as the ELN data file eln_data_xml.yaml . Step 8: Give a descriptive name for the generated .nxs file. Step 9: Click on the save icon to start the conversion process.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-experimental-data"], "title": "Uploading and Publishing Data in NOMAD¶", "section": "Uploading Experimental Data¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/tutorial/upload_publish.html#uploading-experimental-data", "meta": {"method": "faq_block", "score": 0.7, "id": "5004cd1d1e0de2ae"}}
{"question": "What happens to the VASP POTCAR upon upload?", "gold_answer": "For VASP data, NOMAD complies with the licensing of the POTCAR files. In agreement with Georg Kresse , NOMAD extracts the most important information of the POTCAR file and stores them in a stripped version called POTCAR.stripped . The POTCAR files are then automatically removed from the upload, so that you can safely publish your data.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/examples/computational_data/faqs.html#preparing-and-managing-raw-data"], "title": "Frequently Asked Questions¶", "section": "Preparing and Managing Raw Data¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/examples/computational_data/faqs.html#preparing-and-managing-raw-data", "meta": {"method": "faq_block", "score": 0.85, "id": "10d10f069a178581"}}
{"question": "Can I upload large MD trajectories?", "gold_answer": "NOMAD has a file size limit of 30 GB per upload. We additionally advise users to further trim their trajectories for efficient use of the platform tools. In general, it is best to upload a representative set of trajectory frames (depending on the use case), to be findable and understandable to other researchers, and then link the entry to the full raw trajectory within your own (local) storage solution, so that it can be easily accessed upon request. Please see the relevant guides for more information: nomad-simulation-parsers >> Guide to preparing Gromacs trajectories for upload to NOMAD", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/examples/computational_data/faqs.html#preparing-and-managing-raw-data"], "title": "Frequently Asked Questions¶", "section": "Preparing and Managing Raw Data¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/examples/computational_data/faqs.html#preparing-and-managing-raw-data", "meta": {"method": "faq_block", "score": 0.9, "id": "f1fc88a7d9247bc0"}}
{"question": "What do I do if my MD engine is not supported?", "gold_answer": "The most robust approach for integrating your data into NOMAD is via a standardized parser plugin. However, many modern simulation engines that use fully-flexible scriptable input and non-fixed output files challenge or prevent this approach. For these cases, we provide the H5MD-NOMAD specification (i.e., schema and file format) that enables users to self-organize and upload data from any MD software package. See nomad-simulation-parsers > H5MD > About for details.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/examples/computational_data/faqs.html#preparing-and-managing-raw-data"], "title": "Frequently Asked Questions¶", "section": "Preparing and Managing Raw Data¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/examples/computational_data/faqs.html#preparing-and-managing-raw-data", "meta": {"method": "faq_block", "score": 0.85, "id": "d0a5a80c8bc32aa8"}}
{"question": "Adding more workflow metadata", "gold_answer": "You could extend the workflow metadata by adding the metholodogical input parameters. These are stored in the archive with path run[0].method[-1] . The new single_point.archive.yaml will then be: workflow2 : name : DFT SinglePoint inputs : - name : Input system section : '../upload/archive/mainfile/dft.xml#/run/0/system/-1' - name : Input methodology parameters section : '../upload/archive/mainfile/pressure1/dft_p1.xml#/run/0/method/-1' outputs : - name : Output calculation section : '../upload/archive/mainfile/dft.xml#/run/0/calculation/-1' tasks : - m_def : nomad.datamodel.metainfo.workflow.TaskReference task : '../upload/archive/mainfile/dft.xml#/workflow2' name : DFT inputs : - name : Input structure section : '../upload/archive/mainfile/dft.xml#/run/0/system/-1' - name : Input methodology parameters section : '../upload/archive/mainfile/dft.xml#/run/0/method/-1' outputs : - name : Output calculation section : '../upload/archive/mainfile/dft.xml#/run/0/calculation/-1' When uploaded with dft.xml as before, this will generate a similar workflow graph, but with an extra input node.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/howto/customization/workflows.html#simple-workflows-with-supported-tasks"], "title": "How to define custom workflows¶", "section": "Simple workflows with supported tasks¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/howto/customization/workflows.html#simple-workflows-with-supported-tasks", "meta": {"method": "faq_block", "score": 0.8, "id": "095636217f73feb3"}}
{"question": "Installation details", "gold_answer": "Here is more detailed rundown of the installation steps. Previous build is cleaned: rm -rf nomad/app/static/docs rm -rf nomad/app/static/gui rm -rf site We use uv to install the packages. You can install uv using pip install uv . Next we install the nomad package itself (including all extras). The -e option will install NOMAD with symbolic links that allow you to change the code without having to reinstall after each change. The -c flag restricts the installation of dependencies to the versions specified in the provided requirements file, ensuring that only those versions are installed. uv pip install -e . [ parsing,infrastructure,dev ] -c requirements-dev.txt Install \"default\" plugins. TODO: This can be removed once we have proper proper distribution uv pip install -r requirements-plugins.txt Next we build the documentation. sh scripts/generate_docs_artifacts.sh mkdocs build mkdir -p nomad/app/static/docs cp -r site/* nomad/app/static/docs The NOMAD GUI requires a static .env file, which can be generated with: python -m nomad.cli dev gui-env > gui/.env.development This file includes some of the server details needed so that the GUI can make the initial connection properly. If, for example, you change the server address in your NOMAD configuration file, it will be necessary to regenerate this .env file. In production this file will be overridden.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/howto/develop/setup.html#install-nomad"], "title": "How to get started in development¶", "section": "Install NOMAD¶", "source_url": "https://fairmat-nfdi.github.io/nomad-docs/howto/develop/setup.html#install-nomad", "meta": {"method": "faq_block", "score": 0.9, "id": "c2c2229918fe96b9"}}
