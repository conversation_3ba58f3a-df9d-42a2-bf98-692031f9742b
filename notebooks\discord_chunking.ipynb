{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1819f65b-5039-4a03-807b-a219a504c46e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(303,\n", " ['NOMAD - issues - \"This entry does not exist.\" upon publishing [1275052785494392914].json',\n", "  'NOMAD - issues - # not escaped in API calls [1301134820063313992].json',\n", "  'NOMAD - issues - .hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad [1374638026356949032].json',\n", "  'NOMAD - issues - 1.3.14? [1338856302100873267].json',\n", "  'NOMAD - issues - 2D array of strings [1250003438503333938].json'])"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from pathlib import Path\n", "from datetime import datetime, timezone\n", "import re, json, math\n", "import pandas as pd\n", "from collections import Counter\n", "\n", "RAW_DIR = Path(\"/home/<USER>/work/Discord/Analytics/nomad_discord_data_2025-09-08_15-46-23/issues\")   # the raw JSON threads by joe\n", "OUT_DIR = Path(\"/home/<USER>/work/Discord/nomad-bot-rag-docs-discord/external/discord\")\n", "OUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "def slugify(s: str) -> str:\n", "    s = (s or \"\").strip().lower()\n", "    s = re.sub(r\"[^a-z0-9]+\", \"-\", s)\n", "    s = re.sub(r\"-+\", \"-\", s).strip(\"-\")\n", "    return s or \"untitled\"\n", "\n", "def to_utc_iso(ts: str) -> str:\n", "    if not ts:\n", "        return \"\"\n", "    try:\n", "        dt = datetime.fromisoformat(ts)\n", "        return dt.astimezone(timezone.utc).replace(tzinfo=timezone.utc).isoformat().replace(\"+00:00\", \"Z\")\n", "    except Exception:\n", "        return ts\n", "\n", "json_files = sorted(RAW_DIR.glob(\"*.json\"))\n", "len(json_files), [p.name for p in json_files[:5]]\n"]}, {"cell_type": "code", "execution_count": null, "id": "5aa10629", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "# --- Keep code blocks intact while cleaning surrounding text ---\n", "CODEBLOCK_RE = re.compile(r\"```[\\s\\S]*?```\", re.M)\n", "\n", "# Lines that are *only* a sign-off/ack\n", "SIGNOFF_LINE_RE = re.compile(\n", "    r\"^\\s*(thanks|thank you|many thanks|cheers|best(?: regards)?|kind regards|br|rgds|thx)[\\.,!]*\\s*$\",\n", "    re.I,\n", ")\n", "\n", "# Leading courtesy/openers to strip from the *start* of a message\n", "LEADING_FLUFF = [\n", "    r\"(?:hi|hey|hello|dear(?: all)?|good (?:morning|afternoon|evening))[,!.\\s-]*\",\n", "    r\"(?:thanks|thank you|many thanks|thx)[,!.\\s-]*\",\n", "    r\"(?:sorry(?: about that| for the late reply)?)[:,!.\\s-]*\",\n", "    r\"(?:quick question)[:\\-\\s]*\",\n", "    # common “thinking/wondering” preambles\n", "    r\"(?:i was (?:thinking|wondering|trying) about this)[:,\\-\\s]*\",\n", "    r\"(?:i was (?:thinking|wondering|trying))[:,\\-\\s]*\",\n", "]\n", "\n", "LEADING_FLUFF_RE = re.compile(r\"^(?:\" + r\"|\".join(LEADING_FLUFF) + r\")\", re.I)\n", "\n", "# Redact any @mention leftovers\n", "MENTION_RE = re.compile(r\"@\\S+\")\n", "\n", "# Collapse repeated punctuation/whitespace\n", "MULTI_PUNCT_RE = re.compile(r\"([!?.,])\\1{2,}\")   # \"!!!\" -> \"!!\", \"....\" -> \"...\"\n", "WS_RE          = re.compile(r\"\\s+\")\n", "\n", "# (Optional) strip simple ASCII emoji/emoticons\n", "ASCII_EMOJI_RE = re.compile(r\"(:-\\)|:-\\(|;-\\)|:\\)|:\\(|;-\\))\")\n", "\n", "\n", "def trim_conversational_fluff(text: str) -> str:\n", "    \"\"\"\n", "    Make Discord messages read more like documentation snippets:\n", "    - keep code blocks verbatim\n", "    - remove greeting/thanks/apology openers (start-of-message)\n", "    - drop lines that are pure sign-offs/acks\n", "    - redact any @mentions that slipped through\n", "    - collapse excessive punctuation/whitespace\n", "    \"\"\"\n", "    if not text:\n", "        return \"\"\n", "\n", "    # 1) Split out code blocks to avoid touching them\n", "    parts = CODEBLOCK_RE.split(text)  # keeps delimiters if pattern has groups? (no groups -> split removes blocks)\n", "    # Use finditer to preserve blocks exactly:\n", "    rebuilt = []\n", "    last = 0\n", "    for m in CODEBLOCK_RE.finditer(text):\n", "        pre = text[last:m.start()]\n", "        code = text[m.start():m.end()]\n", "        rebuilt.append(_clean_noncode(pre))\n", "        rebuilt.append(code)  # keep code block verbatim\n", "        last = m.end()\n", "    rebuilt.append(_clean_noncode(text[last:]))\n", "\n", "    cleaned = \"\".join(rebuilt).strip()\n", "    return cleaned\n", "\n", "\n", "def _clean_noncode(chunk: str) -> str:\n", "    if not chunk:\n", "        return \"\"\n", "\n", "    # Remove sign-off lines and trim leading fluff on the first substantive line\n", "    lines = [l for l in chunk.splitlines()]\n", "    # drop pure sign-off/ack lines\n", "    lines = [l for l in lines if not SIGNOFF_LINE_RE.match(l)]\n", "\n", "    # Strip leading fluff repeatedly on the first non-empty line\n", "    for i, line in enumerate(lines):\n", "        s = line.strip()\n", "        if not s:\n", "            continue\n", "        # redact any @mentions (after your rewrite_mentions pass)\n", "        s = MENTION_RE.sub(\"[REDACTED_MENTION]\", s)\n", "        # strip common greeting/thanks/apology preambles at *start*\n", "        prev = None\n", "        while prev != s:\n", "            prev = s\n", "            s = LEADING_FLUFF_RE.sub(\"\", s).lstrip()\n", "        # light cleanup\n", "        s = ASCII_EMOJI_RE.sub(\"\", s)\n", "        s = MULTI_PUNCT_RE.sub(r\"\\1\\1\", s)  # cap repeats to length 2\n", "        lines[i] = s\n", "        break  # only the first substantive line gets opener stripping\n", "\n", "    # Collapse whitespace on all lines; keep line breaks to preserve structure\n", "    lines = [WS_RE.sub(\" \", l).strip() for l in lines]\n", "    # remove empty lines that may result\n", "    lines = [l for l in lines if l]\n", "\n", "    return (\"\\n\".join(lines) + (\"\\n\" if chunk.endswith(\"\\n\") else \"\"))\n"]}, {"cell_type": "code", "execution_count": null, "id": "9d5370db-6e75-493c-a171-27993db3d7bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["(303, '\"This entry does not exist.\" upon publishing', 3496)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["def normalize_name(name: str) -> str:\n", "    s = (name or \"\").lstrip(\"@\").strip()\n", "    s = re.sub(r\"\\s+\", \"_\", s)\n", "    s = re.sub(r\"[\\u200b-\\u200f\\u202a-\\u202e]\", \"\", s)\n", "    return s\n", "\n", "def rewrite_mentions(text: str, mentions: list) -> str:\n", "    if not text:\n", "        return \"\"\n", "    out = text\n", "\n", "    # Step 1: replace known mentions with normalized form\n", "    reps = []\n", "    for m in mentions or []:\n", "        nick = m.get(\"nickname\") or m.get(\"name\")\n", "        name = m.get(\"name\")\n", "        cands = []\n", "        if nick:\n", "            cands.append(nick)\n", "        if name and name != nick:\n", "            cands.append(name)\n", "        uniq = sorted(set(cands), key=lambda s: len(s), reverse=True)\n", "        norm = normalize_name(nick or name or \"\")\n", "        if not norm:\n", "            continue\n", "        for cand in uniq:\n", "            reps.append((r\"@\" + re.escape(cand), f\"@{norm}\"))\n", "\n", "    seen, ordered = set(), []\n", "    for pat, repl in reps:\n", "        if pat not in seen:\n", "            seen.add(pat)\n", "            ordered.append((pat, repl))\n", "    for pat, repl in ordered:\n", "        out = re.sub(pat, repl, out)\n", "\n", "    # Step 2: redact *any remaining* @mentions\n", "    out = re.sub(r\"@\\S+\", \"[REDACTED_MENTION]\", out)\n", "\n", "    return out\n", "\n", "\n", "def redact_pii(text: str) -> str:\n", "    # emails\n", "    text = re.sub(r\"[A-Za-z0-9._%+\\-]+@[A-Za-z0-9.\\-]+\\.[A-Za-z]{2,}\", \"[REDACTED_EMAIL]\", text)\n", "    # rough phones\n", "    text = re.sub(r\"\\b(?:\\+?\\d{1,3}[-.\\s]?)?(?:\\(?\\d{2,4}\\)?[-.\\s]?){2,4}\\d{2,4}\\b\", \"[REDACTED_PHONE]\", text)\n", "    return text\n", "\n", "def load_thread_as_text(path: Path):\n", "    data = json.loads(path.read_text(encoding=\"utf-8\"))\n", "    guild = data.get(\"guild\", {}) or {}\n", "    channel = data.get(\"channel\", {}) or {}\n", "    msgs = data.get(\"messages\", []) or []\n", "\n", "    title   = channel.get(\"name\") or path.stem\n", "    section = channel.get(\"category\") or guild.get(\"name\")\n", "    first_ts = None\n", "\n", "    texts = []\n", "    for m in msgs:\n", "        t = (m.get(\"content\") or \"\").strip()\n", "        if not t:\n", "            continue\n", "        t = rewrite_mentions(t, m.get(\"mentions\") or [])\n", "        t = redact_pii(t)\n", "        t = trim_conversational_fluff(t)  # 👈 add this\n", "        if len(t) < 3:\n", "            continue\n", "        texts.append(t)\n", "        if not first_ts:\n", "            first_ts = to_utc_iso(m.get(\"timestamp\"))\n", "\n", "    full_text = \"\\n\".join(texts)\n", "    return {\n", "        \"source\": \"discord\",\n", "        \"title\": title,\n", "        \"section\": section,\n", "        \"text\": full_text,\n", "        \"url\": None,\n", "        \"timestamp\": first_ts or \"\",\n", "    }\n", "\n", "\n", "threads = [load_thread_as_text(p) for p in json_files]\n", "len(threads), threads[0][\"title\"], len(threads[0][\"text\"])\n"]}, {"cell_type": "code", "execution_count": 3, "id": "5308305c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved 303 threads to ../external/discord/stripped/issues.jsonl\n"]}], "source": ["import json, hashlib\n", "from pathlib import Path\n", "\n", "def thread_id(thread: dict) -> str:\n", "    \"\"\"Stable id from title + first timestamp + short hash of text.\"\"\"\n", "    base = f\"{thread.get('title','')}|{thread.get('timestamp','')}\"\n", "    h = hashlib.sha1(thread.get('text','').encode('utf-8')).hexdigest()[:10]\n", "    return f\"{base}|{h}\"\n", "\n", "def save_threads_jsonl(threads: list[dict], out_path: Path):\n", "    out_path.parent.mkdir(parents=True, exist_ok=True)\n", "    # Atomic-ish write\n", "    tmp = out_path.with_suffix(out_path.suffix + \".tmp\")\n", "    with tmp.open(\"w\", encoding=\"utf-8\") as f:\n", "        for th in threads:\n", "            rec = {\n", "                \"id\": thread_id(th),\n", "                \"source\": th.get(\"source\",\"discord\"),\n", "                \"title\": th.get(\"title\",\"\"),\n", "                \"section\": th.get(\"section\",\"\"),\n", "                \"text\": th.get(\"text\",\"\"),\n", "                \"url\": th.get(\"url\"),\n", "                \"timestamp\": th.get(\"timestamp\",\"\"),\n", "                \"char_count\": len(th.get(\"text\",\"\")),\n", "            }\n", "            f.write(json.dumps(rec, ensure_ascii=False) + \"\\n\")\n", "    tmp.replace(out_path)\n", "    return out_path\n", "\n", "# usage\n", "out_jsonl = Path(\"../external/discord/stripped/issues.jsonl\")\n", "save_threads_jsonl(threads, out_jsonl)\n", "print(f\"Saved {len(threads)} threads to {out_jsonl}\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "42392f0e-878e-47d2-a126-2ec688dead0d", "metadata": {}, "outputs": [], "source": ["# def tokenize_words(text: str):\n", "#     return re.findall(r\"[A-Za-zÄÖÜäöüß0-9]+\", text or \"\")\n", "\n", "# def split_sentences(text: str):\n", "#     parts = re.split(r\"(?<=[.!?])\\s+(?=[A-ZÄÖÜ])\", (text or \"\").strip())\n", "#     return [p.strip() for p in parts if p.strip()] or [text.strip()]\n", "\n", "# def chunk_fixed(text: str, max_words: int = 400, overlap: int = 50):\n", "#     toks = tokenize_words(text)\n", "#     if not toks:\n", "#         return []\n", "#     chunks, i, N = [], 0, len(toks)\n", "#     while i < N:\n", "#         j = min(i + max_words, N)\n", "#         chunks.append(\" \".join(toks[i:j]))\n", "#         if j == N:\n", "#             break\n", "#         i = max(0, j - overlap)\n", "#     return chunks\n", "\n", "# def chunk_sentence(text: str, target_words: int = 350):\n", "#     sents = split_sentences(text)\n", "#     chunks, buf, count = [], [], 0\n", "#     for s in sents:\n", "#         n = len(tokenize_words(s))\n", "#         if count + n > target_words and buf:\n", "#             chunks.append(\" \".join(buf)); buf, count = [], 0\n", "#         buf.append(s); count += n\n", "#     if buf:\n", "#         chunks.append(\" \".join(buf))\n", "#     return chunks\n", "\n", "# def bow_vector(text: str) -> Counter:\n", "#     return Counter(tokenize_words((text or \"\").lower()))\n", "\n", "# def cosine_sim(a: Counter, b: Counter) -> float:\n", "#     if not a or not b:\n", "#         return 0.0\n", "#     inter = set(a) & set(b)\n", "#     num = sum(a[t]*b[t] for t in inter)\n", "#     da = math.sqrt(sum(v*v for v in a.values()))\n", "#     db = math.sqrt(sum(v*v for v in b.values()))\n", "#     if da == 0 or db == 0:\n", "#         return 0.0\n", "#     return num / (da * db)\n", "\n", "# def chunk_semantic(text: str, target_words: int = 350, min_sim: float = 0.25):\n", "#     sents = split_sentences(text)\n", "#     if not sents:\n", "#         return []\n", "#     chunks, buf, centroid, count = [], [], Counter(), 0\n", "#     for s in sents:\n", "#         sv = bow_vector(s)\n", "#         sim = cosine_sim(centroid, sv) if centroid else 1.0\n", "#         n = len(tokenize_words(s))\n", "#         if (sim < min_sim and buf) or (count + n > target_words and buf):\n", "#             chunks.append(\" \".join(buf)); buf, centroid, count = [], Counter(), 0\n", "#         buf.append(s); centroid.update(sv); count += n\n", "#     if buf:\n", "#         chunks.append(\" \".join(buf))\n", "#     return chunks\n"]}, {"cell_type": "code", "execution_count": 4, "id": "2a632522-af92-41a2-bf54-baabbb87b1f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(545,\n", " 599,\n", " 3418,\n", " '/home/<USER>/llm-hackathon/data/discord/nomad_discord.fixed.jsonl',\n", " '/home/<USER>/llm-hackathon/data/discord/nomad_discord.sentence.jsonl',\n", " '/home/<USER>/llm-hackathon/data/discord/nomad_discord.semantic.jsonl')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["FIX_MAX, FIX_OVERLAP   = 400, 50\n", "SENT_TARGET            = 350\n", "SEM_TARGET, SEM_MINSIM = 350, 0.25\n", "\n", "out_fixed    = OUT_DIR / \"nomad_discord.fixed.jsonl\"\n", "out_sentence = OUT_DIR / \"nomad_discord.sentence.jsonl\"\n", "out_semantic = OUT_DIR / \"nomad_discord.semantic.jsonl\"\n", "\n", "def write_jsonl(rows, path: Path):\n", "    path.parent.mkdir(parents=True, exist_ok=True)\n", "    with path.open(\"w\", encoding=\"utf-8\") as f:\n", "        for r in rows:\n", "            f.write(json.dumps(r, ensure_ascii=False) + \"\\n\")\n", "\n", "fixed_rows, sentence_rows, semantic_rows = [], [], []\n", "\n", "for thr in threads:\n", "    title   = thr[\"title\"]\n", "    section = thr[\"section\"]\n", "    url     = thr[\"url\"]\n", "    ts      = thr[\"timestamp\"]\n", "    text    = thr[\"text\"]\n", "\n", "    # fixed\n", "    for i, ch in enumerate(chunk_fixed(text, max_words=FIX_MAX, overlap=FIX_OVERLAP)):\n", "        fixed_rows.append({\n", "            \"id\": f\"discord:{slugify(title)}:{slugify(section)}:fixed:{i}\",\n", "            \"source\": \"discord\",\n", "            \"title\": title,\n", "            \"section\": section,\n", "            \"text\": ch,\n", "            \"url\": url,\n", "            \"timestamp\": ts\n", "        })\n", "    # sentence\n", "    for i, ch in enumerate(chunk_sentence(text, target_words=SENT_TARGET)):\n", "        sentence_rows.append({\n", "            \"id\": f\"discord:{slugify(title)}:{slugify(section)}:sentence:{i}\",\n", "            \"source\": \"discord\",\n", "            \"title\": title,\n", "            \"section\": section,\n", "            \"text\": ch,\n", "            \"url\": url,\n", "            \"timestamp\": ts\n", "        })\n", "    # semantic\n", "    for i, ch in enumerate(chunk_semantic(text, target_words=SEM_TARGET, min_sim=SEM_MINSIM)):\n", "        semantic_rows.append({\n", "            \"id\": f\"discord:{slugify(title)}:{slugify(section)}:semantic:{i}\",\n", "            \"source\": \"discord\",\n", "            \"title\": title,\n", "            \"section\": section,\n", "            \"text\": ch,\n", "            \"url\": url,\n", "            \"timestamp\": ts\n", "        })\n", "\n", "write_jsonl(fixed_rows, out_fixed)\n", "write_jsonl(sentence_rows, out_sentence)\n", "write_jsonl(semantic_rows, out_semantic)\n", "\n", "len(fixed_rows), len(sentence_rows), len(semantic_rows), out_fixed.as_posix(), out_sentence.as_posix(), out_semantic.as_posix()\n"]}, {"cell_type": "code", "execution_count": 5, "id": "270c514f-a680-4479-b783-5f039750208f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["nomad_discord.fixed.jsonl lines: 545\n", "nomad_discord.sentence.jsonl lines: 599\n", "nomad_discord.semantic.jsonl lines: 3418\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>title</th>\n", "      <th>section</th>\n", "      <th>chars</th>\n", "      <th>preview</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>discord:this-entry-does-not-exist-upon-publish...</td>\n", "      <td>\"This entry does not exist.\" upon publishing</td>\n", "      <td>issues</td>\n", "      <td>1861</td>\n", "      <td>Hi everyone, here's an issue I'm seeing when p...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>discord:this-entry-does-not-exist-upon-publish...</td>\n", "      <td>\"This entry does not exist.\" upon publishing</td>\n", "      <td>issues</td>\n", "      <td>1621</td>\n", "      <td>If you encounter this for other of your files,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>discord:not-escaped-in-api-calls:issues:senten...</td>\n", "      <td># not escaped in API calls</td>\n", "      <td>issues</td>\n", "      <td>436</td>\n", "      <td>Hey all,\\nI have file names including # and i ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>discord:hdf5-files-own-made-h5-h5iona-for-publ...</td>\n", "      <td>.hdf5 files (own made .h5, .h5iona, ...) for p...</td>\n", "      <td>issues</td>\n", "      <td>1602</td>\n", "      <td>I have to upload all sorts of .hdf5 files (own...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>discord:hdf5-files-own-made-h5-h5iona-for-publ...</td>\n", "      <td>.hdf5 files (own made .h5, .h5iona, ...) for p...</td>\n", "      <td>issues</td>\n", "      <td>2472</td>\n", "      <td>Is there any doc about that?\\nin order to make...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                  id  \\\n", "0  discord:this-entry-does-not-exist-upon-publish...   \n", "1  discord:this-entry-does-not-exist-upon-publish...   \n", "2  discord:not-escaped-in-api-calls:issues:senten...   \n", "3  discord:hdf5-files-own-made-h5-h5iona-for-publ...   \n", "4  discord:hdf5-files-own-made-h5-h5iona-for-publ...   \n", "\n", "                                               title section  chars  \\\n", "0       \"This entry does not exist.\" upon publishing  issues   1861   \n", "1       \"This entry does not exist.\" upon publishing  issues   1621   \n", "2                         # not escaped in API calls  issues    436   \n", "3  .hdf5 files (own made .h5, .h5iona, ...) for p...  issues   1602   \n", "4  .hdf5 files (own made .h5, .h5iona, ...) for p...  issues   2472   \n", "\n", "                                             preview  \n", "0  Hi everyone, here's an issue I'm seeing when p...  \n", "1  If you encounter this for other of your files,...  \n", "2  Hey all,\\nI have file names including # and i ...  \n", "3  I have to upload all sorts of .hdf5 files (own...  \n", "4  Is there any doc about that?\\nin order to make...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# size & a peek\n", "for p in [out_fixed, out_sentence, out_semantic]:\n", "    n = sum(1 for _ in p.open(\"r\", encoding=\"utf-8\"))\n", "    print(p.name, \"lines:\", n)\n", "\n", "# load a few rows\n", "sample = []\n", "with out_sentence.open(\"r\", encoding=\"utf-8\") as f:\n", "    for i, line in enumerate(f):\n", "        if i >= 5: break\n", "        sample.append(json.loads(line))\n", "pd.DataFrame([{\n", "    \"id\": r[\"id\"], \"title\": r[\"title\"], \"section\": r[\"section\"], \"chars\": len(r[\"text\"]), \"preview\": r[\"text\"][:200]\n", "} for r in sample])\n"]}, {"cell_type": "code", "execution_count": null, "id": "3a74845d-c198-43ba-9d9c-cfb030b7787f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON> (nomad-ragbot)", "language": "python", "name": "nomad-ragbot"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}