# Melt Czochralski experiment

#### drag and drop "schema file", "data file", "data set", and "base classes subfolder" into the nomad upload page 

for more info on archive files structure, check out the [README.md](https://github.com/FAIRmat-Experimental/Area_A_application_definitions/blob/main/README.md) file in the root folder of this repo

## schema file (.schema.archive.yaml)
  
a yaml file containing the ELN schema that will be used to structure user's data. 

## data file (.data.archive.yaml)

a yaml file built with the corresponding schema file structure. 

## data set 

files are in preparation

## base classes file

this file is placed in the base_classes folder in the root level of the repo, the user will find a copy of it in her/his folder and have to drag and drop it together to the other files when starting an upload.
