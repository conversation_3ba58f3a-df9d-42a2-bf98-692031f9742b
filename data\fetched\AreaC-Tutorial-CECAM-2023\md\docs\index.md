# Introduction

This tutorial provides an overview of using the NOMAD software, with a focus on support for classical molecular simulations.
NOMAD is a free open-source data management platform for Materials Science which follows the F.A.I.R. (Findable, Accessible, Interoperable, and Reusable) principles [@Wilkinson2016;@goFairWeb], and is developed primarily by the [FAIRmat consortium](https://www.fairmat-nfdi.eu/fairmat).

!!! info "This tutorial was created by the FAIRmat Area C (computation) team:"

    Dr. <PERSON>, [<EMAIL>](mailto:<EMAIL>)
        - contact for electronic structure calculations

    Dr. <PERSON>, [<EMAIL>-berlin](mailto:<EMAIL>-berlin)
        - contact for excited state calculations

    Dr. <PERSON>, [joseph.rud<PERSON><EMAIL>](mailto:joseph.rud<PERSON><PERSON>@physik.hu-berlin.de)
        - general contact, contact for molecular simulations
