---
hide: toc
---

# NOMAD Documentation

<!-- A single sentence that says what the product is, succinctly and memorably -->
NOMAD is a free, and open-source data management platform for materials science, whose goal is to make scientific research data FAIR (findable, accessible, interoperable and reusable).

<!-- A paragraph of one to three short sentences, that describe what the product does. -->
NOMAD provides tools for data management, sharing, and publishing.
The platform lets you structure, explore, and analyze your data and the data of others.

<!-- A third paragraph of similar length, this time explaining what need the product meets -->
NOMAD solves the challenge of using heterogeneous and unfindable data.
<!-- Finally, a paragraph that describes whom the product is useful for. -->
NOMAD is useful for scientists that work with data, for research groups that need to collaborate on data, and for communities that need to build an archive of FAIR research data.

<div markdown="block" class="home-grid">
<div markdown="block">

## Tutorial

A series of tutorials will guide you through the main functionality of NOMAD.

- [Upload and publish your own data](tutorial/upload_publish.md)
- [Use the search interface to identify interesting data](tutorial/explore.md)
- [Use the API to search and access processed data for analysis](tutorial/access_api.md)
- [Create and use custom schemas in NOMAD](tutorial/custom.md)
- [Developing a NOMAD plugin](tutorial/develop_plugin.md)

- [Example data and exercises](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-1/tutorial-1-materials){:target="_blank"}
- [More videos and tutorials on YouTube](https://youtube.com/playlist?list=PLrRaxjvn6FDW-_DzZ4OShfMPcTtnFoynT){:target="_blank"}

</div>
<div markdown="block">

## How-to guides

How-to guides provide step-by-step instructions for a wide range of tasks, with the overarching topics:

- Manage and find data
- Programmatic data access
- NOMAD Oasis — self-hosting
- Plugins
- Customization
- Development

[Open the how-to guides](howto/overview.md){.md-button .nomad-button .nomad-button--card-action}

</div>

<div markdown="block">

## Explanation

The explanation section provides background knowledge on what are
schemas and structured data, how does processing work, the NOMAD architecture, and more.

</div>
<div markdown="block">

## Reference

The reference includes all CLI commands and arguments, all configuration options,
the possible schema annotations and their arguments, and a glossary of used terms.

</div>
</div>

<h2>Project and community</h2>

NOMAD is an open source project that warmly welcomes community projects, contributions, suggestions, fixes and constructive feedback.
NOMAD is developed by FAIRmat, an open NFDI consortium of over 30 partners building a shared
data structure of for materials science together.

- [Get support](https://nomad-lab.eu/nomad-lab/support.html){:target="_blank"}
- [Join our online forum](https://matsci.org/c/nomad/32){:target="_blank"}
- [Contribute](howto/develop/contrib.md)
- [View our roadmap](https://nomad-lab.eu/nomad-lab/features.html){:target="_blank"}
- [Code guidelines](reference/code_guidelines.md)

Thinking about using NOMAD for your next project? Get in touch!
