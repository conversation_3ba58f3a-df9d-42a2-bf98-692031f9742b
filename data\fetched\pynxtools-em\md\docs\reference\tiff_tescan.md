# TESCAN Tagged Image File Format TIFF

The pynxtools-em parser and normalizer reads the following content and maps them on respective NeXus concepts that are defined in the NXem application definition:

| Concept | NeXus/HDF5 |
| --------------- | --------------  |
| Device | :heavy_check_mark: |
| EmissionCurrent | :heavy_check_mark: |
| HV | :heavy_check_mark: |
| Magnification | :heavy_check_mark: |
| PredictedBeamCurrent | :heavy_check_mark: |
| SerialNumber | :heavy_check_mark: |
| SpecimenCurrent | :heavy_check_mark: |
| SpotSize | :heavy_check_mark: |
| StageRotation | :heavy_check_mark: |
| StageTilt | :heavy_check_mark: |
| StageX | :heavy_check_mark: |
| StageY | :heavy_check_mark: |
| StageZ | :heavy_check_mark: |
| StigmatorX | :heavy_check_mark: |
| StigmatorY | :heavy_check_mark: |
| WD | :heavy_check_mark: |
