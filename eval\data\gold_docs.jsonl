{"id": "docs-1", "question": "Input fields offered by the built-in schema Instrument ELN", "answer": "The built-in schema Instrument ELN provides the following fields for input: name: Automatically used as the entry name. tags: User selected tags to improve searchability. datetime: Allows entry of a date/time stamp. ID: A unique, human-readable ID for the. description: A free text field for additional information. Additional subsections available in the data subsection include: instrument identifiers: Specify the type of instrument and additional metadata, if applicable.", "source": "docs-faq"}
{"id": "docs-2", "question": "What you will learn¶?", "answer": "Organize and manage complex research workflows using NOMAD Integrate diverse data sources into a single, reproducible project Track data provenance and metadata Interface with the NOMAD repository programmatically for automation and high-throughput use", "source": "docs-faq"}
{"id": "docs-3", "question": "Input fields offered by the built-in schema Measurement ELN", "answer": "name: Automatically used as the entry name. starting time Allows entry of a date/time stamp for the measurement. tags: User selected tags to improve searchability. ID: A unique, human-readable ID for the process. location: A text field specifying the location where the process took place. description: A free text field for additional information about the process. Additional subsections available in the data subsection include: steps: Define the step-by-step procedure for the material processing...", "source": "docs-faq"}
{"id": "docs-4", "question": "default pagination", "answer": "To avoid performance issues, the server will paginate the results by default. To control the pagination, one can use the pagination field in the request configuration (see below).", "source": "docs-faq"}
{"id": "docs-5", "question": "What does it mean to \"break an entry\"?", "answer": "If every potential section instance that followed a section definition in the old schema still follows the same section definition in the new schema, we can safely replace the schema. If not, we will need to migrate data to implement the logical transition we intended. Otherwise, we will break existing data.", "source": "docs-faq"}
{"id": "docs-6", "question": "Download the example file for this exercise", "answer": "We have prepared a compressed file for this task, which can be downloaded from this link . The file contains multiple NOMAD ELN entries in .json format. These entries have been created using the NOMAD ELN built-in schema, organized into folders, and categorized with custom tags. You can drag and drop this file into a new upload in NOMAD to view its contents.", "source": "docs-faq"}
{"id": "docs-7", "question": "no universality", "answer": "The * wildcard is not universal and only works for homogeneous data. This means it can only be used to represent upload_id , entry_id , dataset_id , etc., for data that follows a fixed schema ( MongoDB ). It won't work in archives, the corresponding metainfo (definitions), and alike.", "source": "docs-faq"}
{"id": "docs-8", "question": "technical details", "answer": "It is categorized as a GraphQL - like API implemented within the REST style framework FastAPI . Because GraphQL requires static, explicitly defined schemas ahead of time while NOMAD supports data with dynamic schema, it cannot be implemented directly using existing GraphQL tools. As a result, there are no GUI tools available unfortunately.", "source": "docs-faq"}
{"id": "docs-9", "question": "a valid example", "answer": "The following is a valid curl command that fetches the archive of a random entry x36WdKPMctUOkjXMyV8oQq2zWcSx . curl -X 'POST' \\ 'https://nomad-lab.eu/prod/v1/api/v1/graph/query' \\ -H 'accept: application/json' \\ -H 'Content-Type: application/json' \\ -d '{ \"entries\":{ \"x36WdKPMctUOkjXMyV8oQq2zWcSx\":{ \"archive\":{ \"m_request\":{ \"directive\":\"plain\" } } } } }'", "source": "docs-faq"}
{"id": "docs-10", "question": "Input fields offered by the built-in schema Substance ELN", "answer": "The built-in schema Substance ELN provides the following fields for input: substance name: Automatically used as the entry name. tags: User selected tags to improve searchability. datetime: Allows entry of a date/time stamp. substance ID: A unique, human-readable ID for the substance. detailed substance description: A free text field for additional information. Additional subsections available in the data subsection include: elemental composition: Define the chemical composition with atomic and ...", "source": "docs-faq"}
{"id": "docs-11", "question": "Different ways and scenarios to upload files to NOMAD", "answer": "In NOMAD, files can be uploaded in different ways, but whether they are processed into Entries or not depends on their format and NOMAD’s built-in parsers. Here’s how it works: Files that are processed by NOMAD These are files that are recognized by NOMAD, meaning that a built-in parser exists for the file you uploaded, the file will be processed according to a data schemas. It means that NOMAD reads the file, extracts data and organizes them based on the data schema which allows for generating ...", "source": "docs-faq"}
{"id": "docs-12", "question": "What is a release¶?", "answer": "NOMAD is a public service, a Git repository, a Python package, and a docker image. What exactly is a NOMAD release? It is all of the following: a version tag on the main NOMAD git project , e.g. v1.3.0 a gitlab release based on a tag with potential release notes a version of the nomad-lab Python package released to pypi.org, e.g. nomad-lab==1.3.0 . a docker image tag, e.g. gitlab-registry.mpcdf.mpg.de/nomad-lab/nomad-fair:v1.3.0 the docker image tag stable points to the image with the latest rel...", "source": "docs-faq"}
{"id": "docs-13", "question": "What is a custom schema¶?", "answer": "An example of custom schema written in YAML language.", "source": "docs-faq"}
{"id": "docs-14", "question": "Share your upload", "answer": "Step 1: Open the Edit upload members window, but clicking on the EDIT UPLOAD MEMBERS button. Step 2: Start typing the name of the NOMAD user you want to share the upload with. A list of matching users will appear—select the correct name from the list. Step 3: Assign a role to the user by selecting either Coauthor or Reviewer from the dropdown menu. - Coauthors can edit and add files to the upload. - Reviewers can only view the contents of the upload (no editing rights). Step 4: Click on submit .", "source": "docs-faq"}
{"id": "docs-15", "question": "data duplication", "answer": "Some fields are both stored in the MongoDB database and the Elasticsearch index. And it is likely that the desired information can be directly fetched from Elasticsearch . In this case, there is no need to navigate to the MongoDB database. However, if the request needs to access archive, it has to use entry -> archive path if starting from search .", "source": "docs-faq"}
{"id": "docs-16", "question": "Task: Create an ELN entry for substances", "answer": "Create an ELN entry in NOMAD for the following substances: Chloroform Glass substrate Use the Substance ELN schema and include as many details as you like (e.g., Substance Name, Datetime, Substance ID, Description).", "source": "docs-faq"}
{"id": "docs-17", "question": "Task: Reference P3HT powder as input for the process", "answer": "For the Process ELN entry created above, make reference to the substance ELN entry P3HT Powder as an input of the process. Tip: Use the workflow2 section of the entry.", "source": "docs-faq"}
{"id": "docs-18", "question": "Input fields offered by the built-in schema Material Processing ELN", "answer": "The Material Processing ELN schema provides the following fields for input: name: Automatically used as the entry name. starting time and ending time: Allows entry of a date/time stamp for the process duration. tags: User selected tags to improve searchability. ID: A unique, human-readable ID for the process. location: A text field specifying the location where the process took place. description: A free text field for additional information about the process. Additional subsections available in...", "source": "docs-faq"}
{"id": "docs-19", "question": "plain directive", "answer": "The plain directive means 'just return the data as it is'. We will introduce other directives later.", "source": "docs-faq"}
{"id": "docs-20", "question": "various formats of references", "answer": "The format of the reference string may vary, depending on whether it is a reference to the same entry or to another entry.", "source": "docs-faq"}
{"id": "docs-21", "question": "workflow_scripts.archive.yaml", "answer": "data : m_def : 'nomad.datamodel.metainfo.eln.ElnFileManager' name : 'Workflow Scripts' description : 'All the scripts run during setup of the MD simulation.' Files : - file : 'Custom_ELN_Entries/workflow_script_1.py' description : 'Creates the simulation box and inserts water molecules.' - file : 'Custom_ELN_Entries/workflow_script_2.py' description : 'Creates the appropriate force field files for the simulation engine.'", "source": "docs-faq"}
{"id": "docs-22", "question": "Adding more workflow metadata", "answer": "You could extend the workflow metadata by adding the metholodogical input parameters. These are stored in the archive with path run[0].method[-1] . The new single_point.archive.yaml will then be: workflow2 : name : DFT SinglePoint inputs : - name : Input system section : '../upload/archive/mainfile/dft.xml#/run/0/system/-1' - name : Input methodology parameters section : '../upload/archive/mainfile/pressure1/dft_p1.xml#/run/0/method/-1' outputs : - name : Output calculation section : '../upload/...", "source": "docs-faq"}
{"id": "docs-23", "question": "Filtering entries in NOMAD", "answer": "NOMAD provides various filters that can be used to efficiently find your ELN entries, but the following two filters are particularlly effective: Filter by built-in schema used to create the entry. For example, ELNInstrument, ELNSubstances, ELNSample, etc. Filter by custom tags, where you assign common tags to related entries for easy grouping. For example, tag all solvents as \"my_solvent\" or all samples as \"my_samples\". Using these filters helps you quickly locate specific entries in your ELN.", "source": "docs-faq"}
{"id": "docs-24", "question": "How to deal with hotfixes¶?", "answer": "This depends on the current develop branch and requires a judgement call. There are two opposing scenarios: The develop branch only contains minor fixes or fix/features that are not likely to effect the released functionality. In this case, a new release with an increased patch version is the right call. The develop branch adds major refactorings and commits that likely effect the released functionality. In this case, a v1.3.0-hotfix branch should be created. After adding commits with the hotfix...", "source": "docs-faq"}
{"id": "docs-25", "question": "Contents of NOMAD's Explore Menu", "answer": "The following explore pages are currently available: Entries : Search entries across all domains. Theory : Focus on calculations and materials data derived from theoretical models. Experiment : Explore data from experimental sources, such as ELNs (Electronic Lab Notebooks) or characterization techniques e.g., EELS (Electron Energy Loss Spectroscopy). Tools : Explore among several AI toolkit notebooks. Use Cases : Search data tailored to specific use cases, such Metal-Organic Frameworks (MOFs).", "source": "docs-faq"}
{"id": "docs-26", "question": "Download the example files for this exercise", "answer": "We have prepared a set of files for this task that can be downloaded from this link . Download the folder to your local machine at you preferred directory. This folder contains the input and output files of a DFT calculation for Iron(III) Oxide using the FHI-aims code. FHI-aims* an all-electron density-functional-theory package that employs numeric atom-centered basis functions. It is designed for accurate and efficient simulations of molecules, clusters, surfaces, and bulk materials across the ...", "source": "docs-faq"}
{"id": "docs-27", "question": "Task: Create an ELN entry for a sample", "answer": "Create an ELN entry in NOMAD for P3HT solution in chloroform. Reference the sample to its components (P3HT powder and chloroform). Use the Generic Sample ELN schema and include as many details as you like (e.g., Short Name, Datetime, ID, Description).", "source": "docs-faq"}
{"id": "docs-28", "question": "What happens to the VASP POTCAR upon upload?", "answer": "For VASP data, NOMAD complies with the licensing of the POTCAR files. In agreement with Georg Kresse , NOMAD extracts the most important information of the POTCAR file and stores them in a stripped version called POTCAR.stripped . The POTCAR files are then automatically removed from the upload, so that you can safely publish your data.", "source": "docs-faq"}
{"id": "docs-29", "question": "complete definition", "answer": "The complete definition of the RequestConfig can be found in nomad/graph/model.py .", "source": "docs-faq"}
{"id": "docs-30", "question": "Input fields offered by the built-in schema Generic Sample ELN", "answer": "The built-in schema Generic Sample ELN provides the following fields for input: name: Automatically used as the entry name. tags: User selected tags to improve searchability. datetime: Allows entry of a date/time stamp. ID: A unique, human-readable ID for the sample. description: A free text field for additional information. Additional subsections available in the data subsection include: elemental composition: Define the chemical composition with atomic and mass fractions. components: Specify t...", "source": "docs-faq"}
