# Welcome to the NOMAD CAMELS Plugin Documentation

This plugin provides NOMAD schemas, parsers and a search app for data coming from [NOMAD CAMELS](https://fau-lap.github.io/NOMAD-CAMELS/). It allows you to automatically read and process your CAMELS measurement files in NOMAD and gives an easy-to-use search app to filter and search through all your measurements.

!!! note "CAMELS File Upload"
    You can either manually upload your CAMELS files to NOMAD or you can login to NOMAD **within** CAMELS and **automatically** upload your measurements to NOMAD.

## Introduction

These are the 

<div markdown="block" class="home-grid">
<!-- <div markdown="block">

### Tutorial

TODO

- [Tutorial](tutorial/tutorial.md)

</div> -->
<div markdown="block">

### How-to guides

How-to guides provide step-by-step instructions for a wide range of tasks, with the overarching topics:

- [Install this plugin](how_to/install_this_plugin.md)
- [Use this plugin](how_to/use_this_plugin.md)
<!-- - [Contribute to this plugin](how_to/contribute_to_this_plugin.md)
- [Contribute to the documentation](how_to/contribute_to_the_documentation.md) -->

</div>

<!-- <div markdown="block">

### Explanation

The explanation [section](explanation/explanation.md) provides background knowledge on this plugin.

</div> -->
<!-- <div markdown="block">

### Reference

The reference [section](reference/references.md) includes all CLI commands and arguments, all configuration options,
the possible schema annotations and their arguments, and a glossary of used terms.

</div> -->
</div>
