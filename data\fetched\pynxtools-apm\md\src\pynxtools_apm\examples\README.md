# Context

The usa_madison_cameca_eln example was developed by FAIRmat. It shows how atom probe users can write some of the metadata
that they may document alongside their experiments as is displayed in the AMETEK/Cameca proprietary software tools IVAS or now
APSuite. The code shows how these metadata can be stored as a textfile and added to the parsing into the NeXus file.
Code that can read parts of legacy binary CamecaRoot files like RHIT, HITS is not shared.
