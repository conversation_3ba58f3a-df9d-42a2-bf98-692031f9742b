---
hide: toc
---

# Documentation for pynxtools-igor

pynx<PERSON>-igor is a free, and open-source data software for converting data stored with Wavemetrics [Igor Pro](https://www.wavemetrics.com/) into the [NeXus](https://www.nexusformat.org/) format. It is implemented as a plugin for [pynxtools](https://github.com/FAIRmat-NFDI/pynxtools) and allows to read, translate, and standardize data from Igor Pro binary waves and packed experiment files. Depending on the domain of data, pynxtools allows to ensure compliance with various NeXus application definitions, e.g. [NXmpes](https://fairmat-nfdi.github.io/nexus_definitions/classes/contributed_definitions/NXmpes.html) for angle-resolved photoemission spectroscopy (ARPES) experiments, or [NXxrd](https://fairmat-nfdi.github.io/nexus_definitions/classes/contributed_definitions/NXxrd.html) for x-ray diffraction experiments.

pynxtools-igor is developed both as a standalone reader and as a tool within [NOMAD](https://nomad-lab.eu/), which is the open-source data management platform for materials science we are developing within [FAIRmat](https://www.fairmat-nfdi.eu/fairmat/).


<div markdown="block" class="home-grid">
<div markdown="block"> 

### Tutorial

A series of tutorials giving you an overview on how to store or convert your XPS data to NeXus compliant files.

- [Installation guide](tutorial/installation.md)
- [Standalone usage and examples](tutorial/standalone.md)

</div>
<div markdown="block">

### How-to guides

- coming soon!

</div>

<div markdown="block">

### Learn

- coming soon!

</div>
<div markdown="block">

### Reference

Here you can learn which options are available for configuring the [igor reader](reference/igor.md)

</div>
</div>

<h2>Project and community</h2>

- [NOMAD code guidelines](https://nomad-lab.eu/prod/v1/staging/docs/reference/code_guidelines.html) 

Any questions or suggestions? [Get in touch!](https://www.fair-di.eu/fairmat/about-fairmat/team-fairmat)

[The work is funded by the Deutsche Forschungsgemeinschaft (DFG, German Research Foundation) - 460197019 (FAIRmat).](https://gepris.dfg.de/gepris/projekt/460197019?language=en)
