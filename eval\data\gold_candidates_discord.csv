id,score,method,title,section,question,proposed_answer,source_url,timestamp,q_line,a_start,a_end,thread_id
Problem with nomad-FAIR package in nomad-distro-dev|2025-07-01T13:16:01.556000Z|54e7621230|q39-a40-41,1.0,discord_thread_heuristic,Problem with nomad-FAIR package in nomad-distro-dev,issues,Can you try with another port then?,You can modify the start command and pass an `--app-port` argument,,2025-07-01T13:16:01.556000Z,39,40,41,Problem with nomad-FAIR package in nomad-distro-dev|2025-07-01T13:16:01.556000Z|54e7621230
Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q68-a69-70,1.0,discord_thread_heuristic,Create multiple entry from parser or new entry in normalizer,issues,Do you know how I can create the precursor entry if it not exist in parser or normalizer other than doing that in `Parser.is_mainfile()`?,I do it like this https://github.com/nomad-hzb/nomad-baseclasses/blob/014e7017e25176193db8e86200979cfdae9b77c3/src/baseclasses/helper/utilities.py#L296 but hampus is working on an alternative. I basicalky Check if a file with the given Name already exists in the Upload and if Not I create,,2025-06-14T07:31:20.113000Z,68,69,70,Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab
Deploy plugin in nomad_distro_template|2025-05-22T14:01:28.017000Z|f8333e7572|q2-a3-4,1.0,discord_thread_heuristic,Deploy plugin in nomad_distro_template,issues,Is the image used in the docker compose file using your repository's image?,This [line](https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/docker-compose.yaml#L95) should point to your repository's image instead of the `fairmat-nfdi/nomad-distro-template:main` one.,,2025-05-22T14:01:28.017000Z,2,3,4,Deploy plugin in nomad_distro_template|2025-05-22T14:01:28.017000Z|f8333e7572
".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad|2025-05-21T06:40:45.023000Z|405ef159e3|q1-a3-4",1.0,discord_thread_heuristic,".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad",issues,Is there a more automated and efficient way to do the above described procedure while testing schemas with the web-ui of central-NOMAD?,You can use the api directly to upload your files easily. All you need is a token and you are all set `http://localhost:8000/fairdi/nomad/latest/api/v1/extensions/docs#/auth/get_token_auth_token_post`.,,2025-05-21T06:40:45.023000Z,1,3,4,".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad|2025-05-21T06:40:45.023000Z|405ef159e3"
".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad|2025-05-21T06:40:45.023000Z|405ef159e3|q9-a10-11",1.0,discord_thread_heuristic,".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad",issues,"is it possible to store the chemical_formula in your example to make this entry searchable with the ""Elements / Formula""-filter int the main GUI? Is there any doc about that?","in order to make the elements searchable through the periodic table from `Elements/Formula`, you would need to use the results section normalizer which could help, but the way you are using the data section, it is not possible to achieve that via the central nomad for now unless we define a new `app` to make the custom quantity searchable",,2025-05-21T06:40:45.023000Z,9,10,11,".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad|2025-05-21T06:40:45.023000Z|405ef159e3"
"Setup error on ""nomad-distro-dev""|2025-01-08T14:54:17.574000Z|58cb8ee208|q15-a16-17",1.0,discord_thread_heuristic,"Setup error on ""nomad-distro-dev""",issues,could you have a look here??,an issue with python3.12 earlier versions. If you run `uv python install 3.12.8` and try again it should work,,2025-01-08T14:54:17.574000Z,15,16,17,"Setup error on ""nomad-distro-dev""|2025-01-08T14:54:17.574000Z|58cb8ee208"
"Setup error on ""nomad-distro-dev""|2025-01-08T14:54:17.574000Z|58cb8ee208|q42-a43-44",1.0,discord_thread_heuristic,"Setup error on ""nomad-distro-dev""",issues,Can you try pinning directly to 3.12.8 and see if that works?,`uv python pin 3.12.8` followed by `uv run poe setup`,,2025-01-08T14:54:17.574000Z,42,43,44,"Setup error on ""nomad-distro-dev""|2025-01-08T14:54:17.574000Z|58cb8ee208"
Dataset DOI link -> DOI NOT FOUND|2024-09-25T14:03:54.522000Z|2c3add8ea0|q14-a15-16,1.0,discord_thread_heuristic,Dataset DOI link -> DOI NOT FOUND,issues,Are there some updates here? is it possible to get a registered doi on an oasis?,Here's another dead DOI link from https://dx.doi.org//NOMAD/-1,,2024-09-25T14:03:54.522000Z,14,15,16,Dataset DOI link -> DOI NOT FOUND|2024-09-25T14:03:54.522000Z|2c3add8ea0
GET request on processing upload corrupts it|2024-08-20T05:49:44.814000Z|ea7351ce1e|q15-a16-17,1.0,discord_thread_heuristic,GET request on processing upload corrupts it,issues,"Could you provide some script+data that I could use as a starting point to reproduce this,?","test API endpoint. I've tried to work out an MWE to share with you. Interestingly, I can't reproduce the issue with my MWE. I can indeed upload an immediately query without corrupting the data. There must be something intricate/subtle that happens with my larger code, and I'm not sure what it is at the moment. Let's close this issue for now, because I can't reproduce it in a simple MWE, and also because I can simply use a short `sleep` in my larger code to alleviate the problem",,2024-08-20T05:49:44.814000Z,15,16,17,GET request on processing upload corrupts it|2024-08-20T05:49:44.814000Z|ea7351ce1e
docker-compose up error with nomad-lab 3.11 setup|2024-08-13T12:18:42.333000Z|98252609bf|q29-a30-31,1.0,discord_thread_heuristic,docker-compose up error with nomad-lab 3.11 setup,issues,Is there somewhere to check these type of dependencies/versions which are not part of the pyproject/requirements setup?,Maybe here: https://nomad-lab.eu/prod/v1/develop/docs/howto/develop/setup.html#install-docker,,2024-08-13T12:18:42.333000Z,29,30,31,docker-compose up error with nomad-lab 3.11 setup|2024-08-13T12:18:42.333000Z|98252609bf
set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851|q3-a4-5,1.0,discord_thread_heuristic,set default display unit for arrays read from file,issues,What can I do if I want to keep the vector view as shown and not convert it into a `NumberEditQuantity`?,"I just realize, that in the [documentation](https://nomad-lab.eu/prod/v1/staging/docs/reference/annotations.html#eln-annotations) it says that the `defaultDisplayUnit` is deprecated.",,2024-08-05T10:01:34.812000Z,3,4,5,set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851
Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c|q6-a7-8,1.0,discord_thread_heuristic,Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis,issues,Is Keycloak 16.1.1 really still supported?,"I assume they have discontinued this repository. The offiicial docs of keycloak are now using a different one. I have not yet tested it, but i assume the images are the same. Try to exchange `jboss/keycloak:16.1.1` with `quay.io/keycloak/keycloak:16.1.1`. Let me know if this works please, so we can also change our documentation.",,2024-03-14T16:47:18.591000Z,6,7,8,Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c
Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c|q18-a20-21,1.0,discord_thread_heuristic,Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis,issues,what can we do to fix?,This is probably due to the Jupyterhub update. What do the logs of the `nomad_oasis_north` container say? `docker logs nomad_oasis_north`. Have a look at the docs and try this: https://nomad-lab.eu/prod/v1/staging/docs/howto/oasis/migrate.html#to-122,,2024-03-14T16:47:18.591000Z,18,20,21,Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c
Problem with nomad-FAIR package in nomad-distro-dev|2025-07-01T13:16:01.556000Z|54e7621230|q2-a4-5,0.99,discord_thread_heuristic,Problem with nomad-FAIR package in nomad-distro-dev,issues,can you share the output of `uv pip list`?,Can you try updating the h5grove library? `uv sync --upgrad-package h5grove`,,2025-07-01T13:16:01.556000Z,2,4,5,Problem with nomad-FAIR package in nomad-distro-dev|2025-07-01T13:16:01.556000Z|54e7621230
How to open the forked nomad-distro-dev rep in VSCode correctly (windows 11)?|2025-06-05T13:00:56.126000Z|40125d32a5|q10-a12-13,0.99,discord_thread_heuristic,How to open the forked nomad-distro-dev rep in VSCode correctly (windows 11)?,issues,Do you have any idea what I might be doing wrong?,this clears up some things! Does Joshua need to somehow sign in to start using the Dev container? I've never used this feature as I work on a Linux machine and run our docker compose file (`nomad-distro-dev/docker-compose.yaml`) directly with docker.,,2025-06-05T13:00:56.126000Z,10,12,13,How to open the forked nomad-distro-dev rep in VSCode correctly (windows 11)?|2025-06-05T13:00:56.126000Z|40125d32a5
Error: mypy==1.0.1|2025-06-03T11:05:49.035000Z|af9d90a504|q6-a8-9,0.99,discord_thread_heuristic,Error: mypy==1.0.1,issues,"are unsatisfiable. ..while, before the uv run poe setup command, the python version was >=1.15 in all the pyproject.toml files. I have also noticed that, upon uv run poe setup the data-time of several files (incuding pyproject.toml ) has been updated. Is it a known issue?",We have updated the setup command to not include the submodule update step anymore: https://github.com/FAIRmat-NFDI/nomad-distro-dev/blob/main/pyproject.toml#L51,,2025-06-03T11:05:49.035000Z,6,8,9,Error: mypy==1.0.1|2025-06-03T11:05:49.035000Z|af9d90a504
Broken properties card|2025-01-29T12:22:46.606000Z|2156e2b13a|q4-a6-7,0.99,discord_thread_heuristic,Broken properties card,issues,do you have a test upload that i can recreate the error locally?,you can use the following published entry to compare the difference in the data type for the quantity in the screenshots: https://nomad-lab.eu/prod/v1/develop/gui/entry/id/ziJhgrU_TNWbOcgs7g1vt1YX5xxL,,2025-01-29T12:22:46.606000Z,4,6,7,Broken properties card|2025-01-29T12:22:46.606000Z|2156e2b13a
Dataset DOI link -> DOI NOT FOUND|2024-09-25T14:03:54.522000Z|2c3add8ea0|q22-a24-25,0.99,discord_thread_heuristic,Dataset DOI link -> DOI NOT FOUND,issues,"What do you mean with ""manually add a doi to a dataset""?",now i have this `/NOMAD/-1` doi also in our oasis: https://nomad-hzb-se.de/nomad-oasis/gui/user/datasets,,2024-09-25T14:03:54.522000Z,22,24,25,Dataset DOI link -> DOI NOT FOUND|2024-09-25T14:03:54.522000Z|2c3add8ea0
Issues with NOMAD-Sintering Tutorial and NOMAD Oasis Docker Configuration|2024-06-10T11:05:16.439000Z|a0001b31a3|q9-a11-12,0.99,discord_thread_heuristic,Issues with NOMAD-Sintering Tutorial and NOMAD Oasis Docker Configuration,issues,How can we determine which plugin to use when reading a file and how do we navigate graphs on NOMAD?,"1. What do you mean by ""reading a file""? Do you mean when you upload a file to NOMAD? What parser gets called is decide by the matching criteria. The simplest criteria is to match on either the file name/ending or the mime type of the file using the `MatchingParser` class. You can find more information about this in the docs here: https://nomad-lab.eu/prod/v1/docs/explanation/processing.html#matching",,2024-06-10T11:05:16.439000Z,9,11,12,Issues with NOMAD-Sintering Tutorial and NOMAD Oasis Docker Configuration|2024-06-10T11:05:16.439000Z|a0001b31a3
questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173|q17-a20-21,0.97,discord_thread_heuristic,questions about nomad-distro-dev,issues,how do I rebase my local nomad-FAIR branch? Can I do this in the nomad-distro-dev? Or do I need to open nomad-FAIR folder in an extra VSC window?,Or you can `cd packages/nomad-FAIR` and run `git` commands and it should work for the nomad repo,,2025-01-16T11:42:39.899000Z,17,20,21,questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173
"Setup error on ""nomad-distro-dev""|2025-01-08T14:54:17.574000Z|58cb8ee208|q86-a89-90",0.97,discord_thread_heuristic,"Setup error on ""nomad-distro-dev""",issues,What's the docker version?,`docker` itself comes with the compose command https://docs.docker.com/reference/cli/docker/compose/,,2025-01-08T14:54:17.574000Z,86,89,90,"Setup error on ""nomad-distro-dev""|2025-01-08T14:54:17.574000Z|58cb8ee208"
2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d|q112-a115-116,0.97,discord_thread_heuristic,2D array of strings,issues,"What is `path-to-a-valid-file`? Also, do you have some configuration to recognize that specific file so that your parser class `MyParser` is being called?","`path-to-a-valid-file` was just here to say that the CLI needs a file that is recognised by the parser. For instance, in case this is replaced by `../../Examples_from_AMP_partners/KUL/4grains2x4x3_compressionY.hdf5` and I have the `mainfile_name_re` argument in the `__init__.py` of the parser set to `'.*/.hdf5'` so that it will recognize the file.",,2024-06-11T08:27:23.680000Z,112,115,116,2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d
Suggestions in Inputfields|2025-09-03T09:15:45.189000Z|2e3ea963c1|q7-a8-9,0.96,discord_thread_heuristic,Suggestions in Inputfields,issues,is there a possibility to enable this for custom quantities?,"Julia is correct. We only collect suggestions for data stored in `results`. Because the custom quantities use a different storage mechanism (which allows better scaling to multiple schemas + base class searches), adding suggestions to them does not work in the same way.",,2025-09-03T09:15:45.189000Z,7,8,9,Suggestions in Inputfields|2025-09-03T09:15:45.189000Z|2e3ea963c1
Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q62-a63-64,0.96,discord_thread_heuristic,Reproducibility indicators bug from afm nexus file,issues,Which links can store the sensor sections and which can not?,The issue to link `cantilever_oscillators` most probably coming from the `NXcomponent`. Let see if this is the same issue for other sensors as well.,,2025-08-11T09:40:29.449000Z,62,63,64,Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1
The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q22-a23-24,0.96,discord_thread_heuristic,The new oasis-template doesn't works,issues,Can you run the script as mentioned here?,This will update the `docker-compose.yml` file,,2024-10-04T13:34:29.120000Z,22,23,24,The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8
set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851|q0-a1-2,0.96,discord_thread_heuristic,set default display unit for arrays read from file,issues,Is it possible to set a 'defaultDisplayUnit' for an array that was read in from a file?,"I know I can set a 'defaultDisplayUnit' for quantities for which I define the component with `a_eln = ELNAnnotation(component='NumberEditQuantity', defaultDisplayUnit='hour')`",,2024-08-05T10:01:34.812000Z,0,1,2,set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851
NOMAD LAB broken?!|2024-06-07T11:12:45.374000Z|dc54c82581|q12-a20-21,0.96,discord_thread_heuristic,NOMAD LAB broken?!,issues,Are you uploading to the same `upload` or a fresh `upload`?,"I have an upload which might have the same problem caused by a nexus parsed entry. The code that handles dynamic search quantities in nexus entries throws an exception when the definition of a quantity is not there anymore. I guess something might have been removed/changed in the latest nexus changes. In my case it falls over a quantity called `energy_calibration`. I try to [provide a fix](<https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/1903>) that simply ignores quantities for missing definitions. Its a bit cumbersome to do this, because I can only test this agains the problematic data on the deployment. I report back, if I know more.",,2024-06-07T11:12:45.374000Z,12,20,21,NOMAD LAB broken?!|2024-06-07T11:12:45.374000Z|dc54c82581
Standard way of writing `m_proxy_value`|2024-04-10T17:08:30.668000Z|169773661f|q5-a6-7,0.96,discord_thread_heuristic,Standard way of writing `m_proxy_value`,issues,Is there some reason for this discrepancy? Can we stick with one form?,"I came across this because I am comparing the `m_proxy_values` to identify identical references, and because of this discrepancy, I get different results before and after the `MetainfoNormalizer` is run.",,2024-04-10T17:08:30.668000Z,5,6,7,Standard way of writing `m_proxy_value`|2024-04-10T17:08:30.668000Z|169773661f
Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3|q13-a17-18,0.95,discord_thread_heuristic,Solve `mongo_user_group` warning in the application startup,issues,Can you show the the result of `docker ps`?,You will probably have to use `--host localhost`.,,2025-07-16T15:57:41.003000Z,13,17,18,Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3
runing nomad dev distro taking ~3 min to load|2024-11-28T16:25:22.079000Z|34153e48cf|q3-a5-7,0.95,discord_thread_heuristic,runing nomad dev distro taking ~3 min to load,issues,Is it faster if you run `uv run nomad admin run appworker`?,"So I tried with `uv run -vv poe start` and this time it took 5 min 😅 I think one solution would be to reduce the number of plugins included. By default, we have included all of the plugins that are part of the central nomad system: https://github.com/FAIRmat-NFDI/nomad-distro-dev/blob/main/pyproject.toml#L10-L27, but you can exclude which ever ones that are not relevant to your work.",,2024-11-28T16:25:22.079000Z,3,5,7,runing nomad dev distro taking ~3 min to load|2024-11-28T16:25:22.079000Z|34153e48cf
Inconsistent Ruff behavior|2024-05-28T12:41:10.993000Z|300b4983a2|q10-a15-17,0.95,discord_thread_heuristic,Inconsistent Ruff behavior,issues,What did you have as the original code? The below version?,"`ruff format .` also ""fixes"" it in the same way as `ruff check --fix` The formatter isn't going to `sort` imports according to this: https://docs.astral.sh/ruff/formatter/#sorting-imports",,2024-05-28T12:41:10.993000Z,10,15,17,Inconsistent Ruff behavior|2024-05-28T12:41:10.993000Z|300b4983a2
Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q62-a64-65,0.94,discord_thread_heuristic,Create multiple entry from parser or new entry in normalizer,issues,is the precursor entry then in a different upload? or are all precursors in one upload shared with everyone?,"you can design the create system in such a way that it only creates the entry if it doesnt exist, since each entry has a file name and if the filename can be derived from the precursor_id then you can just check in that upload if upload already contains such a file. filename could be then `f""{precursor_id}.archive.json""`",,2025-06-14T07:31:20.113000Z,62,64,65,Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab
Uploads stuck in processing phase|2025-04-16T08:48:04.351000Z|d95a1a1177|q5-a7-8,0.94,discord_thread_heuristic,Uploads stuck in processing phase,issues,"If you have updated your nomad-distro version, there might be a discrepancy somewhere. You need to check the logs in your app container to get a better view. do you see any error?","There are other options as well, please check documentation on : `https://nomad-lab.eu/prod/v1/docs/reference/cli.html#nomad-admin`",,2025-04-16T08:48:04.351000Z,5,7,8,Uploads stuck in processing phase|2025-04-16T08:48:04.351000Z|d95a1a1177
Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41|q9-a11-12,0.94,discord_thread_heuristic,Updating distro-dev after pydantic update,issues,What's your uv version?,Yeah I think you'll have to upgrade uv first. If you installed using `curl .. | sh ..` you can use `uv self update`,,2025-02-10T08:34:08.970000Z,9,11,12,Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41
Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99|q28-a30-31,0.94,discord_thread_heuristic,Could not find a version that satisfies the requirement networkx==3.3,issues,"which message of discussion do you was to point out, Nathan?",`sudo dnf install python3.11-devel` for fedora,,2024-08-19T11:26:40.185000Z,28,30,31,Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99
Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c|q2-a5-7,0.93,discord_thread_heuristic,Failing test in plugin,issues,Does anyone have hints on how to solve this problem?,https://github.com/FAIRmat-NFDI/nomad-schema-plugin-nmr/blob/824fa87a6c395ecf77867f6b89d7a44fb90e095b/src/nomad_nmr_schema/schema_packages/schema_package.py#L370 Maybe this is causing the schema not to be loaded properly. Can you try defining the `init` method as:,,2025-04-14T08:48:46.049000Z,2,5,7,Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c
Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99|q11-a16-17,0.93,discord_thread_heuristic,Could not find a version that satisfies the requirement networkx==3.3,issues,"which python are you using, Ahmed?",https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/Dockerfile?ref_type=heads we install all of the same requirements in Docker on python 3.11,,2024-08-19T11:26:40.185000Z,11,16,17,Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99
nomad_oasis_app container unhealthy|2024-05-30T08:08:19.843000Z|df3de3b692|q47-a52-53,0.93,discord_thread_heuristic,nomad_oasis_app container unhealthy,issues,Is it enough to `docker compose pull` afterwards or do I have to re-download the package?,"Thats my fault. His MR, but my commit. I wanted to run nomad from a different directory (e.g. the `infra` in the nomad-gui). That was supposed to fix a probem, but I did not account that `__file__` will change to `../site-packages/..` during install and we do not manifest/copy the example uploads.",,2024-05-30T08:08:19.843000Z,47,52,53,nomad_oasis_app container unhealthy|2024-05-30T08:08:19.843000Z|df3de3b692
Data section of entries in GUI not showing up|2025-09-04T10:36:29.135000Z|0966f48f60|q2-a3-5,0.92,discord_thread_heuristic,Data section of entries in GUI not showing up,issues,can you please take a look?,The mainfile of the entry `*.archive.json` contains the data section Created a MR to revert the change: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2625,,2025-09-04T10:36:29.135000Z,2,3,5,Data section of entries in GUI not showing up|2025-09-04T10:36:29.135000Z|0966f48f60
FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q117-a118-121,0.92,discord_thread_heuristic,FHIaims parsing issues,issues,Are there any specific changes that require a custom GUI that could not be included in the main package?,"I'm not sure I understand the question properly. I have some local nomad-lab gui patches, but they are probably not critical for the work I'm doing right now. I have however some other patches (normalizing, datamodel) which I need and it would be also nice to have an image with the fixes from https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2536 Anyway, this is getting offtopic, from the FHIaims issue I'll open a new thread where I'll try to describe in more detail what I'm trying to achieve here (and my ultimate goals). I can confirm the patches you provided work (I could not test it in the oasis, but it fixes running `nomad parse <outfile>` locally).",,2025-07-24T12:22:12.739000Z,117,118,121,FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93
Changing ELN data in a parsed non-json entry breaks the parsing.|2025-05-19T15:39:38.226000Z|fe69444cc5|q23-a26-27,0.92,discord_thread_heuristic,Changing ELN data in a parsed non-json entry breaks the parsing.,issues,Why should underscores not be allowed in the name?,"When you create a new entry from AGE_sample, since there is no `name` populated by the user yet, the internal normalizer will pick the name from the `entry_name`, replaces all underscores with white spaces and fill the name.",,2025-05-19T15:39:38.226000Z,23,26,27,Changing ELN data in a parsed non-json entry breaks the parsing.|2025-05-19T15:39:38.226000Z|fe69444cc5
"GUI does not show ""repeats"" subsection, visibility and order does not work.|2025-05-15T11:50:54.826000Z|574d77fa31|q12-a15-16",0.92,discord_thread_heuristic,"GUI does not show ""repeats"" subsection, visibility and order does not work.",issues,How can we hide them? has already mentioned that `visible` flag is not doing anything. Is there other otion(s)?,you can use the `eln` annotation on the section definition for now until we have a fix for display:,,2025-05-15T11:50:54.826000Z,12,15,16,"GUI does not show ""repeats"" subsection, visibility and order does not work.|2025-05-15T11:50:54.826000Z|574d77fa31"
Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q139-a140-142,0.92,discord_thread_heuristic,Unexpected error in the research app,issues,Is it sufficient to substitute the first line in the file with the second one to resolve any problem?,"The issue with celsius units was fixed in this MR https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2380. 1.3.16dev170 might have the fix, not sure. Our latest release candidate version 1.3.16rc1 for sure has the fix. Could you tell more about what git repository are you referring to? Is this happening in your copy of `nomad-distro-template?`",,2025-02-28T07:57:57.339000Z,139,140,142,Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b
Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app|2025-08-07T09:10:48.009000Z|5e08ea8db6|q1-a2-3,0.91,discord_thread_heuristic,Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app,issues,"When running the app, only one of the menus is working at a given moment, the other one is present but greyed-out and can not be interacted with. If I only have one of the menus, either of them works. Is there a way to have both simultaneously?",Changed the channel name: Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app,,2025-08-07T09:10:48.009000Z,1,2,3,Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app|2025-08-07T09:10:48.009000Z|5e08ea8db6
Tabular parser row chemical_formula|2025-06-12T09:58:22.006000Z|b6699b3fd8|q25-a26-27,0.91,discord_thread_heuristic,Tabular parser row chemical_formula,issues,do you also get the following errors when accessing the main .yaml file (in my case tabular-parser-row-mode_mod_JS-2_corr_indentation.archive.yaml)?,"you should be able to call the section Sample, it is not protected. If you call it Sample, remember to also change the type in line 65 to '#/Sample'",,2025-06-12T09:58:22.006000Z,25,26,27,Tabular parser row chemical_formula|2025-06-12T09:58:22.006000Z|b6699b3fd8
Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5|q31-a32-33,0.91,discord_thread_heuristic,Protected Oasis - Restrict GUI/API Access only for Login-User,issues,"What would make sense is the option to use your own key_cloak, and restrict access to only logged in users. Is that what you want?","I know the name of the scientists working for the Collaborative Research Centre - that not a problem at all. I also use a local keycloak instance to keep track of them and to provide the access (and to delete if necessary). In the future I will also bind the keycloak with our university ""identity management system"" to use a more official login/access control. (If students or PI leave they will automatically loose the account and cannot access the oasis - and vice versa if a new student arrives).",,2025-02-28T08:48:27.056000Z,31,32,33,Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5
Warnings and Errors while installing NOMAD|2025-02-18T13:24:07.009000Z|22dfbe511e|q1-a2-3,0.91,discord_thread_heuristic,Warnings and Errors while installing NOMAD,issues,How to resolve these?,I'd recommend using nomad-distro-dev to setup a local development environment https://github.com/FAIRmat-NFDI/nomad-distro-dev,,2025-02-18T13:24:07.009000Z,1,2,3,Warnings and Errors while installing NOMAD|2025-02-18T13:24:07.009000Z|22dfbe511e
Unit Registry|2024-12-19T15:34:54.028000Z|c5631a8352|q4-a5-6,0.91,discord_thread_heuristic,Unit Registry,issues,do we have ampere_hour registered in our unit system?,You can also check in the nomad-fair repo the dir nomad/units,,2024-12-19T15:34:54.028000Z,4,5,6,Unit Registry|2024-12-19T15:34:54.028000Z|c5631a8352
Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q58-a59-60,0.91,discord_thread_heuristic,Memory error when I build my container,issues,is this the plugin you want to load?,https://github.com/PauloGitHB/sintering_plugin,,2024-11-15T09:44:36.042000Z,58,59,60,Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d
Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q95-a96-97,0.91,discord_thread_heuristic,Memory error when I build my container,issues,How much RAM do you have available?,From the default plugins listed here: https://github.com/PauloGitHB/nomad-oasis-cemes/blob/main/pyproject.toml#L24-L41,,2024-11-15T09:44:36.042000Z,95,96,97,Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d
"In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c|q2-a3-4",0.91,discord_thread_heuristic,"In and ELN lane, the rendering of API response in json is overlapping with fields.",issues,can you have a look?,Yes. I started working on a fix for the problem where the visual overlap here: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2194,,2024-11-07T15:03:16.142000Z,2,3,4,"In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c"
Clarify Display Docs|2024-11-01T14:15:53.587000Z|3650b87949|q26-a27-28,0.91,discord_thread_heuristic,Clarify Display Docs,issues,Could you please send me your schema?,https://github.com/FAIRmat-NFDI/nomad-perovskite-solar-cells-database/blob/22-extend-ion-schema/src/perovskite_solar_cell_database/composition.py#L227,,2024-11-01T14:15:53.587000Z,26,27,28,Clarify Display Docs|2024-11-01T14:15:53.587000Z|3650b87949
The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q86-a87-88,0.91,discord_thread_heuristic,The new oasis-template doesn't works,issues,Which url are you using to access the website?,http://theIPofmymachine/nomad-oasis/gui/,,2024-10-04T13:34:29.120000Z,86,87,88,The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8
GET request on processing upload corrupts it|2024-08-20T05:49:44.814000Z|ea7351ce1e|q13-a14-15,0.91,discord_thread_heuristic,GET request on processing upload corrupts it,issues,are you using to upload the data originally?,"The data should definitely not be mutated/corrupted by a GET request, so we have to somehow reproduce and fix this. As a workaround, you can keep on using `sleep`.",,2024-08-20T05:49:44.814000Z,13,14,15,GET request on processing upload corrupts it|2024-08-20T05:49:44.814000Z|ea7351ce1e
k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525|q6-a7-8,0.91,discord_thread_heuristic,k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing,issues,"2 - Obviously the k-mesh will be completely wrong for non-uniform grids, but hopefully this should be fringe enough to not be a big concern, but another check that the k-points are uniformly spaced could be not that difficult?","3 - if the k_mesh.grid is provided but not the points, the points will be reconstructed (at least for Gamma-centered mesh) https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/normalizing/method.py?ref_type=heads#L378 `points = np.meshgrid(*[np.linspace(0, 1, n) for n in k_mesh.grid])`. This however will create the full grid, so again will be inconsistent with the parsers which provide the symmetry reduced ones into `points` , so IMO we should save this one into `all_points` and duplicate them into points only in case of P1 symmetry.",,2024-07-12T10:18:27.642000Z,6,7,8,k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525
The Pipeline of the Example Oasis failed.|2024-07-10T08:57:00.118000Z|2ba075a4a9|q1-a2-3,0.91,discord_thread_heuristic,The Pipeline of the Example Oasis failed.,issues,Should I just try to rerun the pipeline or can you see the underlying problem?,https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-demo-oasis-distribution/-/jobs,,2024-07-10T08:57:00.118000Z,1,2,3,The Pipeline of the Example Oasis failed.|2024-07-10T08:57:00.118000Z|2ba075a4a9
Worker fails to start with latest develop|2024-07-10T08:56:51.543000Z|ef9f3ee105|q5-a6-7,0.91,discord_thread_heuristic,Worker fails to start with latest develop,issues,is this likely the same reason the example oasis deployment failed?,"Yes, Markus created an issue for this: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2067",,2024-07-10T08:56:51.543000Z,5,6,7,Worker fails to start with latest develop|2024-07-10T08:56:51.543000Z|ef9f3ee105
HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q63-a69-70,0.91,discord_thread_heuristic,HDF5 in NOMAD,issues,Can you please have also another look at your example at GitHub and the corresponding message above?,I have just tested it on our production deployment `https://nomad-lab.eu/prod/v1/gui/about/information`. Please have a look at the attached video and let me know if you are facing issues still.,,2024-05-27T13:17:35.844000Z,63,69,70,HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57
Referencing another Upload is not allowed|2024-04-02T11:24:50.663000Z|d4a693cc22|q2-a3-4,0.91,discord_thread_heuristic,Referencing another Upload is not allowed,issues,Can I ask you more info on why this happens?,"It is quite hard to say without additional information. Looks like the schema/parser is doing some actions in the `ServerContext` that causes this, not sure if it is a bug or a feature. Maybe has more insight but I think we would need some way to reproduce this.",,2024-04-02T11:24:50.663000Z,2,3,4,Referencing another Upload is not allowed|2024-04-02T11:24:50.663000Z|d4a693cc22
Documentation for elabFTW and Chemotion integration|2024-03-28T09:18:53.204000Z|0e5de407af|q0-a1-2,0.91,discord_thread_heuristic,Documentation for elabFTW and Chemotion integration,issues,Do we have a tutorial or learning page (or any other resources) on how to use elabftw and chemotion integration in NOMAD?,https://nomad-lab.eu/prod/v1/staging/docs/howto/manage/eln.html#elabftw-integration,,2024-03-28T09:18:53.204000Z,0,1,2,Documentation for elabFTW and Chemotion integration|2024-03-28T09:18:53.204000Z|0e5de407af
Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q67-a71-72,0.9,discord_thread_heuristic,Reproducibility indicators bug from afm nexus file,issues,"Is this the same file, that contains the links to the sensors?","Now, I have found what was the problem behind the link. There are no fields are available inside the target group (in this case `height_piezo_sensor`). If you add a field in the target group, then linking to the section works. I would say still it is a bug should be resolved in NOMAD.",,2025-08-11T09:40:29.449000Z,67,71,72,Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1
Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67|q12-a14-16,0.9,discord_thread_heuristic,Adding plugin to `nomad-distro-dev` fails,issues,Can you share some verbose logs?,Ah i think it's because the package is named `nomad-simulation-parsers` bit awkward to have a different name here https://github.com/FAIRmat-NFDI/nomad-parser-plugins-simulation/blob/develop/pyproject.toml#L16C9-L16C33,,2025-06-13T08:52:00.065000Z,12,14,16,Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67
"Referencing PlotSection: Error ""The item ""figures:0"" could not be found.""|2025-04-22T09:46:09.559000Z|702cce7a9c|q3-a54-55",0.9,discord_thread_heuristic,"Referencing PlotSection: Error ""The item ""figures:0"" could not be found.""",issues,Is there any way to adjust the code?,": Now I tried the code with version `1.3.16` and drafted a [docker image](https://github.com/Bondoki/NOMAD_Oasis_template/tree/plottesting) with the code you provided as [plugin](https://github.com/Bondoki/PlotSectionSchema), with the corresponding [images](https://github.com/Bondoki?tab=packages&repo_name=NOMAD_Oasis_template) for testing purpose. Unfortunately, it's still the same behavior: Then I ""create from Schema"" the `OverviewClass`, select the ""plus button"" to create a subsection `Measurement Example` and hit save, firstly the plot is generated, but secondly if I use the ""Overview-tab"" to ""view the data in the archive"", I got the same error `The item ""figures:0"" could not be found.` This behavior seems to be reminiscent to the general schema class. On the other side, if I create only a `Measurement Example` entity and access the data from that overview tab, everything is working.",,2025-04-22T09:46:09.559000Z,3,54,55,"Referencing PlotSection: Error ""The item ""figures:0"" could not be found.""|2025-04-22T09:46:09.559000Z|702cce7a9c"
2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d|q8-a26-27,0.9,discord_thread_heuristic,2D array of strings,issues,Which lead to my question: Is there a way to keep the 2D structure of string arrays ?,"To clarify things, with Python, I access this array, Iload the HDF5 file using `h5py` and I use this ""path"": `file['cell_to']['phase']` (with `cell_to` being the name of the group and `phase` the name of the dataset concerned). I use `silx` (https://www.silx.org/) to visualize the HDF5 and it says that this dataset is of type `compound`, which I don't really know of.",,2024-06-11T08:27:23.680000Z,8,26,27,2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d
The need of strg+shift+R|2024-03-01T13:43:17.859000Z|22dfec3545|q0-a11-12,0.9,discord_thread_heuristic,The need of strg+shift+R,issues,what the expierence with nomad updates is. When i am updating plugins reloads are not helping but we have to do strg+shift+R. Is it possible to force a proper reload of nomad also when plugins have changes? is someone else experiencing that?,"If you open the `artifacts.js` alone (e.g. new tab with ""https://nomad-lab.eu/prod/v1/gui/artifacts.js"") and if you wait for 60 seconds before sending, the network tab should always look like this:",,2024-03-01T13:43:17.859000Z,0,11,12,The need of strg+shift+R|2024-03-01T13:43:17.859000Z|22dfec3545
nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52|q34-a36-37,0.89,discord_thread_heuristic,nomad-oasis can not stablely be started,issues,what should this part be specified?,This part is the metadata for your deployment and I don't think this part is causing your issue: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/config/models/config.py?ref_type=heads#L249-306,,2025-07-17T08:58:19.106000Z,34,36,37,nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52
Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3|q0-a2-3,0.89,discord_thread_heuristic,Solve `mongo_user_group` warning in the application startup,issues,It should be possible to manage the warnings in the startup of the application?,"For the `nomad.utils.structlo` warning, did you try to update `nomad-distro-dev`? [Recently the `pynxtools==0.10.0` pin has been relaxed](https://github.com/FAIRmat-NFDI/nomad-distro-dev/pull/67) and newer version of `pynxtools>=0.10.8` has fixed this warning",,2025-07-16T15:57:41.003000Z,0,2,3,Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3
Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e|q13-a15-16,0.89,discord_thread_heuristic,Where to find available base sections for yaml,issues,What code are you using?,for DFT? RSPt: https://www.uu.se/en/department/physics-and-astronomy/research/materials-theory/code-development,,2025-06-02T09:45:34.629000Z,13,15,16,Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e
NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520|q0-a2-3,0.89,discord_thread_heuristic,NOMAD Oasis Requests Takes a lot of Time from Institute Intranet,issues,"While I am using my local client to test the [oasis image with keycloak](https://nomad-lab.eu/prod/v1/docs/howto/oasis/install.html#running-nomad), even though I do not have internet I can able to access Oasis. When I deploy the same image on server and while trying to access with a lab computer that only has intranet connection, it takes ages for Oasis to initialize on browser. Mostly request to websites like `unpkg.com` seems to take lots of time. I don't exactly understand the difference between these two scenarios, can someone help me?","The only other that comes to mind is this issue that we had: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2065. You should only have this problem if you are running a version < 1.3.4. Could you check your NOMAD version from the GUI information page: `gui/about/information` (scroll to the section ""About this distribution"")",,2024-10-29T16:31:40.296000Z,0,2,3,NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520
SliderEditQuantity in python plugin schema|2024-10-16T06:16:19.077000Z|d38161544c|q7-a9-10,0.89,discord_thread_heuristic,SliderEditQuantity in python plugin schema,issues,can you comment on this?,There is a fix here https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2167 but it has not been merged yet,,2024-10-16T06:16:19.077000Z,7,9,10,SliderEditQuantity in python plugin schema|2024-10-16T06:16:19.077000Z|d38161544c
Re-adding deleted file does not process|2024-09-19T15:51:19.857000Z|8a906fdb95|q13-a15-16,0.89,discord_thread_heuristic,Re-adding deleted file does not process,issues,Does the file which you added has the same binary content and the same filename?,https://fairmat-nfdi.github.io/nexus_definitions/classes/contributed_definitions/NXmicrostructure_score_config.html,,2024-09-19T15:51:19.857000Z,13,15,16,Re-adding deleted file does not process|2024-09-19T15:51:19.857000Z|8a906fdb95
dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db|q8-a10-11,0.89,discord_thread_heuristic,dynamic appearance of app widgets,issues,how difficult is it to adapt the app with parallel dashboards?,"the current catalysis app on the oasis has a lot of widgets on one page. It would be great if we could make it less overwhelming for the user by allowing them to choose the relevant widgets, by e.g. grouping them. The widgets at the moment have several ""themes"" from top to bottom: reaction (name, reactant, product), catalyst properties (elemental comp., preparation method, catalyst type), reaction conditions (temp., gas concentrations, pressure, whsv), reaction results (scatterplots with T vs. Conversion, T vs. Selectivity, 2 plots with Conversion of a specific example reactant vs. Selectivity of a specific product). Especially one of the last 2 plots will be empty if some filter has been set at the top. I think with either parallel dashboards we can keep the whole page less overwhelming or maybe with some ""toggle/hide"" buttons for a group of widgets, so that they only appear if the users chooses so see them.",,2024-09-19T10:21:05.589000Z,8,10,11,dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db
Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99|q3-a5-6,0.89,discord_thread_heuristic,Could not find a version that satisfies the requirement networkx==3.3,issues,Are you on python3.11?,https://pypi.org/project/networkx/ it's there on pypi for >= 3.10 though,,2024-08-19T11:26:40.185000Z,3,5,6,Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99
Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765|q6-a8-9,0.89,discord_thread_heuristic,Problem when `docker compose up -d` an Oasis,issues,Can you share your docker compose file?,Here is the `docker-compose.yaml` file I am using and I am working on Ubuntu 20.04.6 LTS,,2024-08-02T08:23:26.436000Z,6,8,9,Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765
`ArchiveQuery` fetches nothing in Oasis|2024-04-18T13:22:06.338000Z|161631014e|q3-a5-6,0.89,discord_thread_heuristic,`ArchiveQuery` fetches nothing in Oasis,issues,What can be wrong here?,https://nomad.support.ikz-berlin.de/nomad-oasis/gui/user/uploads/upload/id/xO04KmCySVCH6hZusB04zA,,2024-04-18T13:22:06.338000Z,3,5,6,`ArchiveQuery` fetches nothing in Oasis|2024-04-18T13:22:06.338000Z|161631014e
entry_references disappeared|2025-05-23T13:02:27.183000Z|7ddeb9a273|q0-a1-2,0.88,discord_thread_heuristic,entry_references disappeared,issues,"i have the weirdest error, the entry_references section is not there anymore? and with this the queries for referenced entries break, das someone has an idea?",I just checked and `entry_references` are still there in `EntryMetadata`. It is still part of the response object and also you should be able to see it by checking the box `all_defined`. There has been a recent change made here https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/datamodel/datamodel.py?ref_type=heads#L1069,,2025-05-23T13:02:27.183000Z,0,1,2,entry_references disappeared|2025-05-23T13:02:27.183000Z|7ddeb9a273
Parsing Polarizabilities and Dipole Moments|2025-01-23T23:42:28.354000Z|5acd63f9fc|q13-a14-15,0.88,discord_thread_heuristic,Parsing Polarizabilities and Dipole Moments,issues,"I am not sure if the 27 GB .zip file I uploaded is still there after the Time Limit Error. I can not see it, but it may just be not accessible for me?","2. I uploaded a subset with only 50 entries as a test, that contains polarizabilities as Single Point calculations; and Dipole Moments in the Molecular Dynamics runs. The link to the upload entry is: https://nomad-lab.eu/prod/v1/gui/user/uploads/upload/id/czXVoqV6RYOVXJhZXnTgLA",,2025-01-23T23:42:28.354000Z,13,14,15,Parsing Polarizabilities and Dipole Moments|2025-01-23T23:42:28.354000Z|5acd63f9fc
"Creating multiple uploads causes some to get stuck ""Waiting""|2024-08-28T11:24:51.603000Z|7bca1ff866|q32-a33-36",0.88,discord_thread_heuristic,"Creating multiple uploads causes some to get stuck ""Waiting""",issues,"Is it a bug that these uploads get stuck in ""Waiting"" forever?","Yes, I think you could call it a bug. If the worker crashes, there is probably no easy way to somehow know that the status should be reset. Maybe we could run some cleanup job at regular intervals. That would be very good. Because right now it gets stuck and you can't even delete the upload without running the `nomad admin reset-processing`",,2024-08-28T11:24:51.603000Z,32,33,36,"Creating multiple uploads causes some to get stuck ""Waiting""|2024-08-28T11:24:51.603000Z|7bca1ff866"
nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52|q102-a105-106,0.87,discord_thread_heuristic,nomad-oasis can not stablely be started,issues,can I access them inside app? will nomad_oasis_app return a web interface?,https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/configs/nginx_base_conf#L58-L73,,2025-07-17T08:58:19.106000Z,102,105,106,nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52
Trigger unpublished upload reprocessing with the API?|2025-07-10T12:37:04.381000Z|bccb1bfe23|q0-a1-2,0.87,discord_thread_heuristic,Trigger unpublished upload reprocessing with the API?,issues,Is there a way to trigger the reprocessing of an unpublished upload via the API?,`/uploads/{upload_id}/action/process`,,2025-07-10T12:37:04.381000Z,0,1,2,Trigger unpublished upload reprocessing with the API?|2025-07-10T12:37:04.381000Z|bccb1bfe23
Logout button does not logout north|2025-07-05T19:57:47.090000Z|e55802a0dc|q2-a5-6,0.87,discord_thread_heuristic,Logout button does not logout north,issues,Can you change this?,https://w0l1d.medium.com/implementing-global-logout-in-keycloak-a-comprehensive-guide-c6f22d4034bd,,2025-07-05T19:57:47.090000Z,2,5,6,Logout button does not logout north|2025-07-05T19:57:47.090000Z|e55802a0dc
Issues with user management?|2025-04-09T10:21:47.198000Z|befffd7d94|q24-a27-28,0.87,discord_thread_heuristic,Issues with user management?,issues,Could you find any solution to this?,as long as this loads https://nomad-lab.eu/prod/v1/gui/search/entries this might be a different problem,,2025-04-09T10:21:47.198000Z,24,27,28,Issues with user management?|2025-04-09T10:21:47.198000Z|befffd7d94
Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q29-a32-33,0.87,discord_thread_heuristic,Unexpected dependency version on NOMAD develop,issues,> Does this also apply eventually to staging and production when the versions are bumped up?,"We only have the plugins in nomad-FAIR to run the test suite, there's a related issue for this (https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2255). Once those tests are removed, we can remove the `default_plugins.txt` entirely. So the list on nomad-FAIR doesn't affect any deployments.",,2025-01-24T11:39:53.165000Z,29,32,33,Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c
"When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702|q34-a35-37",0.87,discord_thread_heuristic,"When registering a new account, the confirmation email does not arrive",issues,do you know how to behave in these cases when the confirmation email does not arrive?,"I suppose this is about the realm `fairdi_nomad_test`, right? Have you tried resetting your password on the login page? Otherwise I can look up your user in keycloak. Yes I did try a reset and it still asks me for a email. My user should be GiovanniCastorina",,2025-01-16T10:34:58.468000Z,34,35,37,"When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702"
Remove default search apps and views|2025-01-07T17:06:10.938000Z|a0ef036b18|q1-a4-7,0.87,discord_thread_heuristic,Remove default search apps and views,issues,"How can I get rid of these default apps, and clean up the menu of the AllEnties App?",I believe this was fixed here: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2239 But I'm not sure if we have made a release since then. I'll check. Indeed it seems like the latest release (1.3.13) is from November 29th and the fix by Lauri is from December 2nd. There are dev releases from as recently as 5 days ago though that you could use until the 1.1.14 release is available.,,2025-01-07T17:06:10.938000Z,1,4,7,Remove default search apps and views|2025-01-07T17:06:10.938000Z|a0ef036b18
Remove default search apps and views|2025-01-07T17:06:10.938000Z|a0ef036b18|q21-a24-25,0.87,discord_thread_heuristic,Remove default search apps and views,issues,Could you link me your dev-distro repo then?,https://github.com/rettigl/nomad-distro-dev,,2025-01-07T17:06:10.938000Z,21,24,25,Remove default search apps and views|2025-01-07T17:06:10.938000Z|a0ef036b18
Scatterplot: focus on region|2024-11-04T13:23:24.664000Z|9bdeb903e8|q2-a5-6,0.87,discord_thread_heuristic,Scatterplot: focus on region,issues,Can I tests this in the Oasis? Which scatter plot?,"Yes, you can test this in the[Test Oasis](https://nomad-lab.eu/prod/v1/oasis/gui/search/heterogeneouscatalyst?results.properties.catalytic.reaction.reaction_conditions.temperature%5Bgte%5D=%20K&results.properties.catalytic.reaction.reaction_conditions.temperature%5Blte%5D=%20K)",,2024-11-04T13:23:24.664000Z,2,5,6,Scatterplot: focus on region|2024-11-04T13:23:24.664000Z|9bdeb903e8
Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin|2024-10-22T10:58:19.936000Z|41985e2c35|q14-a17-18,0.87,discord_thread_heuristic,Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin,issues,what kind of files do you upload?,https://github.com/nomad-hzb/nomad-hysprint/blob/dde2f9eea9de581ba310d4088ddd673e2d14ea27/src/nomad_hysprint/parsers/__init__.py#L21 this is what is matched,,2024-10-22T10:58:19.936000Z,14,17,18,Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin|2024-10-22T10:58:19.936000Z|41985e2c35
Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7|q36-a39-40,0.87,discord_thread_heuristic,Publishing an entry from Oasis to Central NOMAD,issues,"If not, is there any other way to reuse our existing parser for manually importing data via e.g. the GUI of nomad-central to allow us to publish and initial data set with DOI?","This is a bit manual but I assume that developing a parser means that the data file format is not recognised by core parsers and so they will not be recognised when uploaded (at least, this is my case). With the YAML and JSON, I can then upload my unrecognised files and create the entry with the data contained in the JSON file, which act as a descriptive tag for the unrecognised files. However, when doing so, I need to point to the schema file in the JSON file with the `m_def` metadata to reference the YAML file with `""m_def"": ""../uploads/{id}/raw/my_schema.archive.yaml#SectionName""` (if the schema file is in an other upload, following https://support.hdfgroup.org/documentation/hdf5-docs/advanced_topics/intro_SWMR.html)",,2024-08-29T07:38:20.591000Z,36,39,40,Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7
Cache Pub Chem Queries|2024-04-16T12:43:43.375000Z|9bcd1deaf9|q12-a15-16,0.87,discord_thread_heuristic,Cache Pub Chem Queries,issues,Can you share a zoom link?,https://hu-berlin.zoom-x.de/j/?pwd=VUZxOGtCOGo0Vm1WdFczMk1waVk5Zz09,,2024-04-16T12:43:43.375000Z,12,15,16,Cache Pub Chem Queries|2024-04-16T12:43:43.375000Z|9bcd1deaf9
Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5|q12-a15-16,0.87,discord_thread_heuristic,Accessing Section Definitions via API,issues,"So, when you deserialize your JSON response into a Python `dict`, can you access `response['data']['archive']['data']`?","You can check them out at our [API dashboard](https://nomad-lab.eu/prod/v1/gui/analyze/apis), but from your use case I assume you want [`entries/archive/query`](https://nomad-lab.eu/prod/v1/api/v1/extensions/docs#/entries%2Farchive).",,2024-04-12T07:49:39.151000Z,12,15,16,Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5
Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5|q29-a32-33,0.87,discord_thread_heuristic,Accessing Section Definitions via API,issues,is there a more elegant way then to parse this file?,"import json resp = requests.get(""https://nomad-hzb-ce.de/nomad-oasis/gui/artifacts.js"")",,2024-04-12T07:49:39.151000Z,29,32,33,Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5
NOMAD can't deal with ISO8601 time formats?|2024-03-15T07:54:51.424000Z|7937b7ad2b|q2-a3-4,0.87,discord_thread_heuristic,NOMAD can't deal with ISO8601 time formats?,issues,"Should we change the time stamp to not include ""+"" or is this something that can be fixed?",This is on https://nomad-lab.eu/prod/v1,,2024-03-15T07:54:51.424000Z,2,3,4,NOMAD can't deal with ISO8601 time formats?|2024-03-15T07:54:51.424000Z|7937b7ad2b
NOMAD can't deal with ISO8601 time formats?|2024-03-15T07:54:51.424000Z|7937b7ad2b|q6-a9-10,0.87,discord_thread_heuristic,NOMAD can't deal with ISO8601 time formats?,issues,"Can you clarify what you mean ""can't deal""? You cannot upload the file? Through the UI? Or you can upload, but then it is not picked up for processing? Or its processed, but you cannot open it in the GUI?",This helped to pin poin the issue. There is an url encoding problem in our h5grove app. Here is the github issue (https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1952) and the fix (https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/1749).,,2024-03-15T07:54:51.424000Z,6,9,10,NOMAD can't deal with ISO8601 time formats?|2024-03-15T07:54:51.424000Z|7937b7ad2b
Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q57-a59-62,0.86,discord_thread_heuristic,Reproducibility indicators bug from afm nexus file,issues,Can you please try it?,"Yeah, this is a bug. This is not happening with other SPM family member e.g., STM. In that experiment the `bias_sweep` group of the `reprocibility_indicators` can redirect the reference group properly. But for AFM for some reasons it fails. I will drive deeper why it behaving wired. I've found another possible issue in the scan environment section, where only two sensors are stored by linking. In practice, if I try to store all the three section required by the scan environment section only two of them will be effectively stored. There are a few sensors available to link the sensors exists under the instrument group.",,2025-08-11T09:40:29.449000Z,57,59,62,Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1
Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67|q28-a34-35,0.86,discord_thread_heuristic,Adding plugin to `nomad-distro-dev` fails,issues,Is it possible that the names `nomad-simulation-parsers` and `nomad-simulations` clash somehow?,"By the way, I just had to manually install `nptyping` and `toposort` just now, they apparently are missing from the general installation requirements.",,2025-06-13T08:52:00.065000Z,28,34,35,Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67
VSCode debugger for plugins|2024-04-03T10:00:43.669000Z|63b7730b26|q7-a9-12,0.86,discord_thread_heuristic,VSCode debugger for plugins,issues,Does it work if you pip install the plugin instead?,"Well, I mean the plugin. You mentioned $PYTHONPATH above which I never use. I just pip install the plugin into my venv. Did that first. suggested adding the `$PYTHONPATH` to the venv Okay, then I'm not sure. I haven't run the debugger in a while. Sorry.",,2024-04-03T10:00:43.669000Z,7,9,12,VSCode debugger for plugins|2024-04-03T10:00:43.669000Z|63b7730b26
Suggestions in Inputfields|2025-09-03T09:15:45.189000Z|2e3ea963c1|q0-a1-2,0.85,discord_thread_heuristic,Suggestions in Inputfields,issues,can someone tell me when the Terms WIdgets with Input fields allows suggestions based on already type information?,"I believe this is only possible for those terms defined in the results.py file in the central software, which have an added property `a_elasticsearch =[Elasticsearch(suggestion='default'),]` as e.g. here:",,2025-09-03T09:15:45.189000Z,0,1,2,Suggestions in Inputfields|2025-09-03T09:15:45.189000Z|2e3ea963c1
Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67|q6-a7-8,0.85,discord_thread_heuristic,Adding plugin to `nomad-distro-dev` fails,issues,Is it in the same `packages/` subdirectory?,"Yes, it is. It should be found via `members = [""packages/*""]`",,2025-06-13T08:52:00.065000Z,6,7,8,Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67
Include PDF in Oasis|2025-06-05T10:04:09.333000Z|d38858399c|q9-a10-12,0.85,discord_thread_heuristic,Include PDF in Oasis,issues,do you mean the browser annotation or which kind of quantity did you use to store the data? do you by any chance still have a some code snippets that i can have a look at?,"yes the `a_` annotation `batch_plan_pdf = Quantity(type=str, a_browser=dict(adaptor='RawFileAdaptor'))`",,2025-06-05T10:04:09.333000Z,9,10,12,Include PDF in Oasis|2025-06-05T10:04:09.333000Z|d38858399c
is the structure display broken in develop?|2025-06-03T11:41:04.468000Z|a8258e1bc2|q1-a2-3,0.85,discord_thread_heuristic,is the structure display broken in develop?,issues,Could you please share a link?,"It seems today the issue resolved itself. not sure if it was an update from my browser or a recent fix, today both develop deployment and most recent develop locally work again for me.",,2025-06-03T11:41:04.468000Z,1,2,3,is the structure display broken in develop?|2025-06-03T11:41:04.468000Z|a8258e1bc2
Can't login to distro-dev|2025-04-14T13:53:10.381000Z|37d17c5a03|q4-a5-6,0.85,discord_thread_heuristic,Can't login to distro-dev,issues,Do you use a gmail address?,"In fact, I see two entries with your name, one named test. However, they are both in the production realm. That's the one you access when you use the main nomad site.",,2025-04-14T13:53:10.381000Z,4,5,6,Can't login to distro-dev|2025-04-14T13:53:10.381000Z|37d17c5a03
Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c|q63-a64-65,0.85,discord_thread_heuristic,Mongo DB collection warning is weird,issues,"What do you mean with ""did not have anything in the group""?",That the extra group `mongo_user_group` was empty,,2025-03-07T10:17:12.565000Z,63,64,65,Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c
pynxtools with config files - wrong way of passing the config file?|2025-02-27T13:54:49.583000Z|38a2e2719f|q8-a9-10,0.85,discord_thread_heuristic,pynxtools with config files - wrong way of passing the config file?,issues,Is there now a better or different way to pass a config file?,"Ok. Small update. The version of pynxtools-xps was not the problem. Now I reinstalled from my local copy of it, and I still get the crash",,2025-02-27T13:54:49.583000Z,8,9,10,pynxtools with config files - wrong way of passing the config file?|2025-02-27T13:54:49.583000Z|38a2e2719f
Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q19-a20-21,0.85,discord_thread_heuristic,Uploads are getting directly stuck in nomad-distro-dev,issues,Can you share a screenshot of the terminal you are using and the error message?,ah you'll have to delete the venv folder and try again,,2025-01-09T14:23:39.252000Z,19,20,21,Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9
"Setup error on ""nomad-distro-dev""|2025-01-08T14:54:17.574000Z|58cb8ee208|q133-a134-135",0.85,discord_thread_heuristic,"Setup error on ""nomad-distro-dev""",issues,What's the output for `docker ps`?,"In browser developer console, what's the output for `window.nomadEnv`",,2025-01-08T14:54:17.574000Z,133,134,135,"Setup error on ""nomad-distro-dev""|2025-01-08T14:54:17.574000Z|58cb8ee208"
Include new modifications of plugins in the Oasis Docker image|2024-12-12T14:30:13.125000Z|8ee3628afe|q9-a18-19,0.85,discord_thread_heuristic,Include new modifications of plugins in the Oasis Docker image,issues,Are you using any version tags for your plugin?,"` ""nomad-txrm-parser @ git+https://github.com/Guillaume-Gaisne/nomad-TXRM_parser.git"",`",,2024-12-12T14:30:13.125000Z,9,18,19,Include new modifications of plugins in the Oasis Docker image|2024-12-12T14:30:13.125000Z|8ee3628afe
Problem when launching the Oasis from the documentation|2024-12-06T10:51:26.395000Z|a0216bee32|q6-a7-8,0.85,discord_thread_heuristic,Problem when launching the Oasis from the documentation,issues,"Is the link pointing to a wrong version of the oasis, that it is not working nicely ?","I am working with a personal oasis, so I am used to make these steps to create an oasis.",,2024-12-06T10:51:26.395000Z,6,7,8,Problem when launching the Oasis from the documentation|2024-12-06T10:51:26.395000Z|a0216bee32
Error while running the Tutorial|2024-11-19T09:00:42.371000Z|55a178e6ca|q4-a5-6,0.85,discord_thread_heuristic,Error while running the Tutorial,issues,Where did you run it?,Directly running it on the example oasis using the clicking on the link,,2024-11-19T09:00:42.371000Z,4,5,6,Error while running the Tutorial|2024-11-19T09:00:42.371000Z|55a178e6ca
"Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd|q19-a20-21",0.85,discord_thread_heuristic,"Uploads are not displayed, despite being in the volumes folder",issues,Can you quickly tell me where I have to do this?,Okay then you can try to export bundle using cli and then import to your test oasis using cli as well.,,2024-10-09T19:25:25.125000Z,19,20,21,"Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd"
The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q45-a46-47,0.85,discord_thread_heuristic,The new oasis-template doesn't works,issues,What do you mean with my upload ?,"If you were uploading a specific file that causes this issue, or if this error comes up when you try to create a new upload",,2024-10-04T13:34:29.120000Z,45,46,47,The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8
The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q72-a73-74,0.85,discord_thread_heuristic,The new oasis-template doesn't works,issues,Do you see anything in the browser console log?,"In chrome it should be right click -> inspect, and you should see that side bar",,2024-10-04T13:34:29.120000Z,72,73,74,The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8
The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q76-a77-78,0.85,discord_thread_heuristic,The new oasis-template doesn't works,issues,What do you see in network tab?,This is how it looks for me when I click Upload,,2024-10-04T13:34:29.120000Z,76,77,78,The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8
"Creating multiple uploads causes some to get stuck ""Waiting""|2024-08-28T11:24:51.603000Z|7bca1ff866|q6-a8-10",0.85,discord_thread_heuristic,"Creating multiple uploads causes some to get stuck ""Waiting""",issues,do you have any idea what is happening here? Are we overloading the oasis by pushing too many uploads at the same time?,We don't get the issue when we create the upload in the GUI and use the file dialog to upload the zip file. Running `nomad admin reset-processing` it needed to reset 13 Entry processes in and 14 Upload processes,,2024-08-28T11:24:51.603000Z,6,8,10,"Creating multiple uploads causes some to get stuck ""Waiting""|2024-08-28T11:24:51.603000Z|7bca1ff866"
Uploading process continues forever!|2024-05-22T12:30:19.255000Z|9e206ccef9|q8-a9-10,0.85,discord_thread_heuristic,Uploading process continues forever!,issues,"could you have a quick look on it, please?",You need to open the interactive terminal on your nomad-app container and reset the processing.,,2024-05-22T12:30:19.255000Z,8,9,10,Uploading process continues forever!|2024-05-22T12:30:19.255000Z|9e206ccef9
Unwanted warning for NumberEditQuantity of unit='dimensionless'|2024-04-30T14:42:44.988000Z|16a758717d|q11-a12-13,0.85,discord_thread_heuristic,Unwanted warning for NumberEditQuantity of unit='dimensionless',issues,Does is work if you simply ommit the unit in the schema?,"It works fine in that case, however, the quantity is not of type `pint.Quantity` in that case",,2024-04-30T14:42:44.988000Z,11,12,13,Unwanted warning for NumberEditQuantity of unit='dimensionless'|2024-04-30T14:42:44.988000Z|16a758717d
Metainfo Browser for Schema Plugins|2024-04-18T09:36:17.035000Z|227c642bc1|q1-a5-6,0.85,discord_thread_heuristic,Metainfo Browser for Schema Plugins,issues,"The normal metainfo browser should show plugins as a ""source"" when installed. But I guess you want something, where you don't have a nomad installation and just get a browser, e.g. `nomad admin run metainfo-browser`?","For install plugins you can use the normal metainfo browser, e.g. here is the `nomad_measurments` on the example oasis: <https://nomad-lab.eu/prod/v1/oasis/gui/analyze/metainfo/nomad_measurements>. Maybe we can improve the metainfo browser, but there is no plan for an external tool/service.",,2024-04-18T09:36:17.035000Z,1,5,6,Metainfo Browser for Schema Plugins|2024-04-18T09:36:17.035000Z|227c642bc1
"Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.|2024-04-02T14:04:10.722000Z|8eb0f03f1d|q4-a5-6",0.85,discord_thread_heuristic,"Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.",issues,"Does it persist? I get some weird timeout errors when working from home with the NOMAD keycloak which gives the same error. Usually waiting a minute and then closing the error resolves this. If that doesn't work, could you check the logs and see if there is some more info there?","for your answers. Yes the troubleshooting is indeed hard, because the error does not occur in a reproducible manner. Sometimes everything works fine, sometimes not. And I have the feeling that the behaviour also depends on the used browser and if I e.g. open uploads in a new tab or the same tab. Edge seems to work slightly better than Firefox and often in new tabs the error occurs more often.",,2024-04-02T14:04:10.722000Z,4,5,6,"Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.|2024-04-02T14:04:10.722000Z|8eb0f03f1d"
Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q1-a3-4,0.84,discord_thread_heuristic,Reproducibility indicators bug from afm nexus file,issues,can you please help here?,"Image lower left: GUI shows the sections and list of concepts in that section (next to the selected column). In this case, the section has only one field available there which is `cantilever_tip_temperature` that field contains the data and clicking the you may see the data on the nexus file (in most left column).",,2025-08-11T09:40:29.449000Z,1,3,4,Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1
displaying non-symmetric tensors in GUI|2025-07-29T14:01:43.413000Z|c94a836dd8|q12-a14-15,0.84,discord_thread_heuristic,displaying non-symmetric tensors in GUI,issues,"What would be then the best practice for the use cases, in which we wish to skip transposing the tensors in the GUI, because we need exactly the tensor in the archive and showing a `Matrix.T` would be scientifically false?","if we let the parser transpose the data as `Matrix.T`, GUI displays the data correctly ✅ , but the stored data in the archive is now transposed ⛔",,2025-07-29T14:01:43.413000Z,12,14,15,displaying non-symmetric tensors in GUI|2025-07-29T14:01:43.413000Z|c94a836dd8
ArchiveQuery raising errors in nomad-lab v1.3.15|2025-03-26T15:19:44.146000Z|c02881671c|q35-a42-43,0.84,discord_thread_heuristic,ArchiveQuery raising errors in nomad-lab v1.3.15,issues,How do you mean?,Also control whether to use api or directly contact keycloak to get token via option `from_api` in archive query.,,2025-03-26T15:19:44.146000Z,35,42,43,ArchiveQuery raising errors in nomad-lab v1.3.15|2025-03-26T15:19:44.146000Z|c02881671c
Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff|q42-a44-45,0.84,discord_thread_heuristic,Permission denied when creating new Jupyter Notebook in uploads folder,issues,Do you have an idea what could be the problem here?,"~~Looking at your description, you might be accidentally doing the wrong thing: you need to first navigate inside the upload folder, then right-click and press ""New Notebook"": this will create it inside the folder. The way you described it will try to create it in the ""uploads"" folder which is not permitted. A Jupyter user does not have write priviledges for that.~~",,2024-11-20T11:04:36.071000Z,42,44,45,Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff
Help with parse_section()|2024-06-17T08:27:08.519000Z|2a61c0228e|q30-a32-33,0.84,discord_thread_heuristic,Help with parse_section(),issues,Is it preferred not to use m_def?,"both are equivalent, so I think you can use whatever feels more comfortable, but probably Theodore knows more",,2024-06-17T08:27:08.519000Z,30,32,33,Help with parse_section()|2024-06-17T08:27:08.519000Z|2a61c0228e
HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q53-a55-56,0.84,discord_thread_heuristic,HDF5 in NOMAD,issues,Why does the processing of this 1.7MB example .h5 file take several minutes?,"I also get alway a ""Unexpected error: ""[object Object] (500)"". Please try again and let us know, if this error keeps happening."" in a red bar in the web-interface when uploading the schema + .h5 file",,2024-05-27T13:17:35.844000Z,53,55,56,HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57
pyBIS error when running setup_dev_env.sh|2024-05-13T09:51:10.077000Z|027d978143|q7-a9-11,0.84,discord_thread_heuristic,pyBIS error when running setup_dev_env.sh,issues,Does `git clone https://github.com/FAIRmat-NFDI/fairmat-pybis.git/` work?,But not during the pip install I'll try again later. Maybe it was something temporary,,2024-05-13T09:51:10.077000Z,7,9,11,pyBIS error when running setup_dev_env.sh|2024-05-13T09:51:10.077000Z|027d978143
FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q82-a87-88,0.83,discord_thread_heuristic,FHIaims parsing issues,issues,"The deployments are usually the same 1.3.16 version though, but I guess the electronicparser versions might be different?",In the `uv.lock` file it should tell which versions of the packages are installed. Here's the link for the template: https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/uv.lock#L3504,,2025-07-24T12:22:12.739000Z,82,87,88,FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93
publishing data from a NOMAD-oasis|2025-05-07T12:27:13.463000Z|6a04a5868f|q8-a18-19,0.83,discord_thread_heuristic,publishing data from a NOMAD-oasis,issues,How is this currently possible (Publish it on local NOMAD than publishing on/to central NOMAD)?,"Regarding the question about to publish in an Oasis. Yes, you can do that (on your own risks and rules), but to generate a DOI in those datasets you will need to configure your NOMAD installation to have a DOI provider (https://nomad-lab.eu/prod/v1/docs/reference/config.html#datacite). Also, I guess you will need to have your Oasis openly available (not only on your intranet).",,2025-05-07T12:27:13.463000Z,8,18,19,publishing data from a NOMAD-oasis|2025-05-07T12:27:13.463000Z|6a04a5868f
Customization of an app|2025-02-24T07:24:54.477000Z|a04c15214a|q2-a7-8,0.83,discord_thread_heuristic,Customization of an app,issues,Is there a way to fix this issue?,https://nomad-lab.eu/prod/v1/docs/howto/plugins/apps.html#column,,2025-02-24T07:24:54.477000Z,2,7,8,Customization of an app|2025-02-24T07:24:54.477000Z|a04c15214a
Saving bug in old GUI; normalisation to workflow2|2024-12-14T17:44:41.436000Z|d28b697242|q14-a19-20,0.83,discord_thread_heuristic,Saving bug in old GUI; normalisation to workflow2,issues,What should have happened instead?,We could address this in the MR: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2053,,2024-12-14T17:44:41.436000Z,14,19,20,Saving bug in old GUI; normalisation to workflow2|2024-12-14T17:44:41.436000Z|d28b697242
Parser plugin error|2024-05-24T14:33:02.169000Z|0ce0e65647|q10-a13-15,0.83,discord_thread_heuristic,Parser plugin error,issues,do you get the same ?,"In my parser class, I had beside the `parse` function a custom `__init__`. had the same issue and I told him to try removing it",,2024-05-24T14:33:02.169000Z,10,13,15,Parser plugin error|2024-05-24T14:33:02.169000Z|0ce0e65647
Plugin does not show up in Oasis installation|2024-05-14T11:08:47.371000Z|08ac9da2df|q10-a15-16,0.83,discord_thread_heuristic,Plugin does not show up in Oasis installation,issues,can you check if there is something in the logs of you nomad app?,[:12:11 +0000] [7] [INFO] Listening at: http://0.0.0.0:8000 (7),,2024-05-14T11:08:47.371000Z,10,15,16,Plugin does not show up in Oasis installation|2024-05-14T11:08:47.371000Z|08ac9da2df
Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q44-a45-46,0.82,discord_thread_heuristic,Reproducibility indicators bug from afm nexus file,issues,"again about this point and just to clarification, how is it possible that the groups in the scan_environment section are created correctly? I mean, in that case those groups are defined in the xml without any specification of the quantity to report but when I create the link from the sensors defined in the main part of the instrument I'm able to see all quantities contained in the referenced sections. What are the differences to the case of the NXcomponent class used to refer the oscillator?",Can it be because some of these are explicitly defined in and inherited from `NXspm`? See https://fairmat-nfdi.github.io/nexus_definitions/classes/contributed_definitions/NXspm.html#nxspm-entry-instrument-scan-environment-group,,2025-08-11T09:40:29.449000Z,44,45,46,Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1
How to create an Admin Account on our Oasis?|2025-07-16T11:33:32.132000Z|79c53f52a5|q3-a4-5,0.82,discord_thread_heuristic,How to create an Admin Account on our Oasis?,issues,👍 and how do we find the user id of the account we want to make a admin user?,You could use the API: https://nomad-lab.eu/prod/v1/staging/api/v1/extensions/docs#operations-tag-users,,2025-07-16T11:33:32.132000Z,3,4,5,How to create an Admin Account on our Oasis?|2025-07-16T11:33:32.132000Z|79c53f52a5
Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q31-a32-33,0.82,discord_thread_heuristic,Create multiple entry from parser or new entry in normalizer,issues,Maybe let's start from the beginning. Do you need to have ELN functionality in any of the entries after parsing?,"If you don't need any ELN functionality you should be able to use a parser and `child_archives`. However, I have never done this myself so I would then refer you to someone from Area C/D. Perhaps could explain what's going wrong there.",,2025-06-14T07:31:20.113000Z,31,32,33,Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab
NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007|q22-a23-24,0.82,discord_thread_heuristic,NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12,issues,"0) At the moment NOMAD Oasis version 1.3.16 is the most stable recent one, nor?",1) How to I download/get/install the most recent NOMAD Oasis image version (==1.3.16) without any modification? What exact commands I have to run besides `docker compose pull` and `docker compose up -d`? At the moment it only downloads all images and nomad-lab with v1.3.15.,,2025-06-06T14:58:44.249000Z,22,23,24,NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007
"Jupyter launch, unexpected error (504)|2025-04-15T11:49:41.936000Z|8709bf1672|q12-a15-16",0.82,discord_thread_heuristic,"Jupyter launch, unexpected error (504)",issues,Do you mean this https://dash.plotly.com/dash-in-jupyter?,"and I mean this sideways question I'll try managing myself, was just curious if you tried it already",,2025-04-15T11:49:41.936000Z,12,15,16,"Jupyter launch, unexpected error (504)|2025-04-15T11:49:41.936000Z|8709bf1672"
Limiting the Oasis workers to save memory|2025-04-09T08:09:43.694000Z|70654d8f14|q15-a16-17,0.82,discord_thread_heuristic,Limiting the Oasis workers to save memory,issues,"I am in a similar situation that I want to limit the number of workers for our oasis, as the machine has very many cores. Where and how can I configure this?",We set the limit on number of tasks https://github.com/FAIRmat-NFDI/nomad-distro-template/pull/85,,2025-04-09T08:09:43.694000Z,15,16,17,Limiting the Oasis workers to save memory|2025-04-09T08:09:43.694000Z|70654d8f14
Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94|q95-a98-99,0.82,discord_thread_heuristic,Adding h5view of data file to entry overview,issues,is this the updated def?,if you want to plot only a dataset use the signal field,,2025-04-03T09:05:50.630000Z,95,98,99,Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94
distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q40-a41-42,0.82,discord_thread_heuristic,distro-dev not running on latest develop,issues,"Hrmm, I still get the error. Do I need to delete venv/cache and run setup again?","yep, can you try deleting the `uv.lock` file too",,2025-04-01T12:49:10.385000Z,40,41,42,distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4
Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5|q19-a26-27,0.82,discord_thread_heuristic,Protected Oasis - Restrict GUI/API Access only for Login-User,issues,Is not the option oasis:allowed_users: doing this?,"Problem statement/use case: But also within the ""University network"" the ""public part"" of the oasis - the gui/search interface (e.g. https://nomad-lab.eu/prod/v1/oasis/gui/search/eln ) and also the public API can be accessed by all people who know the IP address of the dedicated oasis (and maybe they are not part of any associated working groups). As there is a very nice convenient feature for ""Uploads in Step3"" with the checkbox: ""Edit visibility and access; Enabling this will allow all users, including guests without an account, to view the upload even before it is published."" to share unpublished quite recent research data between the working groups for review not bothering you to select a special group or person. Also ""enabling visibility of unpublished data"" allows for data changes e.g. some data have been updated in contrast to ""published data in the oasis can not be altered (step 5)"". So, I am searching for a workflow to share all (""not yet publishable"") data to everybody, who has access to the oasis, but not exposing recent research data to the world/university.",,2025-02-28T08:48:27.056000Z,19,26,27,Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5
Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41|q2-a3-8,0.82,discord_thread_heuristic,Updating distro-dev after pydantic update,issues,Can you try `uv python install 3.12.8`?,Or `3.12.9` I get: ``` Searching for Python versions matching: Python 3.12.8 error: No download found for request: cpython-3.12.8-linux-x86_64-gnu,,2025-02-10T08:34:08.970000Z,2,3,8,Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41
Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q39-a40-41,0.82,discord_thread_heuristic,Unexpected dependency version on NOMAD develop,issues,"E.g., I don't think nomad-lab relies on MDanalysis anymore, but plugins like simulation workflow schema, which is in the central deployments, do depend on this. Do we still need the dependency in requirements-plugins.txt, or it's enough that the plugin depends on it within it's toml?","2. `FAIRmat-NFDI/nomad-distro-dev` is only used to configure your local development environments, so it has no effect on any deployments.",,2025-01-24T11:39:53.165000Z,39,40,41,Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c
Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q46-a47-48,0.82,discord_thread_heuristic,Unexpected dependency version on NOMAD develop,issues,"It looks like these were updated a week ago. But the atomistic parsers has several versions above this, e.g., v1.0.3 from 3 weeks ago. Why is this not updating in the requirements and how can I fix this?","The toml gives a valid set of distributions, the `requirements.txt` is generated using the `scripts/generate_python_dependencies.sh` script. But if you want a specific version of nomad-parser-plugins-atomistic in the requirements file, you can bump up the version in the toml file and generate the requirements txt file.",,2025-01-24T11:39:53.165000Z,46,47,48,Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c
Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q48-a49-50,0.82,discord_thread_heuristic,Unexpected dependency version on NOMAD develop,issues,"I don't want a specific version though, I just always want the latest release version. I thought this would already happen with `""nomad-parser-plugins-atomistic>=1.0""` in the toml. The question is why is this stuck at the 1.0.2 release?","In the `scripts/generate_python_dependencies.sh` it will only upgrade nomad-lab and nomad-lab-gui, no plugins are upgraded automatically so they will always keep their locked version. The intention here was that no other dependency gets automatically updated and individual plugin authors can upgrade their plugins as they see fit and we have a more controlled roll out. Otherwise, if random plugins get updated automatically during the nightly run, the pipeline might start failing due to changes in the plugin.",,2025-01-24T11:39:53.165000Z,48,49,50,Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c
New plugin does not show up in a nomad-distro-dev|2025-01-21T16:08:15.456000Z|9f9ff6cfc7|q4-a5-6,0.82,discord_thread_heuristic,New plugin does not show up in a nomad-distro-dev,issues,"- the parser is triggered when you drag and drop your csv file in the upload, is something happening?","- did you create your classes inheriting from ""EntryData"" like in the [examples](https://github.com/FAIRmat-NFDI/AreaA-Examples/blob/7dd9ab0b3c257fa8f734327f3cf3be334007968e/workshop_01-25/schema/plotting.py#L58) ?",,2025-01-21T16:08:15.456000Z,4,5,6,New plugin does not show up in a nomad-distro-dev|2025-01-21T16:08:15.456000Z|9f9ff6cfc7
DEV Deployment stalls on upload|2025-01-21T10:41:48.261000Z|07f5df7e4e|q15-a18-19,0.82,discord_thread_heuristic,DEV Deployment stalls on upload,issues,Are these all uploads that are left in a limbo state because of failed processing? They are not published or anything?,"It could be that there is some sort of memory bug or something that is subtle, but in my experience the hanging is somewhat rare and random, so I wouldn't really want to spend time digging into this. Perhaps we can start keeping a more detailed log of these issues to try to find any correlations.",,2025-01-21T10:41:48.261000Z,15,18,19,DEV Deployment stalls on upload|2025-01-21T10:41:48.261000Z|07f5df7e4e
The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q43-a44-45,0.82,discord_thread_heuristic,The new oasis-template doesn't works,issues,Can you share the upload?,I am able to create a Sintering upload,,2024-10-04T13:34:29.120000Z,43,44,45,The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8
More a question than an issue: data curation.|2024-09-18T08:20:59.171000Z|31ca795375|q9-a10-11,0.82,discord_thread_heuristic,More a question than an issue: data curation.,issues,"> * Imagine you have a setup that directly outputs data to your NOMAD OASIS, can you then modify, delete, annotate the data sets, add some field to it etc.?","Being admin of your oasis, you can essentially re-run/re-process entries at will (https://nomad-lab.eu/prod/v1/staging/docs/howto/oasis/admin.html). You could have a script running at a different moment in time to run your entries to modify, delete, annotate fields... This can be done creating and maintaining a parser plugin.",,2024-09-18T08:20:59.171000Z,9,10,11,More a question than an issue: data curation.|2024-09-18T08:20:59.171000Z|31ca795375
Help with parse_section()|2024-06-17T08:27:08.519000Z|2a61c0228e|q18-a26-27,0.82,discord_thread_heuristic,Help with parse_section(),issues,Can anyone give me some direction as to what might be going wrong here?,"You can also use `section_def` without the need of adding `m_def`, something like:",,2024-06-17T08:27:08.519000Z,18,26,27,Help with parse_section()|2024-06-17T08:27:08.519000Z|2a61c0228e
Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed|q53-a54-55,0.82,discord_thread_heuristic,Mkdocs Error when updating Nomad,issues,"Mh, looks exactly like mine. Does `python -c ""from mkdocs.__main__ import cli""` work for you? This is what your original error message is about, line 5 of the shell command. Is your `which python` also pointing to `/home/<USER>/software/nomad/.pyenv/bin/python3.9`. This is very weird. But I guess you can like with using `python -m mkdocs` as an alternative?","No, `python -c ""from mkdocs.__main__ import cli""` produces the same error. `import mkdocs` works, so I could try the second options.",,2024-05-07T09:51:39.304000Z,53,54,55,Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed
Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q9-a35-36,0.81,discord_thread_heuristic,Reproducibility indicators bug from afm nexus file,issues,"could you make the file available somewhere to reproduce? Is this on a local oasis, or a central installation?","The problem is related to the linking. Essentially, in `/NXafm/ENTRY/reproducibility_indicators`, we only define `cantilever_oscillator`, but none of its sub-items. NOMAD will then only try to parse the `cantilever_oscillator` group from the link, but not anything that is inside that Hdf5 group. This is the general case for links, if you want to have all sub-elements also show up at the link destination, you need to explicitly define them in application definition as well.",,2025-08-11T09:40:29.449000Z,9,35,36,Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1
FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q88-a113-114,0.81,discord_thread_heuristic,FHIaims parsing issues,issues,can you confirm the version you have in your repo?,"So for example `nomad-lab[parsing, infrastructure]==1.3.16` works, but `nomad-lab[parsing, infrastructure] @ git+https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR.git does not even though it is the same version (will crash on start due to missing /opt/venv/lib/python3.12/site-packages/nomad/app/static/gui). So I assume I'm still missing some extra steps...",,2025-07-24T12:22:12.739000Z,88,113,114,FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93
Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271|q32-a66-67,0.81,discord_thread_heuristic,Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage,issues,Do you think this could work for you?,"Note that the fixes are enabled only in our `develop` deployment, which contains nightly updates for our development purposes. So if you want to reprocess something yourself, you need to go to: http://nomad-lab.eu/prod/v1/develop/gui",,2025-05-16T07:42:17.401000Z,32,66,67,Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271
PlotSection for Static Image|2025-04-17T04:24:57.503000Z|1ce8a0f30c|q0-a6-7,0.81,discord_thread_heuristic,PlotSection for Static Image,issues,I asked myself if there's any built-in functionality to plot/show an image such as `.tif` as these are the results of [SEM measurements](https://en.wikipedia.org/wiki/Scanning_electron_microscope)?,"``` python # see https://plotly.com/python/images/#zoom-on-static-images fig = go.Figure() scale_factor = 1.0 fig.add_trace( go.Scatter( x=[0, img_width * scale_factor], y=[0, img_height * scale_factor], mode=""markers"", marker_opacity=0 ) ) # Configure axes fig.update_xaxes( visible=False, range=[0, img_width * scale_factor] ) fig.update_yaxes( visible=False, range=[0, img_height * scale_factor], # the scaleanchor attribute ensures that the aspect ratio stays constant scaleanchor=""x"" ) # Add image fig.add_layout_image( dict( x=0, sizex=img_width * scale_factor, y=img_height * scale_factor, sizey=img_height * scale_factor, xref=""x"", yref=""y"", opacity=1.0, layer=""below"", sizing=""stretch"", source=uri) ) ```",,2025-04-17T04:24:57.503000Z,0,6,7,PlotSection for Static Image|2025-04-17T04:24:57.503000Z|1ce8a0f30c
Server in Jupyter lab in North tool is not starting up|2025-01-07T10:00:09.162000Z|a94c31d7e2|q2-a16-17,0.81,discord_thread_heuristic,Server in Jupyter lab in North tool is not starting up,issues,Can you try the develop instance to see if that works?,I think with `develop` they mean this: https://nomad-lab.eu/prod/v1/develop/gui/ which works for me.,,2025-01-07T10:00:09.162000Z,2,16,17,Server in Jupyter lab in North tool is not starting up|2025-01-07T10:00:09.162000Z|a94c31d7e2
Uploading non compressed files with python|2024-12-02T08:36:17.305000Z|e305a682fb|q3-a34-35,0.81,discord_thread_heuristic,Uploading non compressed files with python,issues,"Which doesn't appear if I compress my nexus file into a zip file for example. However, from what I checked, the requests module seems to be able to take all kind of file (or maybe by AI helper lied, which happens). Do you know if there's an option I should modify in the call to requests?","Oki, cool. so I updated the `update_to_NOMAD` function as follow. Should I/can I update the NOMAD documentation accordingly? I think others may bump into the same problem.",,2024-12-02T08:36:17.305000Z,3,34,35,Uploading non compressed files with python|2024-12-02T08:36:17.305000Z|e305a682fb
Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q38-a88-89,0.81,discord_thread_heuristic,Memory error when I build my container,issues,When you add a plugin do you just add it to project.optional-dependencies or you do something else ?,it's the `Template Repository Initialisation` step mentioned here: https://github.com/PauloGitHB/nomad-oasis-cemes?tab=readme-ov-file#nomad-oasis-distribution-template,,2024-11-15T09:44:36.042000Z,38,88,89,Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d
Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q77-a88-89,0.81,discord_thread_heuristic,Memory error when I build my container,issues,can you share your `docker-compose.yml` file?,it's the `Template Repository Initialisation` step mentioned here: https://github.com/PauloGitHB/nomad-oasis-cemes?tab=readme-ov-file#nomad-oasis-distribution-template,,2024-11-15T09:44:36.042000Z,77,88,89,Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d
The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q79-a85-86,0.81,discord_thread_heuristic,The new oasis-template doesn't works,issues,Are there any logs for `docker logs nomad_oasis_app`?,some people needed to chaneg the IP in this file `/etc/docker/daemon.json `,,2024-10-04T13:34:29.120000Z,79,85,86,The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8
Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q31-a76-77,0.81,discord_thread_heuristic,Adapting existing Oasis to new plugin mechanism,issues,How do you mean they are not compatible? You mean regarding the nomad-lab dependency?,"I now commented on <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1610> and <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1955>. The relevant people here, should be part of the respective issues. My idea would be to work on the ""button"" and on the action you do (e.g. creating entries, updating entries) together and to finally document such a use-base with a how-to in the documentation.",,2024-10-01T12:39:26.289000Z,31,76,77,Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459
docker-compose up error with nomad-lab 3.11 setup|2024-08-13T12:18:42.333000Z|98252609bf|q12-a30-31,0.81,discord_thread_heuristic,docker-compose up error with nomad-lab 3.11 setup,issues,"what do you mean the ""full logs""?",Maybe here: https://nomad-lab.eu/prod/v1/develop/docs/howto/develop/setup.html#install-docker,,2024-08-13T12:18:42.333000Z,12,30,31,docker-compose up error with nomad-lab 3.11 setup|2024-08-13T12:18:42.333000Z|98252609bf
2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d|q63-a106-107,0.81,discord_thread_heuristic,2D array of strings,issues,What about `np.str_`?,"Well, based on the Traceback, it seems that using the `nomad parse` command reach out at some point the `/home/<USER>/Desktop/.venvdev/lib/python3.9/site-packages/nomad/cli/parse.py` file which seems to contain the code that is running for the the `parse` argument and in there, the call to `EntryMetadata` seems to be made through this line `entry_archive.metadata.apply_archive_metadata(entry_archive)` (line 88)",,2024-06-11T08:27:23.680000Z,63,106,107,2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d
2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d|q87-a106-107,0.81,discord_thread_heuristic,2D array of strings,issues,"What I am guessing is that you are populating `EntryMetadata`, i.e., the `EntryArchive.metadata` section. In my case, I am populating `EntryData`/`EntryArchive.data`. Is that true?","Well, based on the Traceback, it seems that using the `nomad parse` command reach out at some point the `/home/<USER>/Desktop/.venvdev/lib/python3.9/site-packages/nomad/cli/parse.py` file which seems to contain the code that is running for the the `parse` argument and in there, the call to `EntryMetadata` seems to be made through this line `entry_archive.metadata.apply_archive_metadata(entry_archive)` (line 88)",,2024-06-11T08:27:23.680000Z,87,106,107,2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d
Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed|q6-a37-38,0.81,discord_thread_heuristic,Mkdocs Error when updating Nomad,issues,Is mkdocs installed in your python environment?,You original error messages sounds a bit like the mkdocs package is missing completely. I guess you cannot run `mkdocs` at all. Is `mkdocs --help` already failing? What happens if you try to run the mkdocs package instead of the shell command: `python -m mkdocs build`.,,2024-05-07T09:51:39.304000Z,6,37,38,Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed
Migrating to new plugin mechanism - Problem with Parser|2025-06-23T12:49:00.346000Z|990628677f|q2-a4-5,0.8,discord_thread_heuristic,Migrating to new plugin mechanism - Problem with Parser,issues,I guess the reason is that I changed the name of my parser in the new plugin (to: 'nomad_perolab_umr.parsers:cicci_txt_parser_entry_point') . For schemas there is the chance to give alias names. Is there something similar for parsers?,Note sure if this works for parser packages too? Could you give it a try and report back here? https://nomad-lab.eu/prod/v1/staging/docs/howto/plugins/schema_packages.html#schema-package-aliases,,2025-06-23T12:49:00.346000Z,2,4,5,Migrating to new plugin mechanism - Problem with Parser|2025-06-23T12:49:00.346000Z|990628677f
Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e|q2-a4-5,0.8,discord_thread_heuristic,Where to find available base sections for yaml,issues,"Or, is there a basic section with DFT simulation metainfo fields one could inherit from?",But it can be a bit difficult to find what you are looking for. If you are looking specifically for sections inheriting from the `BaseSection` parent class you can look at all the inheriting sections of that one: https://nomad-lab.eu/prod/v1/gui/analyze/metainfo/nomad/section_definitions,,2025-06-02T09:45:34.629000Z,2,4,5,Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e
ArchiveQuery error|2025-05-05T13:43:22.936000Z|5e3ea3852f|q13-a14-15,0.8,discord_thread_heuristic,ArchiveQuery error,issues,Can you try switching to nomad v1.3.15 locally?,Sure but it wasn't working on 1.3.14 either,,2025-05-05T13:43:22.936000Z,13,14,15,ArchiveQuery error|2025-05-05T13:43:22.936000Z|5e3ea3852f
Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c|q38-a39-40,0.8,discord_thread_heuristic,Failing test in plugin,issues,Can you test if removing the scaling factor in your case fixes the issue?,"the great shout! I simply translated the units from the magres specification document, as they are found in the magres file which we parse. I did make sure that the units I defined were found in the pint repo definitions, but it makes sense that the scaling factor is unexpected. When I declare the unit as m^3/mol, it works fine.",,2025-04-14T08:48:46.049000Z,38,39,40,Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c
distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q38-a39-40,0.8,discord_thread_heuristic,distro-dev not running on latest develop,issues,What version should I go for?,`>=0.3.2`,,2025-04-01T12:49:10.385000Z,38,39,40,distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4
Apps dissapear in NOMAD Oasis 1.3.15|2025-03-25T09:49:02.502000Z|408040b015|q5-a6-7,0.8,discord_thread_heuristic,Apps dissapear in NOMAD Oasis 1.3.15,issues,Can you DM me the `nomad.yaml` with the secrets removed?,"yes this is correct and this is a valuable fix in 1.3.15, you need to explicitly enable APPs, otherwise all available apps are shown, which was a mess 😄",,2025-03-25T09:49:02.502000Z,5,6,7,Apps dissapear in NOMAD Oasis 1.3.15|2025-03-25T09:49:02.502000Z|408040b015
Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c|q42-a51-52,0.8,discord_thread_heuristic,Mongo DB collection warning is weird,issues,What does `docker ps` say on your system?,So you should be able to use the snippet without the `docker exec`.,,2025-03-07T10:17:12.565000Z,42,51,52,Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c
Overwriting subsections with quantities not possible?|2024-12-02T15:18:28.067000Z|5ce0be9857|q0-a4-5,0.8,discord_thread_heuristic,Overwriting subsections with quantities not possible?,issues,Is it intended that MetaInfo does not allow to overwrite a SubSection with a Quantity?,`nomad.metainfo.metainfo.MetainfoError: Cannot inherit from different property types.`,,2024-12-02T15:18:28.067000Z,0,4,5,Overwriting subsections with quantities not possible?|2024-12-02T15:18:28.067000Z|5ce0be9857
Pint and pydantic update|2024-11-18T12:03:55.550000Z|9743355b6f|q2-a4-5,0.8,discord_thread_heuristic,Pint and pydantic update,issues,⁠ Are there any plans if/when these changes will come?,"The main problem is that both updates require quite a bit of work, as in the case of Pydantic, we need to update our code to use the new interface, and in the case of Pint, we would need to change several of our parsers due to this issue: https://github.com/hgrecco/pint/issues/1809",,2024-11-18T12:03:55.550000Z,2,4,5,Pint and pydantic update|2024-11-18T12:03:55.550000Z|9743355b6f
Clarify Display Docs|2024-11-01T14:15:53.587000Z|3650b87949|q19-a23-24,0.8,discord_thread_heuristic,Clarify Display Docs,issues,What if pass it as a dict?,remove the models SectionDisplayAnnotation and Filter and use dict,,2024-11-01T14:15:53.587000Z,19,23,24,Clarify Display Docs|2024-11-01T14:15:53.587000Z|3650b87949
NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520|q17-a18-19,0.8,discord_thread_heuristic,NOMAD Oasis Requests Takes a lot of Time from Institute Intranet,issues,Do you think this is caused by the lack of SSL certification?,"PS. also it is interesting that no matter what I do(clear cache, hard reset etc.), a single request is always loaded from memory cache (data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAQAAAAfQ//73v/+BiOh/AAA=) I prevented it by blocking.",,2024-10-29T16:31:40.296000Z,17,18,19,NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520
The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q49-a53-54,0.8,discord_thread_heuristic,The new oasis-template doesn't works,issues,could you try cloning the repo and see if you can reproduce this?,"try stuff like changing the browser you use, deleting the cache, updating the version of your browser if available",,2024-10-04T13:34:29.120000Z,49,53,54,The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8
inconsistencies between develop deployment and local development|2024-09-17T10:20:47.494000Z|5c8eda6b6f|q7-a9-10,0.8,discord_thread_heuristic,inconsistencies between develop deployment and local development,issues,"I did make an update to the atomisticparser plugin version yesterday which could cause this, but again I am expecting this to have been updated already. Is there a way to check the plugin version on the deployment?",I've created a PR to update the atomistic parser https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-distro/-/merge_requests/35,,2024-09-17T10:20:47.494000Z,7,9,10,inconsistencies between develop deployment and local development|2024-09-17T10:20:47.494000Z|5c8eda6b6f
Plugin Loaded twice|2024-09-04T10:23:06.791000Z|351b42840e|q11-a15-16,0.8,discord_thread_heuristic,Plugin Loaded twice,issues,"Could the three of you , , coordinate to test this? Now that I'm removing the duplication of the metainfo, I'm no longer sure if entries that use the old alias will be displayed properly. Maybe you could test this branch with entries that use both old and new m_defs?",I guess this is `workflow` vs `workflow2`,,2024-09-04T10:23:06.791000Z,11,15,16,Plugin Loaded twice|2024-09-04T10:23:06.791000Z|351b42840e
"""This entry does not exist."" upon publishing|2024-08-19T11:24:33.397000Z|c54f76332e|q7-a9-10",0.8,discord_thread_heuristic,"""This entry does not exist."" upon publishing",issues,"This particular upload should now work, could you confirm that everything is as it should?",update. Unfortunately I'm still getting the same problem. I've just uploaded and published this: https://nomad-lab.eu/prod/v1/test/gui/user/uploads/upload/id/n5E4GskfTyadQqeufkpaYQ,,2024-08-19T11:24:33.397000Z,7,9,10,"""This entry does not exist."" upon publishing|2024-08-19T11:24:33.397000Z|c54f76332e"
HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q31-a33-34,0.8,discord_thread_heuristic,HDF5 in NOMAD,issues,"But the Quantity obj does store the path though? Cuz it uses that to read, right?","there is also a basesection `HDF5Normalizer` that you can inherit/use in your custom schema. You can deop your h5 file, define path to the sections of your hdf5 file with proper quantity annotations.",,2024-05-27T13:17:35.844000Z,31,33,34,HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57
Website is broken|2024-04-30T09:17:15.715000Z|df2b4fdef6|q4-a5-6,0.8,discord_thread_heuristic,Website is broken,issues,Can someone else confirm that it's a Firefox issue?,"Yeah, same issue for me here here with Firefox 125.0.1",,2024-04-30T09:17:15.715000Z,4,5,6,Website is broken|2024-04-30T09:17:15.715000Z|df2b4fdef6
Exact str match of schema quantity via API|2025-07-01T06:54:57.792000Z|54f8e787d8|q4-a5-11,0.79,discord_thread_heuristic,Exact str match of schema quantity via API,issues,Is there any solution for that?,"we figured out that the trick is: ```python tank = ""manual tank"" query = { ""owner"": ""visible"", ""query"": { ""entry_type"": ""PVcomB_HFDip"", ""upload_id"": ""3HK8UmRCR3OhVqQySD1kAA"", ""search_quantities"": { ""id"": ""data.process.used_tank#nomad_pvcomb.schema_packages.pvcomb_schema_package.PVcomB_HFDip"", ""str_value:all"":tank.split("" "") } }} ```",,2025-07-01T06:54:57.792000Z,4,5,11,Exact str match of schema quantity via API|2025-07-01T06:54:57.792000Z|54f8e787d8
NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007|q24-a31-32,0.79,discord_thread_heuristic,NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12,issues,2) You are certain not to change uv.lock for the docker-build to change also the nomad-version to 1.3.16 in [lines 3550](https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/uv.lock#L3550)?,"🤦‍♂️ Now, I understand that you are referring to 🙃 I changed the `pyproject.toml`, push it to github, and the workflow on github created a docker-image for `app` and `worker` and also for `north with jupyter`, but the `docker-compose.yaml` was not updated in the process, see [here](https://github.com/Bondoki/NOMAD_Oasis_template). I fixed it manually and added a keycloak-section with adjusted `nginx_base.conf`. Now it seems to run.. and I can test the plug-in 🙃 Thanks for your patience 🙂",,2025-06-06T14:58:44.249000Z,24,31,32,NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007
Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94|q37-a38-40,0.79,discord_thread_heuristic,Adding h5view of data file to entry overview,issues,Is there something that I am doing wrong? If I can display the HDF5 file in DATA page is can't it just be replicated in the OVERVIEW page?,"only a quantity that will contain the reference to your data and in the root section def add the annotation,```class MySection(EntryData): m_def = Section(a_h5web=H5WebAnnotation(axes='time', signal='value')) value = Quantity( type=HDF5Dataset, unit='dimensionless', shape=[], a_h5web=H5WebAnnotation( long_name='power', errors='value_e' ), ) archive.data = MySection(value='{filename}#{path_to_data}') ```",,2025-04-03T09:05:50.630000Z,37,38,40,Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94
distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q5-a6-10,0.79,discord_thread_heuristic,distro-dev not running on latest develop,issues,How do I clear the uv cache?,``` uv cache prune # removes unused deps uv cache clean # removes the entire cache ```,,2025-04-01T12:49:10.385000Z,5,6,10,distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4
Adding users to an Oasis|2025-03-25T14:05:19.493000Z|**********|q6-a7-8,0.79,discord_thread_heuristic,Adding users to an Oasis,issues,"What should one do next? I have added those lines and then launched my Oasis from command line, and then asked a colleague to put in her browser the address which appears in my Oasis.... and her browser yields an error, apparently related to firewall settings or something of that kind. So, which is the correct procedure to allow other users to access ""our"" Oasis? Thanks a lot. Best. Elena","I think without this `allowed_users` every registed nomad account can access an oasis, if the required ports are accessible. The `allowed_users` list allows you to restrict this. See also the discussion here: https://discord.com/channels//",,2025-03-25T14:05:19.493000Z,6,7,8,Adding users to an Oasis|2025-03-25T14:05:19.493000Z|**********
"When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702|q15-a16-19",0.79,discord_thread_heuristic,"When registering a new account, the confirmation email does not arrive",issues,do you have the correct keycloak realm in your nomad.yaml in the root folder of your nomad-dev-distro?,``` keycloak: realm_name: fairdi_nomad_test ```,,2025-01-16T10:34:58.468000Z,15,16,19,"When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702"
Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q5-a6-7,0.79,discord_thread_heuristic,Uploads are getting directly stuck in nomad-distro-dev,issues,which sounds okay. Where do you reckon I can find more information where the error occurs. Thanks!,in your nomad.yaml you can try adjusting the console_log_level https://nomad-lab.eu/prod/v1/docs/reference/config.html#services_1,,2025-01-09T14:23:39.252000Z,5,6,7,Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9
Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99|q6-a9-11,0.79,discord_thread_heuristic,Could not find a version that satisfies the requirement networkx==3.3,issues,Can you share the full error message?,ahhh I see I'm on python 3.9 still I didn't try with newer but Amir did,,2024-08-19T11:26:40.185000Z,6,9,11,Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99
Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c|q35-a42-43,0.79,discord_thread_heuristic,Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis,issues,The original `1000:991` is using group 991 which on most distros should be the docker group. You can check what your docker group id is and maybe change the value. But was it a new install? Did is work before? Any changes made to the config? Or did it just suddenly break?,Wrong command. It has to be `upgrade-db` <https://jupyterhub.readthedocs.io/en/stable/howto/upgrading.html#upgrade-jupyterhub-database>. Please make a quick MR for these mistakes. You can set it to automerge.,,2024-03-14T16:47:18.591000Z,35,42,43,Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c
NotImplementedError for context.raw_path_exists method|2024-03-05T08:41:33.756000Z|b6ee07562e|q1-a4-6,0.79,discord_thread_heuristic,NotImplementedError for context.raw_path_exists method,issues,"Because of this difference, the pytests fail during the plugin testing, when using `archive.m_context.raw_path_exists(file_path)`. Can we harmonize these two methods for the classes `ServerContext` and `Context`?","https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/commit/2e8d24cd139cef1a9b9b7244c25c00b4333f28f1 I guess we only implemented the Context interface based on what was necessary. The ServerContext is the more complete one as this is used all the time in the app. The ClientContext is only used by, well, clients. E.g. when you do the ""nomad parse"" command, or the ArchiveQuery instantiates metainfo object froms the retrieved data. For the first case ""nomad parse"" you commit seems ok. For the second one, it does not hurt, but it also does not help, as the raw data is most likely not locally available. I guess it complements the existing `raw..` functions. Feel free to create an MR and merge this.",,2024-03-05T08:41:33.756000Z,1,4,6,NotImplementedError for context.raw_path_exists method|2024-03-05T08:41:33.756000Z|b6ee07562e
Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q14-a17-18,0.78,discord_thread_heuristic,Create multiple entry from parser or new entry in normalizer,issues,"for the solution! It's working now. Is there anything to keep in mind while using `create_archive`? The function is writing to the file systems directly, which I think is discouraged by Nomad. Additionally, does it work for normalizers as well?",You can find the (very) preliminary documentation on the `update_entry` context here: https://github.com/FAIRmat-NFDI/nomad-docs/blob/main/docs/howto/develop/normalizing.md,,2025-06-14T07:31:20.113000Z,14,17,18,Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab
Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80|q10-a13-14,0.78,discord_thread_heuristic,Publishing DFT calculations from RSPt,issues,> Can you give me a rough idea of the scope and user base of this code?,"We will have to ask our partner for whom we are doing the publishing. We are doing it for a common EU project [MaMMoS](https://github.com/mammos-project), for which we wanted to use NOMAD as a platform to openly store and publish our data. Within this project we have all kinds of data, including ab initio, simulations and measurement data. Our partner who is working with this code are from Uppsala university. We can definitely work together on the parser and can organise an initial meeting to discuss that with them.",,2025-06-03T09:29:11.049000Z,10,13,14,Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80
Pydantic validation errors in QueryEditQuantity|2025-05-27T13:12:35.697000Z|2f312b39c1|q10-a13-14,0.78,discord_thread_heuristic,Pydantic validation errors in QueryEditQuantity,issues,and I were trying to debug. Maybe can help us identify the issue?,"I found the issue. In the models for operators, the name of the field is `op` while the actual name of the operator is defined as an alias (for example [here](https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/app/v1/models/models.py?ref_type=heads#L118) ). When `model_dump()` is used, it defaults to using `op` as the key.",,2025-05-27T13:12:35.697000Z,10,13,14,Pydantic validation errors in QueryEditQuantity|2025-05-27T13:12:35.697000Z|2f312b39c1
Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q128-a131-132,0.78,discord_thread_heuristic,Unexpected error in the research app,issues,"- You mentioned that you are here using `nomad-distro-dev`, is this correct?","I am using that same use case (headless server for nomad-distro-dev), and added a note to the readme about the required port forwardings: https://github.com/FAIRmat-NFDI/nomad-distro-dev/blob/21a1566fc1d8ced3757d2d2b60ecea64f24f4b58/README.md?plain=1#L311",,2025-02-28T07:57:57.339000Z,128,131,132,Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b
Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41|q18-a19-21,0.78,discord_thread_heuristic,Updating distro-dev after pydantic update,issues,"Just a quick question about the pydantic upgrade in advance: you mentioned that you need to possibly update your plugins (and you list some simulation plugins there). What updates do you expect are necessary, just some dependencies in the toml, or actually something in the code?","It'd be something like this, but I didn't notice any issues in any of the plugins used in our deployments so I don't think there'd be any changes necessary https://github.com/nomad-coe/electronic-parsers/commit/2340fb753390cd15b9e9feaa96d6af452fca7e41 Or do you mean on your end to use the plugins? If it's listed as a `>=some_version`, uv will automatically resolve to a greater version that is compatible. Otherwise it'll show an error message and updating the toml will fix it",,2025-02-10T08:34:08.970000Z,18,19,21,Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41
k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525|q8-a11-12,0.78,discord_thread_heuristic,k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing,issues,"4 - https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/normalizing/method.py?ref_type=heads#L370 k_mesh.dimensionality will be 3 for all of 1x1x1, 1x1x6, and 20x20x1 grids... is this expected and it is the way the metainfo definition is intended?","In the second case we could use the normalized reciprocal cell, get list of symmetry operations and extend the points to all_points, while double checking that the weights/multiplicities match. And only from `all_points` we could try guessing the grid (preferably after also checking the uniform spacings).",,2024-07-12T10:18:27.642000Z,8,11,12,k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525
HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q83-a86-87,0.78,discord_thread_heuristic,HDF5 in NOMAD,issues,Would sharing a dataset with you help?,"The official and staging deployments lag behind the latest fixes but it will be available in the next release. It should be available though on the develop deployment `https://nomad-lab.eu/prod/v1/develop/gui/about/information`, but I have to warn about the features are experimental there and not stable",,2024-05-27T13:17:35.844000Z,83,86,87,HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57
nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52|q26-a32-33,0.77,discord_thread_heuristic,nomad-oasis can not stablely be started,issues,what should be the nomad-oasis look like if it is successfully deployed? Does it have a web page interface when access it through web browser? can you show me the result if nomad-oasis is successfully deployed?,"deployment_url: ""https://localhost/api""",,2025-07-17T08:58:19.106000Z,26,32,33,nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52
Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q25-a26-27,0.77,discord_thread_heuristic,Create multiple entry from parser or new entry in normalizer,issues,1. How I need to create reference between the `ChildEntryArchive` and `ParentEntryArchive`?,2. Does `ParentEntryArchive` keep track of the `ChildEntryArchive` list? Or How to I create the reference manually? (back to question 1),,2025-06-14T07:31:20.113000Z,25,26,27,Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab
Recreating deleted upload on OASIS|2025-06-05T13:46:57.198000Z|e887b8bdd4|q2-a3-4,0.77,discord_thread_heuristic,Recreating deleted upload on OASIS,issues,"Hrmm, okay, I guess the main issue is if there are references to this upload and we then create a new one. Is there a way to recreate and upload with a specified ID?","The way to recreate everythign with an existing ID would be to move the correctly named folder into .volumes, re-add the data (uploads + entries) into MongoDB from a backup, and run `nomad admin uploads index <upload-id>` to get ES populated with data about the entries.",,2025-06-05T13:46:57.198000Z,2,3,4,Recreating deleted upload on OASIS|2025-06-05T13:46:57.198000Z|e887b8bdd4
Include PDF in Oasis|2025-06-05T10:04:09.333000Z|d38858399c|q0-a1-2,0.77,discord_thread_heuristic,Include PDF in Oasis,issues,if it is possible to generate pdfs in my parser and then parse them into my oasis so that i am able to access them later on. Is that in any way possible? If so how would my schema need to look like ?,Absolutely. You can attach they python code which generates the PDF to your parser or normalize method and use the `archive.m_context.raw_file()` context to create the file. If you want the PDFs to be searchable you can either add them as a file edit quantity in an ELN or create a simple matching parser which matches them. If you tell me a bit more about your use case I might be able to give you some advice on which way to go.,,2025-06-05T10:04:09.333000Z,0,1,2,Include PDF in Oasis|2025-06-05T10:04:09.333000Z|d38858399c
Unexpected error from elastic search when sorting search results by column|2025-04-14T07:58:16.455000Z|6aaa0f990a|q5-a6-7,0.77,discord_thread_heuristic,Unexpected error from elastic search when sorting search results by column,issues,```Any idea on what might be causing this issue?,"Interestingly, it seems that for fields that do not use schema (such as `'mainfile'` or `'entry_id'`) ""load more"" works regardless of the sorting order. Or it may be related not to the schema but to the fact that all entries have `mainfile` and `entry_id`, while not all of them have `___file_time`.",,2025-04-14T07:58:16.455000Z,5,6,7,Unexpected error from elastic search when sorting search results by column|2025-04-14T07:58:16.455000Z|6aaa0f990a
Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q10-a11-12,0.77,discord_thread_heuristic,Uploads are getting directly stuck in nomad-distro-dev,issues,"for your answers, but I am still not really sure what I should do. I only have the 3 infrastructure containers (elastic, rabbitmq, mongo) running and don't see anything special in their logs. The rest is running in the terminal. How can I check if the worker is running properly?","You can check the status of the services by running `docker ps`. This will show ""STATUS"" column that you can check.",,2025-01-09T14:23:39.252000Z,10,11,12,Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9
Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4|q53-a61-62,0.77,discord_thread_heuristic,Can't get dev installation to work,issues,could this be an issue?,If you have an idea on how the docs could be made clearer I'm sure that Ahmed would appreciate a PR here: https://github.com/FAIRmat-NFDI/nomad-distro-dev 😊,,2024-12-16T09:34:40.791000Z,53,61,62,Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4
Error while running the Tutorial|2024-11-19T09:00:42.371000Z|55a178e6ca|q14-a16-17,0.77,discord_thread_heuristic,Error while running the Tutorial,issues,"When running a north tool for the first time, the docker image will be downloaded. This takes a few minutes, and you might get a 504 timeout error. But if you retry after a couple minutes, the tool should start out fine, like happened here. is there any way to pre-download the images during the distribution bootup? That would be nice, at least for the most used tools like jupyter.","As far as I know, the test Oasis runs on k8. The build is based on the `test-oasis` brach of `nomad-distro`: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-distro/-/tree/test-oasis?ref_type=heads. I think the deployment command is somewhere in here: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-distro/-/blob/test-oasis/.gitlab-ci.yml?ref_type=heads, but you should ask for details.",,2024-11-19T09:00:42.371000Z,14,16,17,Error while running the Tutorial|2024-11-19T09:00:42.371000Z|55a178e6ca
Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8|q3-a4-5,0.77,discord_thread_heuristic,Weird GUI data info,issues,"But I agree in some cases it looks strange that only the first word is capitalized, maybe all words should be capitalized. Would that be better?","Well, I am thinking on usage: when you look into the metainfo tab, at the end you want to resolve some path `run.calculation.band_structure_electronic`, which with these changes is not clear at all.",,2024-03-01T12:53:07.703000Z,3,4,5,Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8
nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52|q46-a50-51,0.76,discord_thread_heuristic,nomad-oasis can not stablely be started,issues,"regarding the ssl access, is it possible that I sccessfully access your services without the ssl, through http directly?",yep the `app` https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/docker-compose.yaml#L93 is separate from `proxy` (nginx) https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/docker-compose.yaml#L190,,2025-07-17T08:58:19.106000Z,46,50,51,nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52
Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94|q9-a11-13,0.76,discord_thread_heuristic,Adding h5view of data file to entry overview,issues,However this does not change anything in the overview section. Is there something that I am missing?,"an `HDF5Reference` quantity, you can populate a path to the dataset in the hdf5 file. In the GUI, under the DATA tab, you can then open the specified dataset in a new lane on the right. https://nomad-lab.eu/prod/v1/docs/howto/customization/hdf5.html#hdf5reference here you can find an example",,2025-04-03T09:05:50.630000Z,9,11,13,Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94
Specialized Scatterplots with JMESPath syntax|2025-03-18T10:02:19.551000Z|c675628b74|q3-a7-8,0.76,discord_thread_heuristic,Specialized Scatterplots with JMESPath syntax,issues,Maybe there is an extra whitespace?,"Is it expected that the elemental composition from results/material cannot be used as a marker in a scatterplot? I am trying to get a plot with the mass_fraction of a certain element as marker color in a scatterplot, but it doesn't seem to work. I can use e.g. `results.material.n_elements` or use a label e.g. `results.material.elements[-1]` but if I try to use `results.material.elemental_composition[0].element` I get as marker ""undefined"" and if I change from `element` to `mass_fraction` I get an empty plot.",,2025-03-18T10:02:19.551000Z,3,7,8,Specialized Scatterplots with JMESPath syntax|2025-03-18T10:02:19.551000Z|c675628b74
Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q12-a16-17,0.76,discord_thread_heuristic,Unexpected dependency version on NOMAD develop,issues,Or should I use the same dynamic settings depending on the python version in `nomad-schema-plugin-simulation-workflow` as in `nomad-parser-plugin-atomistic`?,The versions on develop is set here: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-distro/-/blob/main/requirements.txt?ref_type=heads#L156,,2025-01-24T11:39:53.165000Z,12,16,17,Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c
Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a|q12-a54-55,0.76,discord_thread_heuristic,Issue Starting Nomad Server: Connection Problems with Services,issues,Can you try the following to see if you're able to establish a connection with elasticsearch using the python client?,"So I think that Elasticsearch is running fine, but could be that somehow WSL is not letting some of the connections through. I think you are following the `nomad-distro-dev` setup in which Elasticsearch is run through docker, and then the app+worker are run as a separate python process that is outside docker. Is this right?",,2025-01-11T15:49:58.163000Z,12,54,55,Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a
Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a|q30-a54-55,0.76,discord_thread_heuristic,Issue Starting Nomad Server: Connection Problems with Services,issues,Can you try printing out the auth and host?,"So I think that Elasticsearch is running fine, but could be that somehow WSL is not letting some of the connections through. I think you are following the `nomad-distro-dev` setup in which Elasticsearch is run through docker, and then the app+worker are run as a separate python process that is outside docker. Is this right?",,2025-01-11T15:49:58.163000Z,30,54,55,Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a
Writing tests for plugin and parsing|2024-10-30T11:44:42.436000Z|112339db3e|q1-a3-5,0.76,discord_thread_heuristic,Writing tests for plugin and parsing,issues,`nomad.search.QueryValidationError: results.eln.lab_ids is not a doc quantity` why is it not a doc quantity?,"So during your test you are doing an API call or using the `nomad.search` module I guess? I don't think that by default any of the search infrastructure is setup for tests, as typically you only work with the data contained in the archive itself. That is probably why you are getting that error. But I could probably say more if I see the test code that you are trying to run. I think the safest and fastest choice for tests is to mock some of the API/search calls. You can learn more about mocking with pytest here: https://docs.pytest.org/en/stable/how-to/monkeypatch.html",,2024-10-30T11:44:42.436000Z,1,3,5,Writing tests for plugin and parsing|2024-10-30T11:44:42.436000Z|112339db3e
Safe type casting causes error converting json null to numpy float|2024-09-05T07:25:05.497000Z|0ba81e2463|q2-a24-25,0.76,discord_thread_heuristic,Safe type casting causes error converting json null to numpy float,issues,Can you have a look?,if then all `null` values will break we can not update,,2024-09-05T07:25:05.497000Z,2,24,25,Safe type casting causes error converting json null to numpy float|2024-09-05T07:25:05.497000Z|0ba81e2463
Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7|q25-a29-30,0.76,discord_thread_heuristic,Publishing an entry from Oasis to Central NOMAD,issues,"So my question here: how is it different (if it really is) in this case to transfer the metadata to the central NOMAD, compared to only the `results` section let's say ?",I work in a project called MaMMoS where we have to share data of magnetic materials from our Oasis to central-NOMAD. We have set up the Oasis and a distro-dev to test code for first parsers (https://github.com/MaMMoS-project/nomad-mammos-plugin).,,2024-08-29T07:38:20.591000Z,25,29,30,Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7
Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765|q22-a40-41,0.76,discord_thread_heuristic,Problem when `docker compose up -d` an Oasis,issues,Is there anything unexpected in the GUI response?,whats the output when you run `docker ps`?,,2024-08-02T08:23:26.436000Z,22,40,41,Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765
Link to entries broken when changing the Oasis Docker image|2024-07-05T08:07:55.017000Z|bbd7de0589|q8-a25-26,0.76,discord_thread_heuristic,Link to entries broken when changing the Oasis Docker image,issues,Is this a normal behaviour or am I doing something wrong when I am trying to visualize the modifications I made on my image by deleting the repo and the Docker image systematically ?,"It is possible see the entries and have the files deleted. Depending on the view the GUI shows information from mongodb or from elasticsearch. With our default docker-compose based install the mongo and elastic databases are put into a docker volume. If you use the same docker installation, but removed all the files, the docker volumes will still be there. If you want to delete everything, you also need to delete the docker volumes. By default the volumes are called `nomad_oasis_elastic`, `nomad_oasis_mongo` and `nomad_oasis_rabbitmq`. With `docker volume rm ..` you can delete volumes. If you routinely want to wipe all data in your NOMAD installation, you can also use the nomad cli from within one of the containers:",,2024-07-05T08:07:55.017000Z,8,25,26,Link to entries broken when changing the Oasis Docker image|2024-07-05T08:07:55.017000Z|bbd7de0589
Issues with referencing quantities|2024-05-28T08:11:58.541000Z|c3f37c1674|q27-a60-61,0.76,discord_thread_heuristic,Issues with referencing quantities,issues,How is it using `QuantityReference`?,Could you check if some part of your code modifies the shape of `KMesh.points`? In the original definition it seems to not have any shape. Otherwise it could be some deeper problem in the wrong shape being somehow set.,,2024-05-28T08:11:58.541000Z,27,60,61,Issues with referencing quantities|2024-05-28T08:11:58.541000Z|c3f37c1674
nomad.yaml: issue with new plugin mechanism|2024-05-22T15:47:19.297000Z|5cd4333fd8|q2-a6-7,0.76,discord_thread_heuristic,nomad.yaml: issue with new plugin mechanism,issues,"- Furthermore, the `plugins/include` section of the `nomad.yaml` apparently is an ""include only"". (I wasn't aware of that tbh!) The only way to get our plugin running was to omit it completely, which includes every available plugin. Then it is correctly shown in the metainfo browser or the about/information page. - We have not found a way yet to use it with the include section. Maybe somebody knows more?","Ok, it seems things moved around after Lauri's changes. Joe has a branch where he took notes on how to properly clone a plugin, and set it up with the local installation: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/1813",,2024-05-22T15:47:19.297000Z,2,6,7,nomad.yaml: issue with new plugin mechanism|2024-05-22T15:47:19.297000Z|5cd4333fd8
NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007|q17-a18-20,0.75,discord_thread_heuristic,NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12,issues,"I tested the [nomad-distro-template](https://github.com/FAIRmat-NFDI/nomad-distro-template), but it seems to download/install/set-up the oasis version 1.3.15? As I looked in the docker-compose and referring images: the [main](https://github.com/fairmat-nfdi/nomad-distro-template/pkgs/container/nomad-distro-template) does not contain a specific version number. How do I know with NOMAD Oasis version is in the ""main-package""?","You can specify it as a version constraint in dependencies: https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/pyproject.toml#L23 `nomad-lab[parsing, infrastructure]>=1.3.16` or",,2025-06-06T14:58:44.249000Z,17,18,20,NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007
Error 500 when creating a new upload|2025-04-07T07:48:07.283000Z|f79556187d|q18-a20-21,0.75,discord_thread_heuristic,Error 500 when creating a new upload,issues,"Subsidiary question: when the IT person followed the installation steps, they could not run the `docker compose pull` apparently and faced a `permission denied` error. They could only create the image with `docker build . -t oasis-docker-image:main --no-cache --target final`. Have you heard of any similar problem with other users ?",Step 3 from https://github.com/AddMorePower/oasis-docker-image?tab=readme-ov-file#for-a-new-oasis should fix this,,2025-04-07T07:48:07.283000Z,18,20,21,Error 500 when creating a new upload|2025-04-07T07:48:07.283000Z|f79556187d
[Tutorial 13] Error docker github|2024-07-22T16:29:11.550000Z|f5445b9ee8|q2-a11-12,0.75,discord_thread_heuristic,[Tutorial 13] Error docker github,issues,Can you rerun it and check if the issue persists?,runner curl https://gitlab-registry.mpcdf.mpg.de/v2/,,2024-07-22T16:29:11.550000Z,2,11,12,[Tutorial 13] Error docker github|2024-07-22T16:29:11.550000Z|f5445b9ee8
HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q20-a22-23,0.75,discord_thread_heuristic,HDF5 in NOMAD,issues,"or is there a way to fix a ""path"", https://nomad-lab.eu/prod/v1/staging/docs/howto/customization/hdf5.html#:~:text=path%20%3D%20%27external.h5%23path/to/data%27, in the definition of the Quantity? Like when I make a Quantity(type=HDFReference), can I in my schema fix that whenever Nomad writes this data it directs it to HDF5 in my specfied path?",1. `HDF5Reference` points to a specific dataset within a hdf5 filepath you have to pass when parsing / reading.,,2024-05-27T13:17:35.844000Z,20,22,23,HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57
Problem with search function in NOMAD Oasis|2024-05-17T17:04:47.074000Z|6241db1751|q138-a140-141,0.75,discord_thread_heuristic,Problem with search function in NOMAD Oasis,issues,"I have no idea why this solution works, but it does. Maybe you have an idea?","It's good that you found a solution with which you can proceed, but it is clear that we need to improve the `search` interface. I will create a GitLab issue to add a possibility to use plain dictionaries as the query, and also to fix the problem with the custom quantities not being visible during reprocessing.",,2024-05-17T17:04:47.074000Z,138,140,141,Problem with search function in NOMAD Oasis|2024-05-17T17:04:47.074000Z|6241db1751
Problem with `nomad-distro-dev`|2024-10-24T15:21:28.021000Z|3f0f258a83|q8-a23-25,0.74,discord_thread_heuristic,Problem with `nomad-distro-dev`,issues,"Could you check which version of `typing-extensions `you have? Can be done e.g. with `pip list`. The nomad package requires `typing-extensions==4.12.2`, but it could be that e.g. some of the plugins downgrade on your machine. Any further ideas here?","Created a MR for this https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2181 Yep, that's `4.4.0` for me. I'll keep that in mind, we might need to do some updating on the `nomad-parser-plugins-atomistic`.",,2024-10-24T15:21:28.021000Z,8,23,25,Problem with `nomad-distro-dev`|2024-10-24T15:21:28.021000Z|3f0f258a83
Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q5-a10-11,0.74,discord_thread_heuristic,Adapting existing Oasis to new plugin mechanism,issues,"3. I also use baseclasses. I remember there was a discussion about this before here in Discord with . I think I have to use a different NOMAD image, but I am not quite sure which one and in which file I have to change it?","4. If the plugins are defined in the entrypoints, they will be automatically loaded, so there's nothing to be added to the `nomad.yaml` file. The documentation [here](https://nomad-lab.eu/prod/v1/develop/docs/howto/plugins/plugins.html#plugin-entry-points) might be helpful",,2024-10-01T12:39:26.289000Z,5,10,11,Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459
Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q71-a76-77,0.74,discord_thread_heuristic,Adapting existing Oasis to new plugin mechanism,issues,"In the end this button is nothing more than a temporary boolean+save. Like said, a checkbox plus pressing save would be similar. The button would probably also come along as a ""EditQuantity"" component. The backend part (e.g. setting it to false again) would still be done with in `normalize` during processing. Something like this should not be to hard to add. I also guess, this is exactly what has in his code, right?","I now commented on <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1610> and <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1955>. The relevant people here, should be part of the respective issues. My idea would be to work on the ""button"" and on the action you do (e.g. creating entries, updating entries) together and to finally document such a use-base with a how-to in the documentation.",,2024-10-01T12:39:26.289000Z,71,76,77,Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459
Fail to launch jupyter hub on oasis|2024-08-28T12:48:11.408000Z|941875913b|q3-a8-9,0.74,discord_thread_heuristic,Fail to launch jupyter hub on oasis,issues,"Unfortunately I am no longer on site with the users so will take a bit of time to check this but I'll ask the IT admin. For my understanding, is it that it tries to mount these folders in the jupyter container and then can't access them?",You can also run the `nomad admin uploads integrity --missing-storage` command in the container to check if there are uploads with missing (or inaccesible) directories.,,2024-08-28T12:48:11.408000Z,3,8,9,Fail to launch jupyter hub on oasis|2024-08-28T12:48:11.408000Z|941875913b
Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765|q11-a16-17,0.74,discord_thread_heuristic,Problem when `docker compose up -d` an Oasis,issues,"In the app section of the docker compose file, instead of your custom image, could you try with our official one?",For now you can try changing nomad-fair:latest to nomad-fair:v1.3.4 here https://github.com/Guillaume-Gaisne/AMP_internal_Oasis_image/blob/main/Dockerfile,,2024-08-02T08:23:26.436000Z,11,16,17,Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765
Nexus Search very slow for larger entry numbers|2025-03-27T17:23:32.159000Z|ea977dcfa4|q17-a18-20,0.73,discord_thread_heuristic,Nexus Search very slow for larger entry numbers,issues,"That makes sense, that can probably also speed up other widgets, as for histograms/terms you even only need one search quantity, no?","In the case of histogram/terms the code is not even looking at the search quantity values directly, but we instead use the internal aggregation calls from ES. So these widgets don't have the same performance problem. This indeed seems to help a lot. I drafted this solution here as a starting point: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2408",,2025-03-27T17:23:32.159000Z,17,18,20,Nexus Search very slow for larger entry numbers|2025-03-27T17:23:32.159000Z|ea977dcfa4
Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q15-a18-19,0.73,discord_thread_heuristic,Uploads are getting directly stuck in nomad-distro-dev,issues,"ok thanks, I have the devcontainer extension already installed in my VS Code, but I am not quite sure how to use it. Is there a straightforward approach or a page where this is described?","Great, I think I am getting closer to the solution. I reopened the project in a devcontainer. But now my question is in which terminal should I enter the `uv run poe start` command. In the bash terminal it does not work. (Sorry for my stupid questions, but I am really not an expert in this stuff). Really appreciate your help.",,2025-01-09T14:23:39.252000Z,15,18,19,Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9
Customized nexus subsection is not rendered|2024-07-17T13:19:45.414000Z|d8cdf78efe|q2-a5-6,0.73,discord_thread_heuristic,Customized nexus subsection is not rendered,issues,Am I missing to add anything configuration parameters in the nomad.yaml config file? Or this is a bug?,services: api_host: 'localhost' api_base_path: '/fairdi/nomad/latest' oasis: is_oasis: true uses_central_user_management: true north: jupyterhub_crypt_key: '0a6d514e175a9ee6cbc525561afd18598f4fc570ab6e5fa26402e8721bb1308c' enabled: true meta: deployment: 'oasis' deployment_url: 'https://my-oasis.org/api' maintainer_email: 'me mongo: db_name: nomad_oasis_v1 elastic: entries_index: nomad_oasis_entries_v1 materials_index: nomad_oasis_materials_v1,,2024-07-17T13:19:45.414000Z,2,5,6,Customized nexus subsection is not rendered|2024-07-17T13:19:45.414000Z|d8cdf78efe
k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525|q27-a30-31,0.73,discord_thread_heuristic,k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing,issues,2. The other is *symmetry*. I think that unfolding the grid goes beyond the scope of a fix (though we may consider it as a feature in the future). I would see to leverage the *multiplicities* here. You mentioned that for OpenMX these would trivial to set (all 1 I assume)?,"4. Very astute observation! I'd say it depends: if a 2D system is modeled in 3D, the extra dimension should not count. If it is truly 3D, then the dimension with only 1 k-point may indeed be the precision bottleneck, and `k_line_density` should point that out. Typically codes are pretty clear on a system's dimensionality, but some (e.g. VASP) always defaults to 3D, or the user does not leverage the dimension control well... Luckily we have a package that ascertains the dimensionality independently: MatID by Now it just becomes a question of the normalization order: `MethodNormalizer` has to come after. We might also call the analyzer 2x, but it's a pretty expensive routine.",,2024-07-12T10:18:27.642000Z,27,30,31,k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525
Link to entries broken when changing the Oasis Docker image|2024-07-05T08:07:55.017000Z|bbd7de0589|q22-a25-26,0.73,discord_thread_heuristic,Link to entries broken when changing the Oasis Docker image,issues,"So if the files folder has been deleted, how is it that the entries in the Explore page are still showing up ?","It is possible see the entries and have the files deleted. Depending on the view the GUI shows information from mongodb or from elasticsearch. With our default docker-compose based install the mongo and elastic databases are put into a docker volume. If you use the same docker installation, but removed all the files, the docker volumes will still be there. If you want to delete everything, you also need to delete the docker volumes. By default the volumes are called `nomad_oasis_elastic`, `nomad_oasis_mongo` and `nomad_oasis_rabbitmq`. With `docker volume rm ..` you can delete volumes. If you routinely want to wipe all data in your NOMAD installation, you can also use the nomad cli from within one of the containers:",,2024-07-05T08:07:55.017000Z,22,25,26,Link to entries broken when changing the Oasis Docker image|2024-07-05T08:07:55.017000Z|bbd7de0589
Missing help text in some EditQuantity(s)|2024-06-28T09:53:34.561000Z|07e5e54ac1|q0-a10-11,0.73,discord_thread_heuristic,Missing help text in some EditQuantity(s),issues,Is there a reason by some EditQuantity(s) do not have help-text icon to show the description?,https://nomad-lab.eu/prod/v1/staging/gui/dev/editquantity,,2024-06-28T09:53:34.561000Z,0,10,11,Missing help text in some EditQuantity(s)|2024-06-28T09:53:34.561000Z|07e5e54ac1
errors in the gui  whenquantitities are of type=np.float64 and no unit given|2024-03-01T12:38:57.676000Z|289ca000c4|q15-a16-19,0.73,discord_thread_heuristic,errors in the gui  whenquantitities are of type=np.float64 and no unit given,issues,I think this is also for But he is going on vacation for two weeks. How critical is this to you?,"it's is quite a bug since a `NumberEditQuantity` without a unit is used almost in all schemas, I wold say I can imagine. I try to look into it. I merged a fix. https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/1702",,2024-03-01T12:38:57.676000Z,15,16,19,errors in the gui  whenquantitities are of type=np.float64 and no unit given|2024-03-01T12:38:57.676000Z|289ca000c4
FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q70-a72-76,0.72,discord_thread_heuristic,FHIaims parsing issues,issues,"You should use a published package, using git dependencies won't work since the gui would not be available. do you know when the fix was made?","In that case, in the pyproject.toml you can set the lower bound for nomad-lab to 1.3.16 which was released a month ago. `""nomad-lab[parsing, infrastructure]>=1.3.16"",` OK, building it with ""nomad-lab[parsing, infrastructure]>=1.3.16"" works. To be honest I was assuming that if I don't explicitly specify package version like for nomad-lab[parsing, infrastructure] it will already give me the latest released version? Anyway, with ""nomad-lab[parsing, infrastructure]>=1.3.16"" and develop electronic-parsers (latest version in https://github.com/ondracka/nomad-distro-template) I'm still getting the FHIaims normalizing error from above.",,2025-07-24T12:22:12.739000Z,70,72,76,FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93
Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3|q18-a19-20,0.72,discord_thread_heuristic,Solve `mongo_user_group` warning in the application startup,issues,So the right way is python3 merge.py --host localhost?,"Don't forget the other options from the example, `--db-name nomad_v1` and `--action ..`",,2025-07-16T15:57:41.003000Z,18,19,20,Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3
Modify the archive structure?|2025-07-14T08:02:22.296000Z|0a0bcf7fd2|q5-a6-7,0.72,discord_thread_heuristic,Modify the archive structure?,issues,Anyway could you expain more about the last point? What does it mean that the latest updates allows to search in a different way the quantities?,You can add them as searchable quantities in apps: https://nomad-lab.eu/prod/v1/staging/docs/howto/plugins/apps.html#loading-quantity-definitions-into-an-app,,2025-07-14T08:02:22.296000Z,5,6,7,Modify the archive structure?|2025-07-14T08:02:22.296000Z|0a0bcf7fd2
Limiting the Oasis workers to save memory|2025-04-09T08:09:43.694000Z|70654d8f14|q2-a8-9,0.72,discord_thread_heuristic,Limiting the Oasis workers to save memory,issues,Cool! Any idea what could be a reasonable value for this?,"I guess the max memory per child has a similar effdect. But, there is another important setting. This is the one i was mentioning once. This one specifically mentiones the use case of memory leaks: https://docs.celeryq.dev/en/latest/userguide/workers.html#max-tasks-per-child-setting",,2025-04-09T08:09:43.694000Z,2,8,9,Limiting the Oasis workers to save memory|2025-04-09T08:09:43.694000Z|70654d8f14
Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c|q38-a39-40,0.72,discord_thread_heuristic,Mongo DB collection warning is weird,issues,: Could you share the migration script with Laurenz?,Here is the snippet: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/snippets/181,,2025-03-07T10:17:12.565000Z,38,39,40,Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c
Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5|q33-a39-40,0.72,discord_thread_heuristic,Protected Oasis - Restrict GUI/API Access only for Login-User,issues,"But anyway, that's not the point: the oasis has a ""public search"" functionality - also the API giving you all information about (meta)data without an account. This ""public sharing"" feature is nice and good for the official NOMAD instance, but not adequate for ""data sharing between several groups in a protected environment"". I want to restrict the API call and ""gui/search"" functionality only known users to promoted ""sharing (not publishing) with the Collaborative Research Centre"". Does this help what I am aiming for?","👍 Thank you! Now, I understand why `allowed_users` for an oasis should to the same thing. I've not be aware of, that this also will restrict the API call. I will check that in the file `nomad/app/v1/routers/auth.py` But as you facing the same problem as I am, maybe a more general approach without relying on nomad.yaml would be great. Thanks again for the advice 🙂",,2025-02-28T08:48:27.056000Z,33,39,40,Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5
Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q136-a137-138,0.72,discord_thread_heuristic,Unexpected error in the research app,issues,"So to ruturn and close the issue we installed a 1.3.16dev170 this version allows for the correct visualization of the celsius units in the research app, i.e. the bug is already corrected in this version?",Moreover when I try to take upto date my local repository in the merge I take the conflict in the pyproject,,2025-02-28T07:57:57.339000Z,136,137,138,Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b
search widget showing more then 5 hits?|2025-02-24T08:50:06.398000Z|7eb3b96db9|q13-a14-15,0.72,discord_thread_heuristic,search widget showing more then 5 hits?,issues,```Should the suggestions work in the Filter Menu?,There was an issue with the suggestions that was fixed recently in `develop`. The fix is also available in 1.3.14 which is released today.,,2025-02-24T08:50:06.398000Z,13,14,15,search widget showing more then 5 hits?|2025-02-24T08:50:06.398000Z|7eb3b96db9
Customization of an app|2025-02-24T07:24:54.477000Z|a04c15214a|q56-a57-58,0.72,discord_thread_heuristic,Customization of an app,issues,"Ok thanks for the help but in absence of a fix I opted for a different solution, i.e. describe every step as an entrydata and than link every step through a reference. The last point that remain is: when the filters are applied there is a way to concatenate various filters referred to the same quantity with an ""and"" option instead of an or?",currently no way to change the default query mode. This is mostly an oversight from our part. Let me create a feature request out of this and we try to get this into release 1.3.15.,,2025-02-24T07:24:54.477000Z,56,57,58,Customization of an app|2025-02-24T07:24:54.477000Z|a04c15214a
Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a|q28-a29-30,0.72,discord_thread_heuristic,Issue Starting Nomad Server: Connection Problems with Services,issues,"So the connection to Elasticsearch seems to be working, what could be the problem then? What could I check during the server startup process?","The url could possibly be different, in `packages/nomad-FAIR/infrastructure.py` there's a function called `setup_elastic`",,2025-01-11T15:49:58.163000Z,28,29,30,Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a
Unit Registry|2024-12-19T15:34:54.028000Z|c5631a8352|q10-a11-12,0.72,discord_thread_heuristic,Unit Registry,issues,"Now its working. is there ""Percentage"" as unit there?","Anyway, I am looking here for the list: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/units/default_en.txt?ref_type=heads",,2024-12-19T15:34:54.028000Z,10,11,12,Unit Registry|2024-12-19T15:34:54.028000Z|c5631a8352
Authorization issue?|2024-12-03T14:06:17.738000Z|83c2b6ccd7|q1-a9-10,0.72,discord_thread_heuristic,Authorization issue?,issues,Does/did anyone else have this issue?,Didn't solve it unfortunately. I'll try a system reboot.,,2024-12-03T14:06:17.738000Z,1,9,10,Authorization issue?|2024-12-03T14:06:17.738000Z|83c2b6ccd7
Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff|q26-a27-28,0.72,discord_thread_heuristic,Permission denied when creating new Jupyter Notebook in uploads folder,issues,more about how/where are you trying to create the notebook? Are you trying to upload it inside an existing upload? Are you trying to create it during processing? Are you trying to create it inside one of the NORTH tools?,"I started the JupyterLab in the NORTH Tools and then navigated there to the upload folder (in the left menu) and then in the upload folder clicked right -> ""New Notebook"". And then the eroor appears: `Permission denied: uploads/xxx-test-north-788ekQCHQh2G83Atjn-nCw/Untitled.ipynb`",,2024-11-20T11:04:36.071000Z,26,27,28,Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff
Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q71-a76-77,0.72,discord_thread_heuristic,Memory error when I build my container,issues,What's the output for ` docker image inspect ghcr.io/paulogithb/nomad-oasis-cemes:main`?,Error: No such image: ghcr.io/paulogithb/nomad-oasis-cemes:main,,2024-11-15T09:44:36.042000Z,71,76,77,Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d
Unintuitive behavior of entry selection after SelectAll|2024-11-06T09:46:27.241000Z|978a2b109e|q2-a3-4,0.72,discord_thread_heuristic,Unintuitive behavior of entry selection after SelectAll,issues,"Yes, that is indeed unexpected. Could you create an issue about this in Gitlab?",Done: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2191,,2024-11-06T09:46:27.241000Z,2,3,4,Unintuitive behavior of entry selection after SelectAll|2024-11-06T09:46:27.241000Z|978a2b109e
# not escaped in API calls|2024-10-30T10:45:15.019000Z|64a53a1248|q3-a4-5,0.72,discord_thread_heuristic,# not escaped in API calls,issues,That might very well be the case. Could you create an issue about that in Gitlab?,https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2183,,2024-10-30T10:45:15.019000Z,3,4,5,# not escaped in API calls|2024-10-30T10:45:15.019000Z|64a53a1248
nomad parse not working in 13.10|2024-10-28T15:43:27.988000Z|c3418adc85|q16-a17-18,0.72,discord_thread_heuristic,nomad parse not working in 13.10,issues,Not sure what this mean. Was this the MR that broke this or is this a MR that aims to fix it?,https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2130,,2024-10-28T15:43:27.988000Z,16,17,18,nomad parse not working in 13.10|2024-10-28T15:43:27.988000Z|c3418adc85
nomad parse not working in 13.10|2024-10-28T15:43:27.988000Z|c3418adc85|q25-a26-27,0.72,discord_thread_heuristic,nomad parse not working in 13.10,issues,"This was merged, how did you test this?","I ran `nomad parse tests/data/test_sample.archive.yaml --show-archive > normalized.archive.json` in my plugin environment, which has 'nomad-lab 1.3.11.dev19+g5dacb5963.d20241022 /home/<USER>/nomad' installed",,2024-10-28T15:43:27.988000Z,25,26,27,nomad parse not working in 13.10|2024-10-28T15:43:27.988000Z|c3418adc85
Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q3-a4-5,0.72,discord_thread_heuristic,Adapting existing Oasis to new plugin mechanism,issues,"2. Previously my highest folder in the structure was called “UMR_schemas”. Now it is called “schema_packages”. I would prefer to use “schema_packages” but I am not sure If this will lead to a problem, because the already existing archives refer to the definitions in “UMR_schemas…”. Am I right in thinking that this is a problem? And how can I solve it? I think I have read something about aliases in the docs (https://nomad-lab.eu/prod/v1/docs/howto/plugins/schema_packages.html) . Should I use those in this case?","I also already prepared my own image (https://github.com/AG-SEK/nomad-oasis-perolab-umr-image) using the other Github template. At the time of the recording of the tutorial there was still a “plugins.txt” file in the image repository. If I understood it correctly now instead of adding the plugins to the “plugin.txt” file, one needs to write them in the plugins list in the “pyproject.toml”. I did it and it seems to work. But I have some more issues related to the image:",,2024-10-01T12:39:26.289000Z,3,4,5,Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459
Change Affiliation|2024-09-10T06:49:20.188000Z|ab02169519|q0-a1-2,0.72,discord_thread_heuristic,Change Affiliation,issues,"Back when I first registered on NOMAD, I did not enter the full name of my affiliation and the address. How can I change it?",this is something also commented in this issue: https://github.com/nomad-coe/nomad/issues/112,,2024-09-10T06:49:20.188000Z,0,1,2,Change Affiliation|2024-09-10T06:49:20.188000Z|ab02169519
Change Affiliation|2024-09-10T06:49:20.188000Z|ab02169519|q10-a11-12,0.72,discord_thread_heuristic,Change Affiliation,issues,Based on your screenshot you might using a newer version of keycloak where this issue is already fixed. Are you using keycloak.v2 theme for Account theme?,I'm guessing but I think you will see keycloak.v3. As far as I can see v2 is not even maintained/supported anymore so we might need to update keycloak to fix this issue.,,2024-09-10T06:49:20.188000Z,10,11,12,Change Affiliation|2024-09-10T06:49:20.188000Z|ab02169519
Plugin Loaded twice|2024-09-04T10:23:06.791000Z|351b42840e|q8-a9-10,0.72,discord_thread_heuristic,Plugin Loaded twice,issues,I can try to investigate this. You are seeing when using the `main` branch of `nomad-measurements`?,"Ok, I think I figured it out. The aliasing mechanism is causing this duplication. This issue is being tracked in [#2122](https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2122) Should be fairly easy to solve, I keep you updated.",,2024-09-04T10:23:06.791000Z,8,9,10,Plugin Loaded twice|2024-09-04T10:23:06.791000Z|351b42840e
"Creating multiple uploads causes some to get stuck ""Waiting""|2024-08-28T11:24:51.603000Z|7bca1ff866|q23-a31-32",0.72,discord_thread_heuristic,"Creating multiple uploads causes some to get stuck ""Waiting""",issues,Should this not be reset with a docke compose down and up?,👍 In production we use something like 128Gb for heavy processing.. But this all depends on what sort of data you are dealing with and how smart the parsers are,,2024-08-28T11:24:51.603000Z,23,31,32,"Creating multiple uploads causes some to get stuck ""Waiting""|2024-08-28T11:24:51.603000Z|7bca1ff866"
Docker build fails for latest develop|2024-07-10T07:48:13.050000Z|af964aa462|q28-a29-30,0.72,discord_thread_heuristic,Docker build fails for latest develop,issues,"Here is the log, I build without args (really just docker build . in the nomad source tree). I've been doing it like this in the past few years, Do I need some args now?",``` docker build . --target final --build-arg SETUPTOOLS_SCM_PRETEND_VERSION=1.3.3```,,2024-07-10T07:48:13.050000Z,28,29,30,Docker build fails for latest develop|2024-07-10T07:48:13.050000Z|af964aa462
The entry does not exist problem in archive|2024-06-14T04:39:22.417000Z|6d25ec3b83|q0-a1-2,0.72,discord_thread_heuristic,The entry does not exist problem in archive,issues,"Any idea why I'm getting ""The entry does not exist"" here?",https://nomad-lab.eu/prod/v1/gui/upload/id/wTBuDynkQ8WdfJ4Rrw5IDg/entry/id/XKEQiB-r0f1QjlfNBzsrLrsICGJq,,2024-06-14T04:39:22.417000Z,0,1,2,The entry does not exist problem in archive|2024-06-14T04:39:22.417000Z|6d25ec3b83
Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed|q41-a42-43,0.72,discord_thread_heuristic,Mkdocs Error when updating Nomad,issues,Maybe its just the shell command. Maybe it comes from a different environment or uses the wrong environment. Something mixed up in the PATH or PYTHONPATH maybe. What do you get when running `which mkdocs` and what does the file look like?,`which mkdocs` points to the correct environment: `/home/<USER>/software/nomad/.pyenv/bin/mkdocs`,,2024-05-07T09:51:39.304000Z,41,42,43,Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed
Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5|q23-a24-26,0.72,discord_thread_heuristic,Accessing Section Definitions via API,issues,"Ok, so i did manage to use a post request, retrieve the data via 'entries/archive/query' and i can access response['data']['archive']['data']. I could not quite figure out how to convert my dict back into a nomad object yet. Do i have to import the 'EntryArchive'-class from nomad?","Great! Indeed, you should import `EntryArchive` and then feed in `response['data']['archive']`. Note that the conversion may fail if you filtered out parts of the response. One addtional question what we are looking for is information which is in here: https://nomad-hzb-ce.de/nomad-oasis/gui/artifacts.js stuff like this.",,2024-04-12T07:49:39.151000Z,23,24,26,Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5
Tabular parser row chemical_formula|2025-06-12T09:58:22.006000Z|b6699b3fd8|q4-a26-27,0.71,discord_thread_heuristic,Tabular parser row chemical_formula,issues,How should this be done? What am I doing wrong?,"you should be able to call the section Sample, it is not protected. If you call it Sample, remember to also change the type in line 65 to '#/Sample'",,2025-06-12T09:58:22.006000Z,4,26,27,Tabular parser row chemical_formula|2025-06-12T09:58:22.006000Z|b6699b3fd8
NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007|q1-a14-15,0.71,discord_thread_heuristic,NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12,issues,"Btw: the official python version for NOMAD is Python3.12, nor?","Building the image for deployment via `nomad-FAIR` repo is no longer supported, please use https://github.com/FAIRmat-NFDI/nomad-distro-template to build deployment docker images",,2025-06-06T14:58:44.249000Z,1,14,15,NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007
Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e|q8-a11-12,0.71,discord_thread_heuristic,Where to find available base sections for yaml,issues,How come?,"You can click on the ""ALL INHERITING SECTIONS"" and they should load",,2025-06-02T09:45:34.629000Z,8,11,12,Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e
Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q38-a40-42,0.71,discord_thread_heuristic,Unexpected error in the research app,issues,"Great job on the PPM introduction! Regarding the Oasis update, I spoke with the lab manager, and he told me that we have installed version 1.3.14dev160—if I understood correctly. So, could the fix already be effective?","The fix for the error with the menu+Celsius unit is available from `1.3.16.dev106` onwards. Let me know if you need help in installing this. If I understand the question correctly, you would want to hide some ELN fields based on a boolean value? I think changing the GUI layout conditionally is not currently possible, but you can in your normalize functions create a warning/error if some of the required data is not available.",,2025-02-28T07:57:57.339000Z,38,40,42,Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b
"In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c|q8-a23-24",0.71,discord_thread_heuristic,"In and ELN lane, the rendering of API response in json is overlapping with fields.",issues,"As to the second problem: Now I have the referenced entry (Identifier_exp3b6a73f710eaf793557ddecb76d0f73e.archive.json), but I would also need to know the class for ""Experiment identifier"" in order to know what types of references `entry_reference` accepts. Is this part of some custom schema, or part of our base sections?","Sorry, I'm answering late here.To find out why a reference to an Entity does not work in your schema, we will have to try and create a minimal non-working example. Do you have the Python code that defines `pynxtools.Identifier.reference`? I could not spot anything wrong in this link that you sent me: https://nomad-lab.eu/prod/v1/oasis/gui/analyze/metainfo/pynxtools/section_definitions",,2024-11-07T15:03:16.142000Z,8,23,24,"In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c"
Scatterplot: focus on region|2024-11-04T13:23:24.664000Z|9bdeb903e8|q10-a14-15,0.71,discord_thread_heuristic,Scatterplot: focus on region,issues,"Any idea what we should do in the case where we filter for a value in an array? Maybe we keep all the entries that contain at least one match in the array, but we should simply zoom in on the region and not show the rest of the points that did not match. Or do you think we should require all of the points in that array to match the criterion?","The reason why the ""Filter"" results may seem weird is the fact that filters simply return all entries where there is some data that fullfills the query. It does not care about where in the data the condition is filled, and will always just return the full entry. This is why if you create a filter based on `results.properties.catalytic.reaction.reaction_conditions.temperature` `results.properties.catalytic.reaction.reactants.gas_concentration_in`, the plot may also contain other reactions, simply because they are included in this entry where one of the reactions matched the query.",,2024-11-04T13:23:24.664000Z,10,14,15,Scatterplot: focus on region|2024-11-04T13:23:24.664000Z|9bdeb903e8
Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin|2024-10-22T10:58:19.936000Z|41985e2c35|q25-a29-30,0.71,discord_thread_heuristic,Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin,issues,"And also, back to my original question: is it the expected behavior when there is no parser for a certain file format? Like no error message / no crash / just not starting the processing at all (but the uploaded files can be previewed) ?",in the nomad.yaml file: https://nomad-lab.eu/prod/v1/docs/reference/config.html#entrypoints,,2024-10-22T10:58:19.936000Z,25,29,30,Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin|2024-10-22T10:58:19.936000Z|41985e2c35
`ArchiveQuery` fetches nothing in Oasis|2024-04-18T13:22:06.338000Z|161631014e|q11-a36-37,0.71,discord_thread_heuristic,`ArchiveQuery` fetches nothing in Oasis,issues,"Would you expect something, even if the authentication does not work?","But for entries belonging to certain entry classes from plugins, I run into an AttributeError when accessing `context.name`. The error is attached along. ran into the similar error and it seems it was resolved here https://discord.com/channels//",,2024-04-18T13:22:06.338000Z,11,36,37,`ArchiveQuery` fetches nothing in Oasis|2024-04-18T13:22:06.338000Z|161631014e
Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8|q12-a56-57,0.71,discord_thread_heuristic,Weird GUI data info,issues,is being fixed with https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/1693 ?,Issue: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1915,,2024-03-01T12:53:07.703000Z,12,56,57,Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8
Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app|2025-08-07T09:10:48.009000Z|5e08ea8db6|q3-a5-6,0.7,discord_thread_heuristic,Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app,issues,I think this should in general be possible. The issue might be related to us using the same query key for items within a menu. Could you create an issue in Gtlab for this with a minimal app example that demonstrates the problem?,https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2389,,2025-08-07T09:10:48.009000Z,3,5,6,Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app|2025-08-07T09:10:48.009000Z|5e08ea8db6
Automated test to mimic GUI functionalities|2025-06-27T09:10:50.283000Z|f17ee76551|q1-a3-4,0.7,discord_thread_heuristic,Automated test to mimic GUI functionalities,issues,I was just wondering if any way/packages can enable us to mock the uploading and processing of data as it happens on the NOMAD GUI?,"I am asking this from my experience working on this PR: Add LOBSTER workflow schema by naik-aakash · Pull Request #60 · nomad-coe/workflow-parsers -- https://github.com/nomad-coe/workflow-parsers/pull/60 , where one cannot easily add a test for the workflow that is being populated using the child archives approach.",,2025-06-27T09:10:50.283000Z,1,3,4,Automated test to mimic GUI functionalities|2025-06-27T09:10:50.283000Z|f17ee76551
Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80|q19-a21-22,0.7,discord_thread_heuristic,Publishing DFT calculations from RSPt,issues,"2. The answer to this depends a little if you are working on an Oasis or the central NOMAD, and also what the desired ""search functionalities"" are (understanding that they will certainly be limited). Do you want to simply be able to query for RSPt calculations that were uploaded or something more than that?","2. At the moment, if we understood it correctly, publishing from an OASIS is not possible, because ours is not connected to DOI, and transfer of data from OASIS to central NOMAD is not yet functioning, please correct me if we got it wrong. But that's what we've clarified so far that's why we moved to trying putting our data directly on the central NOMAD, because we need it published in June. About ""desired search functionalities"" the answer is all possible. I'm not sure I know all term correctly, but in the best case scenario would be great to be able to use some search tools from NOMAD except for search by text in a search field. If you have time, we could shortly explain it in a zoom/teams/discord call. It is difficult to explain via messages, cause I'm not sure if I'm using the right terminology.",,2025-06-03T09:29:11.049000Z,19,21,22,Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80
TOML parse error|2025-05-05T12:24:25.554000Z|d21caab30f|q7-a9-10,0.7,discord_thread_heuristic,TOML parse error,issues,This command returns: `error: unexpected argument 'uv' found`. Do you possibly mean the `-P` flag (`--upgrade-package`)?,"So far, this seems to have done the trick, the TOML error seems to be gone for now. I will keep an eye out for `uv` versions in our plugins, maybe we have a setting somewhere that we shouldn't.",,2025-05-05T12:24:25.554000Z,7,9,10,TOML parse error|2025-05-05T12:24:25.554000Z|d21caab30f
Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c|q24-a26-27,0.7,discord_thread_heuristic,Failing test in plugin,issues,I tried changing the unit back to *dimensionless* locally and it seemed to fix the error. Any ideas why?,"I don’t know exactly the reason, but you could try removing the unit attribute completely and check if the error persists",,2025-04-14T08:48:46.049000Z,24,26,27,Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c
Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c|q40-a42-43,0.7,discord_thread_heuristic,Failing test in plugin,issues,"In the test data and test script, I should also change the test value. It is just a single float value at the moment, but the expected input in reality is a [3,3] tensor. I intend to define something like, [n11, n12, n13; n21, n22, n23; n31, n32, n33]. When parsed, this is how we would store the value anyway. Have you got any feedback to define it better or is assumption alright?",Follow-up. I have changed the test value in the YAML file and the test py script to a tensor as expected from a magres file (refer here: https://github.com/FAIRmat-NFDI/nomad-schema-plugin-nmr/pull/1/commits/d632ab1f82af149a069127a528800be4051a304b). All tests have passed now. Thank you for your help.,,2025-04-14T08:48:46.049000Z,40,42,43,Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c
questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173|q0-a2-3,0.7,discord_thread_heuristic,questions about nomad-distro-dev,issues,"I am trying to setup a new nomad-distro-dev according to the readme. When I try to spin up docker I get a conflict, probably due to my other nomad installation. Do I really have to delete the other container? Then it will delete my test uploads, wont it?","[+] Running 1/4 ✔ Network nomad-distro-dev_default Created 0.0s ⠋ Container nomad_mongo Creating 0.0s ⠋ Container nomad_rabbitmq Creating 0.0s ⠋ Container nomad_elastic Creating 0.0s Error response from daemon: Conflict. The container name ""/nomad_elastic"" is already in use by container ""972f2ad34a79d8798a5639026d7253bfdd8ca9eb4179b2278cca5ad2fcb0c890"". You have to remove (or rename) that container to be able to reuse that name.```",,2025-01-16T11:42:39.899000Z,0,2,3,questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173
questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173|q22-a24-25,0.7,discord_thread_heuristic,questions about nomad-distro-dev,issues,"Ahmed, how about adding this note to the documentation?",I added it last friday - https://github.com/FAIRmat-NFDI/nomad-distro-dev/pull/32,,2025-01-16T11:42:39.899000Z,22,24,25,questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173
Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4|q16-a18-19,0.7,discord_thread_heuristic,Can't get dev installation to work,issues,"Well, it's not too urgent. Is your fork up to date and one I could also try for testing?",I think it should be up to date now: https://github.com/hampusnasstrom/nomad-distro-dev,,2024-12-16T09:34:40.791000Z,16,18,19,Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4
dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db|q11-a13-14,0.7,discord_thread_heuristic,dynamic appearance of app widgets,issues,"But the filters will still be relevant from one ""group"" of widgets to the next, so I dont think parallel apps is what we need in this case?","I think I get the idea. How would you prefer the switching between the different dashborads to happen, and would swithincg the dashboard affect anything else, e.g. would it toggle on/off some filters? Would you also use this feature if it was available? I would want to get an idea of how relevant this feature is for users.",,2024-09-19T10:21:05.589000Z,11,13,14,dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db
Working with HDF5Datasets|2024-09-10T11:11:53.012000Z|8b84a352a0|q23-a25-26,0.7,discord_thread_heuristic,Working with HDF5Datasets,issues,"sure that could also be a possibility, joe can you pls note this in the issue?",https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2129,,2024-09-10T11:11:53.012000Z,23,25,26,Working with HDF5Datasets|2024-09-10T11:11:53.012000Z|8b84a352a0
set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851|q11-a13-14,0.7,discord_thread_heuristic,set default display unit for arrays read from file,issues,Did you open any issue that I can create a MR?,[here](https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2087),,2024-08-05T10:01:34.812000Z,11,13,14,set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851
Standard way of writing `m_proxy_value`|2024-04-10T17:08:30.668000Z|169773661f|q10-a17-18,0.7,discord_thread_heuristic,Standard way of writing `m_proxy_value`,issues,"From what I am reading here, I could imagine a ReferenceEditQuantity that is already using the form with `/` so that the normalisation does not have to add it later on. But how big of an issue is this?",here's the link to the issue on gitlab: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1992,,2024-04-10T17:08:30.668000Z,10,17,18,Standard way of writing `m_proxy_value`|2024-04-10T17:08:30.668000Z|169773661f
Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67|q19-a24-25,0.69,discord_thread_heuristic,Adding plugin to `nomad-distro-dev` fails,issues,> Is this similar to the 'nomad-parser-plugins-atomistic' vs 'atomisticparsers' issue?,"to have to start this up again. `uv sync` completed successfully once, but when I tried to run `nomad parse`, I got a new (similar) error:",,2025-06-13T08:52:00.065000Z,19,24,25,Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67
questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173|q5-a10-11,0.69,discord_thread_heuristic,questions about nomad-distro-dev,issues,"Next question, the `uv add packages/nomad-parser-plugins-electronic` command does not work. I have added multiple packages, and now when I do the `uv add ..` it always complains about some other packages missing in `tool.uv.sources` do you have to immediately run the uv add command after running each single `git submodule add ..`?","Ah yes, it doesn't support adding multiple submodules at the same time followed by `uv add`. If you're using `uv add`, you'll have to run it after adding each submodule individually.",,2025-01-16T11:42:39.899000Z,5,10,11,questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173
Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a|q5-a6-7,0.69,discord_thread_heuristic,Issue Starting Nomad Server: Connection Problems with Services,issues,"When I execute the uv run poe start command, I receive an error indicating a ""connection refused"" to Elasticsearch. I have attached the Errore.txt file with the full error message for more details. Here is what I have already tried:",1. Verified that Elasticsearch is running using docker ps and responds to http://localhost:9200.,,2025-01-11T15:49:58.163000Z,5,6,7,Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a
Uploads to NOMAD never finish processing|2024-10-21T08:04:29.181000Z|ffe6f2bd9f|q21-a25-27,0.69,discord_thread_heuristic,Uploads to NOMAD never finish processing,issues,"is going to put in a fix, which makes the Gromacs2024 a bit more clear, and avoid some of this error-causing processing, so that we somewhat ease the situation.",Alright.. Could you comment about this also into this issue: https://discord.com/channels// I now reset and reprocessed upload `U__GD1aNQ4KbKmOsHKOtjA`. Could you check if it looks any better now? I don't have access to it.,,2024-10-21T08:04:29.181000Z,21,25,27,Uploads to NOMAD never finish processing|2024-10-21T08:04:29.181000Z|ffe6f2bd9f
FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q77-a80-81,0.68,discord_thread_heuristic,FHIaims parsing issues,issues,you mentioned that it is working on the central deployment right?,fix here https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2536,,2025-07-24T12:22:12.739000Z,77,80,81,FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93
Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3|q5-a8-9,0.68,discord_thread_heuristic,Solve `mongo_user_group` warning in the application startup,issues,"I don't know about those mongoDB warnings (also not getting them locally), maybe you could comment on this?",Here you are: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/snippets/181,,2025-07-16T15:57:41.003000Z,5,8,9,Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3
Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271|q47-a57-58,0.68,discord_thread_heuristic,Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage,issues,"Could you maybe delete or reset the stuck uploads listed above (p0e-3HI3S9KRRuj4nlJV6g, i--gEvRjQiO0Zcly-CZ9vA, qmxnv-8LQxGkAp8ax-Do0A, iJ65x2AJTbeUi9-QOhH4Mw, HlLtWCYtQ1ClTJi5WQvbJQ, SYNGBFqBQcm6r8bnmSNtNA, g51pcc7SRny8AQIyDcHNUw, W7_BDGRrQzKRpJGbkWkFoA), such that I can delete them?","Indeed it looks like I cannot process these uploads even when I assign significant resources for them specifically. I tried now also `qmxnv-8LQxGkAp8ax-Do0A`, but still getting stuck.",,2025-05-16T07:42:17.401000Z,47,57,58,Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271
Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271|q58-a66-67,0.68,discord_thread_heuristic,Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage,issues,We need to figure out what is happening with the processing and where is it getting stuck. do you have somone from Area C that could take on this task? what is your smallest upload that does not get processed? And how large is it roughly in terms of disk space?,"Note that the fixes are enabled only in our `develop` deployment, which contains nightly updates for our development purposes. So if you want to reprocess something yourself, you need to go to: http://nomad-lab.eu/prod/v1/develop/gui",,2025-05-16T07:42:17.401000Z,58,66,67,Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271
Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q3-a33-34,0.68,discord_thread_heuristic,Unexpected error in the research app,issues,I've encountered the same issue with this plugin. The debug screen appears to reference internal functions.. could this be an internal bug?,The fix has now been merged to the `develop` branch (https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2380).,,2025-02-28T07:57:57.339000Z,3,33,34,Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b
Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q46-a131-132,0.68,discord_thread_heuristic,Unexpected error in the research app,issues,Now we have a repository with the content presented in the image. Now the questions are two: where we have to install an external plugin as the one presented in the issue and how to update the repository to prevent the bug?,"I am using that same use case (headless server for nomad-distro-dev), and added a note to the readme about the required port forwardings: https://github.com/FAIRmat-NFDI/nomad-distro-dev/blob/21a1566fc1d8ced3757d2d2b60ecea64f24f4b58/README.md?plain=1#L311",,2025-02-28T07:57:57.339000Z,46,131,132,Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b
"Setup error on ""nomad-distro-dev""|2025-01-08T14:54:17.574000Z|58cb8ee208|q123-a126-127",0.68,discord_thread_heuristic,"Setup error on ""nomad-distro-dev""",issues,That error usually means the API server is not reachable for the frontend. But based on the logs it seems like the server runs without issues. Is localhost:8000 accessible?,"this works, but running uv run poe start afterwards stll throws :",,2025-01-08T14:54:17.574000Z,123,126,127,"Setup error on ""nomad-distro-dev""|2025-01-08T14:54:17.574000Z|58cb8ee208"
Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4|q7-a15-16,0.68,discord_thread_heuristic,Can't get dev installation to work,issues,"Hrmm, strange. What do you have in your `nomad.yaml`?",Assuming you don't have any data you want to keep you could try a `docker compose down --volumes` and then `docker compose up -d`. That should solve the resource already exists I think. But I'm not sure that's your issue here. Unfortunately Ahmed is on vacation.,,2024-12-16T09:34:40.791000Z,7,15,16,Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4
"In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c|q4-a7-8",0.68,discord_thread_heuristic,"In and ELN lane, the rendering of API response in json is overlapping with fields.",issues,I'm not entirely sure what could be wrong in the second image. Can you maybe send me that JSON file and also link/message the definition for the `entity reference` definition in your schema?,"When you have time, you can test the fix for the overlap problem here: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2204",,2024-11-07T15:03:16.142000Z,4,7,8,"In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c"
Problem with SectionProxy|2024-10-14T14:24:47.620000Z|b15d624100|q10-a13-14,0.68,discord_thread_heuristic,Problem with SectionProxy,issues,Any ideas what I am missing out here?,But that's what I exactly don't want to /can not do and why I want to use the SectionProxy. Otherwise I could use a normal Reference.🤔,,2024-10-14T14:24:47.620000Z,10,13,14,Problem with SectionProxy|2024-10-14T14:24:47.620000Z|b15d624100
The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q56-a66-67,0.68,discord_thread_heuristic,The new oasis-template doesn't works,issues,What browser do you use ?,The problem is that we do not reproduce your error and we are able to use your image,,2024-10-04T13:34:29.120000Z,56,66,67,The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8
dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db|q4-a7-8,0.68,discord_thread_heuristic,dynamic appearance of app widgets,issues,this looks really good. Is this already part of a MR?,https://nomad-lab.eu/prod/rae/gui/search,,2024-09-19T10:21:05.589000Z,4,7,8,dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db
Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7|q9-a12-13,0.68,discord_thread_heuristic,Publishing an entry from Oasis to Central NOMAD,issues,"2. So, in the case of the second option above, we would have to upload the data with YAML schema attached, is it correct ?",I added some more information to your docs. It is not yet perfect. You can see it already on our develop deployment here: <https://nomad-lab.eu/prod/v1/develop/docs/explanation/oasis.html>. These docs will move to the official site next week.,,2024-08-29T07:38:20.591000Z,9,12,13,Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7
Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765|q59-a60-61,0.68,discord_thread_heuristic,Problem when `docker compose up -d` an Oasis,issues,ok i found error thank you but how did you get this error? Did you just run the docker-compose file ?,`docker logs nomad_oasis_app`,,2024-08-02T08:23:26.436000Z,59,60,61,Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765
Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8|q53-a56-57,0.68,discord_thread_heuristic,Weird GUI data info,issues,"Ok, when there is the time, I will revert the last change and instead add a possibility to have a custom label on both the quantities and subsections. I guess this should also satisfy ?",Issue: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1915,,2024-03-01T12:53:07.703000Z,53,56,57,Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8
np.nan and None in nomad client|2025-04-24T13:53:33.907000Z|b15340cffb|q3-a9-10,0.67,discord_thread_heuristic,np.nan and None in nomad client,issues,I was told that a related np.nan problem has been discussed here (https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2128). Can there also be a solution for the ArchiveQuery?,"Then call `m_from_dict`, reference code can be seen as attached.",,2025-04-24T13:53:33.907000Z,3,9,10,np.nan and None in nomad client|2025-04-24T13:53:33.907000Z|b15340cffb
Problem with `nomad-distro-dev`|2024-10-24T15:21:28.021000Z|3f0f258a83|q0-a7-8,0.67,discord_thread_heuristic,Problem with `nomad-distro-dev`,issues,"When I run `nomad parse <input_file>` or try to update the environment with `uv run poe setup`, I get the following error:",a problem with Python 3.12 and the package `typing-extensions`. Some people have reported similar issues here: https://github.com/Azure/azure-sdk-for-python/issues/33442,,2024-10-24T15:21:28.021000Z,0,7,8,Problem with `nomad-distro-dev`|2024-10-24T15:21:28.021000Z|3f0f258a83
Uploading process continues forever!|2024-05-22T12:30:19.255000Z|9e206ccef9|q5-a7-8,0.67,discord_thread_heuristic,Uploading process continues forever!,issues,Isn't it a bug? Or?,shell command `nomad admin uploads stop mRGT2zAoRbGTmDeYiZZzDQ --kill` from container does not work. `mRGT2zAoRbGTmDeYiZZzDQ` is the upload_id.,,2024-05-22T12:30:19.255000Z,5,7,8,Uploading process continues forever!|2024-05-22T12:30:19.255000Z|9e206ccef9
GPAW Parser Error: unsupported operand type(s) for *: 'Quantity' and 'NoneType'|2025-07-23T11:36:11.817000Z|911dc4eef0|q4-a8-9,0.66,discord_thread_heuristic,GPAW Parser Error: unsupported operand type(s) for *: 'Quantity' and 'NoneType',issues,```Could someone from the GPAW parser team take a look?,https://nomad-lab.eu/prod/v1/gui/user/uploads/upload/id/5Hw04xz0RAm28GIZtSYS2w,,2025-07-23T11:36:11.817000Z,4,8,9,GPAW Parser Error: unsupported operand type(s) for *: 'Quantity' and 'NoneType'|2025-07-23T11:36:11.817000Z|911dc4eef0
nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52|q41-a42-43,0.66,discord_thread_heuristic,nomad-oasis can not stablely be started,issues,"I don't fully understand how you deployed your Oasis right now (this looks like a Windows Terminal to me), maybe you could tell me more about it? Did you use a custom `api_port` other than the default )? Did you setup HTTPS (if so you're likely to get a 301 redirect https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/configs/nginx_https.conf#L15)?","OK, let me ask you some questions. so in the app container you have a nginx proxy, right? my question is: is it possible we can specify our own http server either using reverse proxy or directly specifing? Currently I use reverse proxy forward the request to your nginx proxy..",,2025-07-17T08:58:19.106000Z,41,42,43,nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52
Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q75-a76-77,0.66,discord_thread_heuristic,Create multiple entry from parser or new entry in normalizer,issues,"If I'm getting everything correctly, the file path provided to `archive.m_context.process_updated_raw_file(self, path: str, allow_modify: bool)` will be treated as another mainfile, thus creating a new EntryArchive. In addition, this function could be call in either parser and normalizer. The only downside is that nothing will happen if it's ran in client context, so it's hard to test, is that true?",in nomad you can not set to public and still edit the data you can share with others and groups and you can publish it (but then it cant be changed anymore),,2025-06-14T07:31:20.113000Z,75,76,77,Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab
NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007|q40-a44-46,0.66,discord_thread_heuristic,NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12,issues,"With that way it'd possible to add new static files, and have a new URL for your impressum. But I suppose there should be a link to the impressum from the home page of the app, is that correct?",new variable under meta config for `footer_links` that could be used to add additional links to the home page: https://nomad-lab.eu/prod/v1/develop/docs/reference/config.html#meta,,2025-06-06T14:58:44.249000Z,40,44,46,NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007
Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80|q4-a5-6,0.66,discord_thread_heuristic,Publishing DFT calculations from RSPt,issues,"post. Indeed there is no development yet for a RSPt parser, however I will make a note of this code for our future development. Can you give me a rough idea of the scope and user base of this code?","For your upcoming publication. You can still upload your calculations to nomad now, and utilize the base features (overarching metadata like authors, references, etc). You could even create a small eln entry to annotate your uploads with further metadata or descriptions.",,2025-06-03T09:29:11.049000Z,4,5,6,Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80
Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c|q53-a54-55,0.66,discord_thread_heuristic,Failing test in plugin,issues,"May I first ask which version of `nomad-simulations` are you using, and how urgent is this?",**Update on the current state of affairs:**,,2025-04-14T08:48:46.049000Z,53,54,55,Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c
Error 500 when creating a new upload|2025-04-07T07:48:07.283000Z|f79556187d|q13-a17-18,0.66,discord_thread_heuristic,Error 500 when creating a new upload,issues,"Ok, when it comes to solve the conflict merge in the `uv.lock` file, how should I handle this ?","So, the IT person answered me and sent me the logs of the Docker image for the NOMAD Oasis. I attach it here but I am not really familiar with it. Anyway, we tried to use the Oasis at around 9am on the 07/04/25 but I don't see any log concerning an issue in the logs around that date",,2025-04-07T07:48:07.283000Z,13,17,18,Error 500 when creating a new upload|2025-04-07T07:48:07.283000Z|f79556187d
distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q46-a55-56,0.66,discord_thread_heuristic,distro-dev not running on latest develop,issues,"eh, okay. Can I change the wheel version uv runs somehow?","I am getting the same errors in our pipelines. Another option would be to update setuptools (see https://github.com/pypa/wheel/blob/main/README.rst), but I am not sure if that breaks many other things",,2025-04-01T12:49:10.385000Z,46,55,56,distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4
distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q93-a102-103,0.66,discord_thread_heuristic,distro-dev not running on latest develop,issues,This is a bit ridiculous. How about they test something like this with a dev release before breaking a fundamental thing like `wheel` for everyone?,This should fix it but until the next release is out I guess the solution is to use build constraints - https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2426,,2025-04-01T12:49:10.385000Z,93,102,103,distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4
docker problem when starting local nomad|2025-03-24T13:38:27.783000Z|4ba9059016|q2-a3-4,0.66,discord_thread_heuristic,docker problem when starting local nomad,issues,"There is no `_placeholder_quantity` anywhere in the code. The same versions (nomad-lab + pynxtools) run fine on another pc. It seems like something of the older version remained in the docker container, however `docker compose down + docker compose up` was not enough to fix the issue. What am I missing?","Update: fresh installation of nomad seems to work. I am unsure if I did something wrong while updating previous installation, or if there is an issue with nomad",,2025-03-24T13:38:27.783000Z,2,3,4,docker problem when starting local nomad|2025-03-24T13:38:27.783000Z|4ba9059016
Oasis crashes when uploading multiple NeXus .hdf5 files|2025-02-13T15:36:13.213000Z|31f7b310d4|q0-a1-2,0.66,discord_thread_heuristic,Oasis crashes when uploading multiple NeXus .hdf5 files,issues,"We are currently trying to upload our data backlog to our local Oasis. The files are all converted to the NeXus format and correctly parsed. However, after rough 100-200 uploaded files, the server PC freezes and needs to be restarted manually. We noticed that during uploading the swap memory size gradually increases until it is full and the server freezes. This only happens when uploading nexus files and happens also with the example ellipsometry .nxs file (when uploaded multiple times under different names). Has anybody seen something similar or advice on how to handle this?","Given that you use a local oasis, providing a little more context would be good to enable at all a search for answers to your observations. These are guiding questions:",,2025-02-13T15:36:13.213000Z,0,1,2,Oasis crashes when uploading multiple NeXus .hdf5 files|2025-02-13T15:36:13.213000Z|31f7b310d4
DEV Deployment stalls on upload|2025-01-21T10:41:48.261000Z|07f5df7e4e|q1-a14-15,0.66,discord_thread_heuristic,DEV Deployment stalls on upload,issues,Can you tell a bit more? Do you mean the `develop` deployment (https://nomad-lab.eu/prod/v1/develop/gui) or your local dev installation?,"Typically when an upload hangs it is because of resources: the processing e.g. runs out of memory or crashes in some other unexpected reason. If possible, this should then be debugged locally. Also the `develop`-deployment is not really equitpped with very beefy machines, so trying to process anything very large there can be problematic.",,2025-01-21T10:41:48.261000Z,1,14,15,DEV Deployment stalls on upload|2025-01-21T10:41:48.261000Z|07f5df7e4e
Is the shape of a Quantity preserved when overwriting?|2024-12-05T14:28:17.339000Z|f69823772f|q12-a13-14,0.66,discord_thread_heuristic,Is the shape of a Quantity preserved when overwriting?,issues,"Did you try to overwrite with `shape=[]`? maybe that would overwrite the shape. If you don't set one, I guess the original shape is still valid?","Mh. I did not try that yet, but that would make some sense I guess. 👍",,2024-12-05T14:28:17.339000Z,12,13,14,Is the shape of a Quantity preserved when overwriting?|2024-12-05T14:28:17.339000Z|f69823772f
"Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd|q5-a6-7",0.66,discord_thread_heuristic,"Uploads are not displayed, despite being in the volumes folder",issues,"then this was a misunderstanding from my side. Is there a chance to keep the upload_id, because of references?",Restoring data with the same `upload_id` (or other ids like `entry_id`) is quite tricky.,,2024-10-09T19:25:25.125000Z,5,6,7,"Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd"
Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q52-a53-54,0.66,discord_thread_heuristic,Adapting existing Oasis to new plugin mechanism,issues,But this is not the scalable approach we want for more users. What is it now that it special in your image? Is it just the button?,"But we will be removing the final image built step from `nomad-FAIR` though, so `nomad-FAIR` in the future will just release a python package, deployments, image releases etc will happen on `nomad-distro`",,2024-10-01T12:39:26.289000Z,52,53,54,Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459
dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db|q0-a1-2,0.66,discord_thread_heuristic,dynamic appearance of app widgets,issues,Would it somehow be possible to decide if a widget appears in an app depending if it is populated by any data points given the current set of filters? So e.g. to make empty widgets invisible? Maybe has an idea if this could be possible?,"A possible alternative (more straight forward) would be to have parallel apps with more locked filters? I was planning this for the solar cell ones. Then, I would day that the app navigation menu would need some update..",,2024-09-19T10:21:05.589000Z,0,1,2,dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db
Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7|q43-a44-45,0.66,discord_thread_heuristic,Publishing an entry from Oasis to Central NOMAD,issues,"I am really interested in your workflow but have to study your explanation a bit further. Do you know if there is any chance to get the schemas out of the GUI using the ""<>""-buttons saying ""show section data as JSON""?",Would you be available for a discussion in a zoom-call?,,2024-08-29T07:38:20.591000Z,43,44,45,Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7
Problems with data types after updating to NOMAD develop branch|2024-07-08T09:06:49.792000Z|923b00310a|q6-a7-8,0.66,discord_thread_heuristic,Problems with data types after updating to NOMAD develop branch,issues,This is due to the new metainfo datatypes merged recently. And yes they are less forgiving now. Auto converting strings into numbers is really a stretch. This conversion from string to float is really the parsers job. whats your opinion on this?,"The type system internally tries to convert the input data into the target type. In this particular case, when a string ""20"" is given, the type instance tries to check the following: ""20"" == str(numpy.float64(""20"")), this results actual check to be something like ""20"" == """" due to the inexact nature of floats.",,2024-07-08T09:06:49.792000Z,6,7,8,Problems with data types after updating to NOMAD develop branch|2024-07-08T09:06:49.792000Z|923b00310a
TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference|2024-06-18T13:26:34.564000Z|d04b45f526|q15-a16-17,0.66,discord_thread_heuristic,TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference,issues,"Question though: Do we need `if self.archive.m_context:` in the new implemention? No right, we can just do a direct setting of the arrays into the quantities that are specified as HDF5Dataset?",somehow did not get any notication but in the develop branch HDF5Dataset is already used . It is simply that the simulationworkflow schema submodule is not updated I will try to update,,2024-06-18T13:26:34.564000Z,15,16,17,TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference|2024-06-18T13:26:34.564000Z|d04b45f526
HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q26-a27-28,0.66,discord_thread_heuristic,HDF5 in NOMAD,issues,"So for the 1st one, if I make an ELN field, and the user edits that ELN field, will the data in this hdf5 be written automatically? Does the Quantity object know the path I initially gave when creating the schema? Or is it that whenever we write or read these Quantities this path has to be specfied?",the first does not do any writing. it is simply a glorified string. if you want to write the data in an auxialiary archive h5 file use 2. then the path is determined by the archive path to the quantity.,,2024-05-27T13:17:35.844000Z,26,27,28,HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57
Cache Pub Chem Queries|2024-04-16T12:43:43.375000Z|9bcd1deaf9|q18-a22-23,0.66,discord_thread_heuristic,Cache Pub Chem Queries,issues,I missed this! Are you done already?,There is a gitlan issue now and the discussion can continue there: <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1981>,,2024-04-16T12:43:43.375000Z,18,22,23,Cache Pub Chem Queries|2024-04-16T12:43:43.375000Z|9bcd1deaf9
Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c|q49-a50-51,0.66,discord_thread_heuristic,Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis,issues,I removed the plugin in the meantime. Should run the command `docker compose run north python -m jupyterhub upgrade` again or the otherone you just posted: `jupyterhub upgrade-db`?,"`upgrade-db` is the right command to upgrade the, well, db",,2024-03-14T16:47:18.591000Z,49,50,51,Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c
Docker Issues with first start up of NOMAD Oasis|2025-04-23T21:33:05.024000Z|151e21ac26|q5-a7-8,0.64,discord_thread_heuristic,Docker Issues with first start up of NOMAD Oasis,issues,10 GB should in theory be enough but it depends on how many cores you are running. I think it should still work on startup though. Is this on the new apple silicon architecture? I just tried it on a Linux distribution (Fedora) and it started fine. Maybe can give it a try as I think he runs a Mac?,"I can also recommend creating your own repository, and removing the set of plugins that you don't need from the toml table. That should decrease the memory requirements.",,2025-04-23T21:33:05.024000Z,5,7,8,Docker Issues with first start up of NOMAD Oasis|2025-04-23T21:33:05.024000Z|151e21ac26
Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94|q119-a121-122,0.64,discord_thread_heuristic,Adding h5view of data file to entry overview,issues,I am sorry.. Where do we see this? Do we go the DATA tab?,Well the is the pynxtools schema which does not use any H5WebAnnotation. It rather uses a Nexus Card solution which automatically renders this card on the overview page for Nexus files.. I do not know how this works exactly,,2025-04-03T09:05:50.630000Z,119,121,122,Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94
Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5|q15-a17-18,0.64,discord_thread_heuristic,Protected Oasis - Restrict GUI/API Access only for Login-User,issues,"```to restrict the access on demand when deploying an oasis, if one need this extended authorization protection. What do you think?","In principle what you suggest is doable, but we would need to investigate quite a lot of time to properly check and ensure that this flag correctly protects all of the endpoints etc.",,2025-02-28T08:48:27.056000Z,15,17,18,Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5
Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q23-a25-26,0.64,discord_thread_heuristic,Unexpected dependency version on NOMAD develop,issues,- Does this also apply eventually to staging and production when the versions are bumped up?,"- So, if I update a plugin dependency, I need to also change the requirements file on nomad-FAIR?",,2025-01-24T11:39:53.165000Z,23,25,26,Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c
New plugin does not show up in a nomad-distro-dev|2025-01-21T16:08:15.456000Z|9f9ff6cfc7|q6-a8-9,0.64,discord_thread_heuristic,New plugin does not show up in a nomad-distro-dev,issues,"- if yes, did you scroll down the drop-down menu opening when you click ""create from schema"" and checking if your classes are in the ""other"" category?",the repository is linked to my first posted message. For the first question I haven't created a parser just a schema with a normalize function that parse automatically a csv file and in the git repository I was able to create the json file normalize.archive. My class is not in any category so I cannot find it.,,2025-01-21T16:08:15.456000Z,6,8,9,New plugin does not show up in a nomad-distro-dev|2025-01-21T16:08:15.456000Z|9f9ff6cfc7
"When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702|q8-a10-11",0.64,discord_thread_heuristic,"When registering a new account, the confirmation email does not arrive",issues,So there still is an adress that needs verification? What is the adress?,I manually validated you email. You should be able to use your account now.,,2025-01-16T10:34:58.468000Z,8,10,11,"When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702"
Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q7-a9-10,0.64,discord_thread_heuristic,Uploads are getting directly stuck in nomad-distro-dev,issues,"If I adjust the log level, where do I get more information logged. I cannot access the Logs tab, because I have no entry in the upload. Is this logged directly in the terminal?",This sounds like the worker has not correctly started. I would look at the docker logs and also check that the worker service has started correctly,,2025-01-09T14:23:39.252000Z,7,9,10,Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9
Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q28-a33-34,0.64,discord_thread_heuristic,Memory error when I build my container,issues,"I can reproduce the issue, so it works if you comment out your own plugin?",https://github.com/PauloGitHB/nomad-distro-template.git,,2024-11-15T09:44:36.042000Z,28,33,34,Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d
NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520|q9-a14-15,0.64,discord_thread_heuristic,NOMAD Oasis Requests Takes a lot of Time from Institute Intranet,issues,"I think to be really used in lab environment, this is a must because it is very common that lab computers mostly do not have access to public internet. I do not know how other institutes that using Oasis handles this. Any comment about IKZ maybe?",So I do tests in offline local development environment (**client and server is same machine**) with clear memory/disk cache and hard reload. The result is that it works without any delays. There is only two requests that are done to public internet (https://unpkg.com/pace-js https://cdn.jsdelivr.net/npm/mathjax and failure of this does not effect the loading time or any functionality.,,2024-10-29T16:31:40.296000Z,9,14,15,NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520
Uploads to NOMAD never finish processing|2024-10-21T08:04:29.181000Z|ffe6f2bd9f|q27-a29-30,0.64,discord_thread_heuristic,Uploads to NOMAD never finish processing,issues,"Yes, it looks very good. All entries have been processed successfully. Thank you very much. Could you also reset and reprocess `hhKM5SazSY2ZA0ExJQRAaA`?","Great, that one is now also reprocessed, please check. As you suggest, I think we will need a mechanism where the users can forcefully reset the processing state after some timeout.",,2024-10-21T08:04:29.181000Z,27,29,30,Uploads to NOMAD never finish processing|2024-10-21T08:04:29.181000Z|ffe6f2bd9f
"Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd|q16-a18-19",0.64,discord_thread_heuristic,"Uploads are not displayed, despite being in the volumes folder",issues,"okay, but I think I can at least have a look at POST /uploads/bundle to move data from my real Oasis to my local test Oasis, right?","Not sure, but I think I should be able to set it up.",,2024-10-09T19:25:25.125000Z,16,18,19,"Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd"
Failed publishing for some uploads|2024-09-19T13:10:32.860000Z|1a37e4d2da|q5-a10-11,0.64,discord_thread_heuristic,Failed publishing for some uploads,issues,"The develop distribution is using atomisticparser v1.0.1, was the upload processed using that version of the parser?",https://nomad-lab.eu/prod/v1/develop/gui/user/uploads/upload/id/jHOZwqYFTpy3Ucc5YlSb0w,,2024-09-19T13:10:32.860000Z,5,10,11,Failed publishing for some uploads|2024-09-19T13:10:32.860000Z|1a37e4d2da
More a question than an issue: data curation.|2024-09-18T08:20:59.171000Z|31ca795375|q15-a20-21,0.64,discord_thread_heuristic,More a question than an issue: data curation.,issues,"> * If you change affiliation, can you update it in your datasets?","Author affiliation can be changed, since it is not tied to an upload/dataset. We are currently having some problems with the form that allows you to do it, this is discussed here: https://discord.com/channels///threads/",,2024-09-18T08:20:59.171000Z,15,20,21,More a question than an issue: data curation.|2024-09-18T08:20:59.171000Z|31ca795375
inconsistencies between develop deployment and local development|2024-09-17T10:20:47.494000Z|5c8eda6b6f|q19-a21-22,0.64,discord_thread_heuristic,inconsistencies between develop deployment and local development,issues,"I wonder though if it is already useful to announce these changes (even if they are subject to further change) in some way other then individually talking with each dev/domain expert who is trying to update things and runs into problems. E.g., you could make a post in #software-updates and then follow up on it when things change again, ?","I think announcing major or breaking changes in #software-updates makes sense. But we also need to figure out a way how to create a filtered release log for the documentation that you can consult about deprecations, breaking changes, etc. I will put the topic on the agenda for the #plugins-taskforce meetings.",,2024-09-17T10:20:47.494000Z,19,21,22,inconsistencies between develop deployment and local development|2024-09-17T10:20:47.494000Z|5c8eda6b6f
TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference|2024-06-18T13:26:34.564000Z|d04b45f526|q4-a5-6,0.64,discord_thread_heuristic,TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference,issues,"when I went to check the error I was assuming that the HDF5 utility needed to be updated, but then I saw at some point the Gromacs parser was partially updated (at least it was changed from my initial implementation), so I assumed you had updated it, but I guess it was somewhere in the middle of your implementation. Thanks, I will update now","sometimes I forget to update nomad submodules whenever I do changes in the parsers, Recently I have to do this for the runschema",,2024-06-18T13:26:34.564000Z,4,5,6,TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference|2024-06-18T13:26:34.564000Z|d04b45f526
improve usability of term widgets with many entries|2024-04-26T09:36:34.943000Z|bb02bfd4ac|q0-a2-3,0.64,discord_thread_heuristic,improve usability of term widgets with many entries,issues,"I would like a way to explore the terms in a term widget that are not showing as the top x items. Currently the only way is to extend the size of the widget, which quickly becomes impractial. Is it possible to add a scrollbar to explore the less frequent appearing terms?",How difficult would it be? Should I open an issue for this? Thanks!,,2024-04-26T09:36:34.943000Z,0,2,3,improve usability of term widgets with many entries|2024-04-26T09:36:34.943000Z|bb02bfd4ac
Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q21-a25-26,0.63,discord_thread_heuristic,Memory error when I build my container,issues,can you try commenting out the plugins you might not need in the pyproject.toml file? the error seems valid though,https://github.com/PauloGitHB/nomad-oasis.git,,2024-11-15T09:44:36.042000Z,21,25,26,Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d
FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q101-a113-114,0.62,discord_thread_heuristic,FHIaims parsing issues,issues,"This seems to work, but I wonder if this is correct? However, if I update some dependencies I need to somehow update the uv.lock first, right? (this happens somehow automatically on the github, but I can't see through github workflow that much to understand what is exactly going on)? How do I do that step locally?","So for example `nomad-lab[parsing, infrastructure]==1.3.16` works, but `nomad-lab[parsing, infrastructure] @ git+https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR.git does not even though it is the same version (will crash on start due to missing /opt/venv/lib/python3.12/site-packages/nomad/app/static/gui). So I assume I'm still missing some extra steps...",,2025-07-24T12:22:12.739000Z,101,113,114,FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93
failing plugin install-and-test workflow in github|2025-06-23T06:21:45.025000Z|ac78218ece|q0-a3-4,0.62,discord_thread_heuristic,failing plugin install-and-test workflow in github,issues,suddenly the automated testing pipeline for my plugin fails. Any Idea why suddenly mongoengine is not installed any more during the install dependencies step?,"For future reference, I was using an outdated workflow file and still had old python requirement, which only started to fail now. using the updated workflow `actions.yml` from the cookiecutter-nomad-plugin repo fixed the issues.",,2025-06-23T06:21:45.025000Z,0,3,4,failing plugin install-and-test workflow in github|2025-06-23T06:21:45.025000Z|ac78218ece
Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q51-a57-59,0.62,discord_thread_heuristic,Create multiple entry from parser or new entry in normalizer,issues,Yes I'm working with spinbot! How did you get that?,"```This is required as precursor is an `entity` (NOMAD base sections definition), it shouldn't be created every time a new mainfile is parsed. Instead, should be added to the new entries as a reference. This cannot be done in`is_mainfile` as the function is not able to read other `EntryArchives`. Perhaps this gives you a bit of inspiration for that? https://github.com/FAIRmat-NFDI/nomad-perovskite-solar-cells-database/blob/main/src/perovskite_solar_cell_database/composition.py#L481",,2025-06-14T07:31:20.113000Z,51,57,59,Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab
Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271|q8-a66-67,0.62,discord_thread_heuristic,Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage,issues,I have now reset and reprocessed the uploads you mentioned. Could you check if they are fine now?,"Note that the fixes are enabled only in our `develop` deployment, which contains nightly updates for our development purposes. So if you want to reprocess something yourself, you need to go to: http://nomad-lab.eu/prod/v1/develop/gui",,2025-05-16T07:42:17.401000Z,8,66,67,Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271
testing workflows|2025-05-06T15:35:10.871000Z|5bc10667ca|q2-a22-23,0.62,discord_thread_heuristic,testing workflows,issues,"I saw a testing.py script from Chema in the neb repo, which looks like it is possible to resolve the references from the workflow through the terminal, but I am not sure how to use it, or how to do something similar. Looking at the testing_AlCo2S4.py file in [tests](https://github.com/FAIRmat-NFDI/nomad-neb-workflows/blob/develop/tests/test_AlCo2S4.py), is there a way to run this and inspect an output for the script? Maybe has some tips?",```m_def: nomad_neb_workflows.schema_packages.neb.NEBWorkflow inputs: - name: Input structure for initial image section: '../upload/archive/mainfile/neb0.traj#/run/0/'```,,2025-05-06T15:35:10.871000Z,2,22,23,testing workflows|2025-05-06T15:35:10.871000Z|5bc10667ca
testing workflows|2025-05-06T15:35:10.871000Z|5bc10667ca|q23-a50-51,0.62,discord_thread_heuristic,testing workflows,issues,"in the example you posted you are not referencing the mainfile, but an entry, and this is the name I give it in the parsing step before, right?","i think the correct reference format is `/uploads/{upload_id}/archive/{entry_id}#/run/0/`. The first one `../upload/archive/mainfile/{mainfile}#/run/0` should work . It is possible that the mainfile is not correct, maybe have a look at the raw files under .volumes",,2025-05-06T15:35:10.871000Z,23,50,51,testing workflows|2025-05-06T15:35:10.871000Z|5bc10667ca
Plugin-Development: How to self-reference a class with ReferenceEditQuantity|2025-04-10T10:20:37.111000Z|50175a2089|q1-a19-20,0.62,discord_thread_heuristic,Plugin-Development: How to self-reference a class with ReferenceEditQuantity,issues,"During plugin development I stumbled about a question, and maybe somebody knows a solution for that: How to self-reference a class with ReferenceEditQuantity?",You can see an example of this in the https://nomad-lab.eu/prod/v1/gui/analyze/metainfo/nomad/section_definitions which is a `CompositeSystem`,,2025-04-10T10:20:37.111000Z,1,19,20,Plugin-Development: How to self-reference a class with ReferenceEditQuantity|2025-04-10T10:20:37.111000Z|50175a2089
distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q10-a55-56,0.62,discord_thread_heuristic,distro-dev not running on latest develop,issues,That is still not working for me 😦 Do you have any idea what could be wrong ?,"I am getting the same errors in our pipelines. Another option would be to update setuptools (see https://github.com/pypa/wheel/blob/main/README.rst), but I am not sure if that breaks many other things",,2025-04-01T12:49:10.385000Z,10,55,56,distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4
ArchiveQuery raising errors in nomad-lab v1.3.15|2025-03-26T15:19:44.146000Z|c02881671c|q26-a29-30,0.62,discord_thread_heuristic,ArchiveQuery raising errors in nomad-lab v1.3.15,issues,I think it was running with the older nomad-lab version after the 18th but I'm not sure. It could just be that no user on that Oasis used it between Tuesday the 18th and Friday the 21st when I updated nomad-lab. Maybe can say if he was running it succesfully in that time?,It looks to me that `ArchiveQuery` tries to login as admin to retrieve data. To do this you have to have the admin password which is not available in any jupyterhub deployment. TLDR: ArchiveQuery should work without admin priviledges.,,2025-03-26T15:19:44.146000Z,26,29,30,ArchiveQuery raising errors in nomad-lab v1.3.15|2025-03-26T15:19:44.146000Z|c02881671c
Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q97-a131-132,0.62,discord_thread_heuristic,Unexpected error in the research app,issues,We obtained these warnings in the worker at the beginning but successively the application actually started. So what could the problem be?,"I am using that same use case (headless server for nomad-distro-dev), and added a note to the readme about the required port forwardings: https://github.com/FAIRmat-NFDI/nomad-distro-dev/blob/21a1566fc1d8ced3757d2d2b60ecea64f24f4b58/README.md?plain=1#L311",,2025-02-28T07:57:57.339000Z,97,131,132,Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b
Oasis crashes when uploading multiple NeXus .hdf5 files|2025-02-13T15:36:13.213000Z|31f7b310d4|q4-a7-8,0.62,discord_thread_heuristic,Oasis crashes when uploading multiple NeXus .hdf5 files,issues,* Which version of Oasis and plugins are you using?,"for your response, i will try to provide a more detailed account of our setup and tests:",,2025-02-13T15:36:13.213000Z,4,7,8,Oasis crashes when uploading multiple NeXus .hdf5 files|2025-02-13T15:36:13.213000Z|31f7b310d4
Using HDF5|2025-01-09T17:23:00.110000Z|d6f076aaf8|q14-a24-25,0.62,discord_thread_heuristic,Using HDF5,issues,can you help here?,"p.parse('tests/data/example.out', a, logger=logging.getLogger())",,2025-01-09T17:23:00.110000Z,14,24,25,Using HDF5|2025-01-09T17:23:00.110000Z|d6f076aaf8
Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q25-a44-45,0.62,discord_thread_heuristic,Uploads are getting directly stuck in nomad-distro-dev,issues,"that's odd, can you try running git describe locally?","but this time, open the repo inside the container and run `git submodule update --init --recurisve` inside the container",,2025-01-09T14:23:39.252000Z,25,44,45,Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9
Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4|q22-a25-26,0.62,discord_thread_heuristic,Can't get dev installation to work,issues,Did you try `docker compose down --volumes` or do you have data in your local that you don't want to loose?,this was also now on a computer that never run nomad before,,2024-12-16T09:34:40.791000Z,22,25,26,Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4
Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff|q8-a47-48,0.62,discord_thread_heuristic,Permission denied when creating new Jupyter Notebook in uploads folder,issues,Maybe somewhere there is the problem?,Then it looks like your Jupyter image is somehow incorrectly using the root user. Is your Oasis based on [nomad-distro-template](https://github.com/FAIRmat-NFDI/nomad-distro-template)? Fekete: I see [in our Dockerfile](https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/39509dfe85640b8a1b25923a040668a8d92cdb98/Dockerfile#L203) that the user is set to $NB_UID in the Jupyter image. Does this default to 1000..?,,2024-11-20T11:04:36.071000Z,8,47,48,Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff
Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff|q28-a47-48,0.62,discord_thread_heuristic,Permission denied when creating new Jupyter Notebook in uploads folder,issues,Any idea why this could be?,Then it looks like your Jupyter image is somehow incorrectly using the root user. Is your Oasis based on [nomad-distro-template](https://github.com/FAIRmat-NFDI/nomad-distro-template)? Fekete: I see [in our Dockerfile](https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/39509dfe85640b8a1b25923a040668a8d92cdb98/Dockerfile#L203) that the user is set to $NB_UID in the Jupyter image. Does this default to 1000..?,,2024-11-20T11:04:36.071000Z,28,47,48,Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff
The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q89-a101-102,0.62,discord_thread_heuristic,The new oasis-template doesn't works,issues,Why not localhost?,"set this issue as ""resolved"" 👍",,2024-10-04T13:34:29.120000Z,89,101,102,The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8
Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q57-a76-77,0.62,discord_thread_heuristic,Adapting existing Oasis to new plugin mechanism,issues,any reason why you don't create a MR to add that to nomad?,"I now commented on <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1610> and <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1955>. The relevant people here, should be part of the respective issues. My idea would be to work on the ""button"" and on the action you do (e.g. creating entries, updating entries) together and to finally document such a use-base with a how-to in the documentation.",,2024-10-01T12:39:26.289000Z,57,76,77,Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459
dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db|q40-a43-44,0.62,discord_thread_heuristic,dynamic appearance of app widgets,issues,I understand. do you think it is possible to have the result of a JMESPath query in the axis or widget title? e.g. reactants[0].name ?,I would also not want to try and make the title dynamic: that is also another rabbithole that might never end.,,2024-09-19T10:21:05.589000Z,40,43,44,dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db
Failure to parse simulations from the newest GROMACS version|2024-09-03T11:41:26.426000Z|6495d4932f|q2-a5-6,0.62,discord_thread_heuristic,Failure to parse simulations from the newest GROMACS version,issues,the report! Indeed that sounds like a problem we could solve with a library update. you know the GROMACS parser quite well: do you think we could simply try to update MDAnalysis to solve this?,"Then I will open an issue to investigate upgrading MDAnalysis, as this could cause issues downstream",,2024-09-03T11:41:26.426000Z,2,5,6,Failure to parse simulations from the newest GROMACS version|2024-09-03T11:41:26.426000Z|6495d4932f
Tutorial 12 and nomad-as-a-data-management-framework-tutorial- example - few problems.|2024-08-26T06:49:00.797000Z|edcd5aaead|q5-a16-17,0.62,discord_thread_heuristic,Tutorial 12 and nomad-as-a-data-management-framework-tutorial- example - few problems.,issues,"""ModuleNotFoundError: No module named 'runschema'"" for example. So it could come back again to this *schema *package or function being deprecated?","The main problems are now fixed on our ""alpha"" deployment: https://nomad-lab.eu/prod/v1/develop/gui/about/information If you want, you can try out the notebook there.",,2024-08-26T06:49:00.797000Z,5,16,17,Tutorial 12 and nomad-as-a-data-management-framework-tutorial- example - few problems.|2024-08-26T06:49:00.797000Z|edcd5aaead
"Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.|2024-04-02T14:04:10.722000Z|8eb0f03f1d|q8-a36-37",0.62,discord_thread_heuristic,"Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.",issues,"By the way, this are the logs of the app container for the Validation Error. Maybe this gives some more insights?","`The token is not yet valid (iat)` sounds like a time(zone) issue. E.g. our user management and your oasis not running in sync. Your laptop being behind. When you open a new tab, you start a new instance of the gui and it will get a new (potentially out of time) token. So every time you open a new tab there is the potential for this to happen. Could you check your os time settings?",,2024-04-02T14:04:10.722000Z,8,36,37,"Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.|2024-04-02T14:04:10.722000Z|8eb0f03f1d"
