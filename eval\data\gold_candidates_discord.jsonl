{"id": "Problem with nomad-FAIR package in nomad-distro-dev|2025-07-01T13:16:01.556000Z|54e7621230|q39-a40-41", "thread_id": "Problem with nomad-FAIR package in nomad-distro-dev|2025-07-01T13:16:01.556000Z|54e7621230", "title": "Problem with nomad-FAIR package in nomad-distro-dev", "section": "issues", "question": "Can you try with another port then?", "proposed_answer": "You can modify the start command and pass an `--app-port` argument", "source_url": null, "timestamp": "2025-07-01T13:16:01.556000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 39, "a_start": 40, "a_end": 41}
{"id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q68-a69-70", "thread_id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab", "title": "Create multiple entry from parser or new entry in normalizer", "section": "issues", "question": "Do you know how I can create the precursor entry if it not exist in parser or normalizer other than doing that in `Parser.is_mainfile()`?", "proposed_answer": "I do it like this https://github.com/nomad-hzb/nomad-baseclasses/blob/014e7017e25176193db8e86200979cfdae9b77c3/src/baseclasses/helper/utilities.py#L296 but hampus is working on an alternative. I basicalky Check if a file with the given Name already exists in the Upload and if Not I create", "source_url": null, "timestamp": "2025-06-14T07:31:20.113000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 68, "a_start": 69, "a_end": 70}
{"id": "Deploy plugin in nomad_distro_template|2025-05-22T14:01:28.017000Z|f8333e7572|q2-a3-4", "thread_id": "Deploy plugin in nomad_distro_template|2025-05-22T14:01:28.017000Z|f8333e7572", "title": "Deploy plugin in nomad_distro_template", "section": "issues", "question": "Is the image used in the docker compose file using your repository's image?", "proposed_answer": "This [line](https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/docker-compose.yaml#L95) should point to your repository's image instead of the `fairmat-nfdi/nomad-distro-template:main` one.", "source_url": null, "timestamp": "2025-05-22T14:01:28.017000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 2, "a_start": 3, "a_end": 4}
{"id": ".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad|2025-05-21T06:40:45.023000Z|405ef159e3|q1-a3-4", "thread_id": ".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad|2025-05-21T06:40:45.023000Z|405ef159e3", "title": ".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad", "section": "issues", "question": "Is there a more automated and efficient way to do the above described procedure while testing schemas with the web-ui of central-NOMAD?", "proposed_answer": "You can use the api directly to upload your files easily. All you need is a token and you are all set `http://localhost:8000/fairdi/nomad/latest/api/v1/extensions/docs#/auth/get_token_auth_token_post`.", "source_url": null, "timestamp": "2025-05-21T06:40:45.023000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 1, "a_start": 3, "a_end": 4}
{"id": ".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad|2025-05-21T06:40:45.023000Z|405ef159e3|q9-a10-11", "thread_id": ".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad|2025-05-21T06:40:45.023000Z|405ef159e3", "title": ".hdf5 files (own made .h5, .h5iona, ...) for publication on central nomad", "section": "issues", "question": "is it possible to store the chemical_formula in your example to make this entry searchable with the \"Elements / Formula\"-filter int the main GUI? Is there any doc about that?", "proposed_answer": "in order to make the elements searchable through the periodic table from `Elements/Formula`, you would need to use the results section normalizer which could help, but the way you are using the data section, it is not possible to achieve that via the central nomad for now unless we define a new `app` to make the custom quantity searchable", "source_url": null, "timestamp": "2025-05-21T06:40:45.023000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 9, "a_start": 10, "a_end": 11}
{"id": "Setup error on \"nomad-distro-dev\"|2025-01-08T14:54:17.574000Z|58cb8ee208|q15-a16-17", "thread_id": "Setup error on \"nomad-distro-dev\"|2025-01-08T14:54:17.574000Z|58cb8ee208", "title": "Setup error on \"nomad-distro-dev\"", "section": "issues", "question": "could you have a look here??", "proposed_answer": "an issue with python3.12 earlier versions. If you run `uv python install 3.12.8` and try again it should work", "source_url": null, "timestamp": "2025-01-08T14:54:17.574000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 15, "a_start": 16, "a_end": 17}
{"id": "Setup error on \"nomad-distro-dev\"|2025-01-08T14:54:17.574000Z|58cb8ee208|q42-a43-44", "thread_id": "Setup error on \"nomad-distro-dev\"|2025-01-08T14:54:17.574000Z|58cb8ee208", "title": "Setup error on \"nomad-distro-dev\"", "section": "issues", "question": "Can you try pinning directly to 3.12.8 and see if that works?", "proposed_answer": "`uv python pin 3.12.8` followed by `uv run poe setup`", "source_url": null, "timestamp": "2025-01-08T14:54:17.574000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 42, "a_start": 43, "a_end": 44}
{"id": "Dataset DOI link -> DOI NOT FOUND|2024-09-25T14:03:54.522000Z|2c3add8ea0|q14-a15-16", "thread_id": "Dataset DOI link -> DOI NOT FOUND|2024-09-25T14:03:54.522000Z|2c3add8ea0", "title": "Dataset DOI link -> DOI NOT FOUND", "section": "issues", "question": "Are there some updates here? is it possible to get a registered doi on an oasis?", "proposed_answer": "Here's another dead DOI link from https://dx.doi.org//NOMAD/-1", "source_url": null, "timestamp": "2024-09-25T14:03:54.522000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 14, "a_start": 15, "a_end": 16}
{"id": "GET request on processing upload corrupts it|2024-08-20T05:49:44.814000Z|ea7351ce1e|q15-a16-17", "thread_id": "GET request on processing upload corrupts it|2024-08-20T05:49:44.814000Z|ea7351ce1e", "title": "GET request on processing upload corrupts it", "section": "issues", "question": "Could you provide some script+data that I could use as a starting point to reproduce this,?", "proposed_answer": "test API endpoint. I've tried to work out an MWE to share with you. Interestingly, I can't reproduce the issue with my MWE. I can indeed upload an immediately query without corrupting the data. There must be something intricate/subtle that happens with my larger code, and I'm not sure what it is at the moment. Let's close this issue for now, because I can't reproduce it in a simple MWE, and also because I can simply use a short `sleep` in my larger code to alleviate the problem", "source_url": null, "timestamp": "2024-08-20T05:49:44.814000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 15, "a_start": 16, "a_end": 17}
{"id": "docker-compose up error with nomad-lab 3.11 setup|2024-08-13T12:18:42.333000Z|98252609bf|q29-a30-31", "thread_id": "docker-compose up error with nomad-lab 3.11 setup|2024-08-13T12:18:42.333000Z|98252609bf", "title": "docker-compose up error with nomad-lab 3.11 setup", "section": "issues", "question": "Is there somewhere to check these type of dependencies/versions which are not part of the pyproject/requirements setup?", "proposed_answer": "Maybe here: https://nomad-lab.eu/prod/v1/develop/docs/howto/develop/setup.html#install-docker", "source_url": null, "timestamp": "2024-08-13T12:18:42.333000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 29, "a_start": 30, "a_end": 31}
{"id": "set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851|q3-a4-5", "thread_id": "set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851", "title": "set default display unit for arrays read from file", "section": "issues", "question": "What can I do if I want to keep the vector view as shown and not convert it into a `NumberEditQuantity`?", "proposed_answer": "I just realize, that in the [documentation](https://nomad-lab.eu/prod/v1/staging/docs/reference/annotations.html#eln-annotations) it says that the `defaultDisplayUnit` is deprecated.", "source_url": null, "timestamp": "2024-08-05T10:01:34.812000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 3, "a_start": 4, "a_end": 5}
{"id": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c|q6-a7-8", "thread_id": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c", "title": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis", "section": "issues", "question": "Is Keycloak 16.1.1 really still supported?", "proposed_answer": "I assume they have discontinued this repository. The offiicial docs of keycloak are now using a different one. I have not yet tested it, but i assume the images are the same. Try to exchange `jboss/keycloak:16.1.1` with `quay.io/keycloak/keycloak:16.1.1`. Let me know if this works please, so we can also change our documentation.", "source_url": null, "timestamp": "2024-03-14T16:47:18.591000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 6, "a_start": 7, "a_end": 8}
{"id": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c|q18-a20-21", "thread_id": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c", "title": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis", "section": "issues", "question": "what can we do to fix?", "proposed_answer": "This is probably due to the Jupyterhub update. What do the logs of the `nomad_oasis_north` container say? `docker logs nomad_oasis_north`. Have a look at the docs and try this: https://nomad-lab.eu/prod/v1/staging/docs/howto/oasis/migrate.html#to-122", "source_url": null, "timestamp": "2024-03-14T16:47:18.591000Z", "method": "discord_thread_heuristic", "score": 1.0, "q_line": 18, "a_start": 20, "a_end": 21}
{"id": "Problem with nomad-FAIR package in nomad-distro-dev|2025-07-01T13:16:01.556000Z|54e7621230|q2-a4-5", "thread_id": "Problem with nomad-FAIR package in nomad-distro-dev|2025-07-01T13:16:01.556000Z|54e7621230", "title": "Problem with nomad-FAIR package in nomad-distro-dev", "section": "issues", "question": "can you share the output of `uv pip list`?", "proposed_answer": "Can you try updating the h5grove library? `uv sync --upgrad-package h5grove`", "source_url": null, "timestamp": "2025-07-01T13:16:01.556000Z", "method": "discord_thread_heuristic", "score": 0.99, "q_line": 2, "a_start": 4, "a_end": 5}
{"id": "How to open the forked nomad-distro-dev rep in VSCode correctly (windows 11)?|2025-06-05T13:00:56.126000Z|40125d32a5|q10-a12-13", "thread_id": "How to open the forked nomad-distro-dev rep in VSCode correctly (windows 11)?|2025-06-05T13:00:56.126000Z|40125d32a5", "title": "How to open the forked nomad-distro-dev rep in VSCode correctly (windows 11)?", "section": "issues", "question": "Do you have any idea what I might be doing wrong?", "proposed_answer": "this clears up some things! Does Joshua need to somehow sign in to start using the Dev container? I've never used this feature as I work on a Linux machine and run our docker compose file (`nomad-distro-dev/docker-compose.yaml`) directly with docker.", "source_url": null, "timestamp": "2025-06-05T13:00:56.126000Z", "method": "discord_thread_heuristic", "score": 0.99, "q_line": 10, "a_start": 12, "a_end": 13}
{"id": "Error: mypy==1.0.1|2025-06-03T11:05:49.035000Z|af9d90a504|q6-a8-9", "thread_id": "Error: mypy==1.0.1|2025-06-03T11:05:49.035000Z|af9d90a504", "title": "Error: mypy==1.0.1", "section": "issues", "question": "are unsatisfiable. ..while, before the uv run poe setup command, the python version was >=1.15 in all the pyproject.toml files. I have also noticed that, upon uv run poe setup the data-time of several files (incuding pyproject.toml ) has been updated. Is it a known issue?", "proposed_answer": "We have updated the setup command to not include the submodule update step anymore: https://github.com/FAIRmat-NFDI/nomad-distro-dev/blob/main/pyproject.toml#L51", "source_url": null, "timestamp": "2025-06-03T11:05:49.035000Z", "method": "discord_thread_heuristic", "score": 0.99, "q_line": 6, "a_start": 8, "a_end": 9}
{"id": "Broken properties card|2025-01-29T12:22:46.606000Z|2156e2b13a|q4-a6-7", "thread_id": "Broken properties card|2025-01-29T12:22:46.606000Z|2156e2b13a", "title": "Broken properties card", "section": "issues", "question": "do you have a test upload that i can recreate the error locally?", "proposed_answer": "you can use the following published entry to compare the difference in the data type for the quantity in the screenshots: https://nomad-lab.eu/prod/v1/develop/gui/entry/id/ziJhgrU_TNWbOcgs7g1vt1YX5xxL", "source_url": null, "timestamp": "2025-01-29T12:22:46.606000Z", "method": "discord_thread_heuristic", "score": 0.99, "q_line": 4, "a_start": 6, "a_end": 7}
{"id": "Dataset DOI link -> DOI NOT FOUND|2024-09-25T14:03:54.522000Z|2c3add8ea0|q22-a24-25", "thread_id": "Dataset DOI link -> DOI NOT FOUND|2024-09-25T14:03:54.522000Z|2c3add8ea0", "title": "Dataset DOI link -> DOI NOT FOUND", "section": "issues", "question": "What do you mean with \"manually add a doi to a dataset\"?", "proposed_answer": "now i have this `/NOMAD/-1` doi also in our oasis: https://nomad-hzb-se.de/nomad-oasis/gui/user/datasets", "source_url": null, "timestamp": "2024-09-25T14:03:54.522000Z", "method": "discord_thread_heuristic", "score": 0.99, "q_line": 22, "a_start": 24, "a_end": 25}
{"id": "Issues with NOMAD-Sintering Tutorial and NOMAD Oasis Docker Configuration|2024-06-10T11:05:16.439000Z|a0001b31a3|q9-a11-12", "thread_id": "Issues with NOMAD-Sintering Tutorial and NOMAD Oasis Docker Configuration|2024-06-10T11:05:16.439000Z|a0001b31a3", "title": "Issues with NOMAD-Sintering Tutorial and NOMAD Oasis Docker Configuration", "section": "issues", "question": "How can we determine which plugin to use when reading a file and how do we navigate graphs on NOMAD?", "proposed_answer": "1. What do you mean by \"reading a file\"? Do you mean when you upload a file to NOMAD? What parser gets called is decide by the matching criteria. The simplest criteria is to match on either the file name/ending or the mime type of the file using the `MatchingParser` class. You can find more information about this in the docs here: https://nomad-lab.eu/prod/v1/docs/explanation/processing.html#matching", "source_url": null, "timestamp": "2024-06-10T11:05:16.439000Z", "method": "discord_thread_heuristic", "score": 0.99, "q_line": 9, "a_start": 11, "a_end": 12}
{"id": "questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173|q17-a20-21", "thread_id": "questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173", "title": "questions about nomad-distro-dev", "section": "issues", "question": "how do I rebase my local nomad-FAIR branch? Can I do this in the nomad-distro-dev? Or do I need to open nomad-FAIR folder in an extra VSC window?", "proposed_answer": "Or you can `cd packages/nomad-FAIR` and run `git` commands and it should work for the nomad repo", "source_url": null, "timestamp": "2025-01-16T11:42:39.899000Z", "method": "discord_thread_heuristic", "score": 0.97, "q_line": 17, "a_start": 20, "a_end": 21}
{"id": "Setup error on \"nomad-distro-dev\"|2025-01-08T14:54:17.574000Z|58cb8ee208|q86-a89-90", "thread_id": "Setup error on \"nomad-distro-dev\"|2025-01-08T14:54:17.574000Z|58cb8ee208", "title": "Setup error on \"nomad-distro-dev\"", "section": "issues", "question": "What's the docker version?", "proposed_answer": "`docker` itself comes with the compose command https://docs.docker.com/reference/cli/docker/compose/", "source_url": null, "timestamp": "2025-01-08T14:54:17.574000Z", "method": "discord_thread_heuristic", "score": 0.97, "q_line": 86, "a_start": 89, "a_end": 90}
{"id": "2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d|q112-a115-116", "thread_id": "2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d", "title": "2D array of strings", "section": "issues", "question": "What is `path-to-a-valid-file`? Also, do you have some configuration to recognize that specific file so that your parser class `MyParser` is being called?", "proposed_answer": "`path-to-a-valid-file` was just here to say that the CLI needs a file that is recognised by the parser. For instance, in case this is replaced by `../../Examples_from_AMP_partners/KUL/4grains2x4x3_compressionY.hdf5` and I have the `mainfile_name_re` argument in the `__init__.py` of the parser set to `'.*/.hdf5'` so that it will recognize the file.", "source_url": null, "timestamp": "2024-06-11T08:27:23.680000Z", "method": "discord_thread_heuristic", "score": 0.97, "q_line": 112, "a_start": 115, "a_end": 116}
{"id": "Suggestions in Inputfields|2025-09-03T09:15:45.189000Z|2e3ea963c1|q7-a8-9", "thread_id": "Suggestions in Inputfields|2025-09-03T09:15:45.189000Z|2e3ea963c1", "title": "Suggestions in Inputfields", "section": "issues", "question": "is there a possibility to enable this for custom quantities?", "proposed_answer": "Julia is correct. We only collect suggestions for data stored in `results`. Because the custom quantities use a different storage mechanism (which allows better scaling to multiple schemas + base class searches), adding suggestions to them does not work in the same way.", "source_url": null, "timestamp": "2025-09-03T09:15:45.189000Z", "method": "discord_thread_heuristic", "score": 0.96, "q_line": 7, "a_start": 8, "a_end": 9}
{"id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q62-a63-64", "thread_id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1", "title": "Reproducibility indicators bug from afm nexus file", "section": "issues", "question": "Which links can store the sensor sections and which can not?", "proposed_answer": "The issue to link `cantilever_oscillators` most probably coming from the `NXcomponent`. Let see if this is the same issue for other sensors as well.", "source_url": null, "timestamp": "2025-08-11T09:40:29.449000Z", "method": "discord_thread_heuristic", "score": 0.96, "q_line": 62, "a_start": 63, "a_end": 64}
{"id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q22-a23-24", "thread_id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8", "title": "The new oasis-template doesn't works", "section": "issues", "question": "Can you run the script as mentioned here?", "proposed_answer": "This will update the `docker-compose.yml` file", "source_url": null, "timestamp": "2024-10-04T13:34:29.120000Z", "method": "discord_thread_heuristic", "score": 0.96, "q_line": 22, "a_start": 23, "a_end": 24}
{"id": "set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851|q0-a1-2", "thread_id": "set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851", "title": "set default display unit for arrays read from file", "section": "issues", "question": "Is it possible to set a 'defaultDisplayUnit' for an array that was read in from a file?", "proposed_answer": "I know I can set a 'defaultDisplayUnit' for quantities for which I define the component with `a_eln = ELNAnnotation(component='NumberEditQuantity', defaultDisplayUnit='hour')`", "source_url": null, "timestamp": "2024-08-05T10:01:34.812000Z", "method": "discord_thread_heuristic", "score": 0.96, "q_line": 0, "a_start": 1, "a_end": 2}
{"id": "NOMAD LAB broken?!|2024-06-07T11:12:45.374000Z|dc54c82581|q12-a20-21", "thread_id": "NOMAD LAB broken?!|2024-06-07T11:12:45.374000Z|dc54c82581", "title": "NOMAD LAB broken?!", "section": "issues", "question": "Are you uploading to the same `upload` or a fresh `upload`?", "proposed_answer": "I have an upload which might have the same problem caused by a nexus parsed entry. The code that handles dynamic search quantities in nexus entries throws an exception when the definition of a quantity is not there anymore. I guess something might have been removed/changed in the latest nexus changes. In my case it falls over a quantity called `energy_calibration`. I try to [provide a fix](<https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/1903>) that simply ignores quantities for missing definitions. Its a bit cumbersome to do this, because I can only test this agains the problematic data on the deployment. I report back, if I know more.", "source_url": null, "timestamp": "2024-06-07T11:12:45.374000Z", "method": "discord_thread_heuristic", "score": 0.96, "q_line": 12, "a_start": 20, "a_end": 21}
{"id": "Standard way of writing `m_proxy_value`|2024-04-10T17:08:30.668000Z|169773661f|q5-a6-7", "thread_id": "Standard way of writing `m_proxy_value`|2024-04-10T17:08:30.668000Z|169773661f", "title": "Standard way of writing `m_proxy_value`", "section": "issues", "question": "Is there some reason for this discrepancy? Can we stick with one form?", "proposed_answer": "I came across this because I am comparing the `m_proxy_values` to identify identical references, and because of this discrepancy, I get different results before and after the `MetainfoNormalizer` is run.", "source_url": null, "timestamp": "2024-04-10T17:08:30.668000Z", "method": "discord_thread_heuristic", "score": 0.96, "q_line": 5, "a_start": 6, "a_end": 7}
{"id": "Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3|q13-a17-18", "thread_id": "Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3", "title": "Solve `mongo_user_group` warning in the application startup", "section": "issues", "question": "Can you show the the result of `docker ps`?", "proposed_answer": "You will probably have to use `--host localhost`.", "source_url": null, "timestamp": "2025-07-16T15:57:41.003000Z", "method": "discord_thread_heuristic", "score": 0.95, "q_line": 13, "a_start": 17, "a_end": 18}
{"id": "runing nomad dev distro taking ~3 min to load|2024-11-28T16:25:22.079000Z|34153e48cf|q3-a5-7", "thread_id": "runing nomad dev distro taking ~3 min to load|2024-11-28T16:25:22.079000Z|34153e48cf", "title": "runing nomad dev distro taking ~3 min to load", "section": "issues", "question": "Is it faster if you run `uv run nomad admin run appworker`?", "proposed_answer": "So I tried with `uv run -vv poe start` and this time it took 5 min 😅 I think one solution would be to reduce the number of plugins included. By default, we have included all of the plugins that are part of the central nomad system: https://github.com/FAIRmat-NFDI/nomad-distro-dev/blob/main/pyproject.toml#L10-L27, but you can exclude which ever ones that are not relevant to your work.", "source_url": null, "timestamp": "2024-11-28T16:25:22.079000Z", "method": "discord_thread_heuristic", "score": 0.95, "q_line": 3, "a_start": 5, "a_end": 7}
{"id": "Inconsistent Ruff behavior|2024-05-28T12:41:10.993000Z|300b4983a2|q10-a15-17", "thread_id": "Inconsistent Ruff behavior|2024-05-28T12:41:10.993000Z|300b4983a2", "title": "Inconsistent Ruff behavior", "section": "issues", "question": "What did you have as the original code? The below version?", "proposed_answer": "`ruff format .` also \"fixes\" it in the same way as `ruff check --fix` The formatter isn't going to `sort` imports according to this: https://docs.astral.sh/ruff/formatter/#sorting-imports", "source_url": null, "timestamp": "2024-05-28T12:41:10.993000Z", "method": "discord_thread_heuristic", "score": 0.95, "q_line": 10, "a_start": 15, "a_end": 17}
{"id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q62-a64-65", "thread_id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab", "title": "Create multiple entry from parser or new entry in normalizer", "section": "issues", "question": "is the precursor entry then in a different upload? or are all precursors in one upload shared with everyone?", "proposed_answer": "you can design the create system in such a way that it only creates the entry if it doesnt exist, since each entry has a file name and if the filename can be derived from the precursor_id then you can just check in that upload if upload already contains such a file. filename could be then `f\"{precursor_id}.archive.json\"`", "source_url": null, "timestamp": "2025-06-14T07:31:20.113000Z", "method": "discord_thread_heuristic", "score": 0.94, "q_line": 62, "a_start": 64, "a_end": 65}
{"id": "Uploads stuck in processing phase|2025-04-16T08:48:04.351000Z|d95a1a1177|q5-a7-8", "thread_id": "Uploads stuck in processing phase|2025-04-16T08:48:04.351000Z|d95a1a1177", "title": "Uploads stuck in processing phase", "section": "issues", "question": "If you have updated your nomad-distro version, there might be a discrepancy somewhere. You need to check the logs in your app container to get a better view. do you see any error?", "proposed_answer": "There are other options as well, please check documentation on : `https://nomad-lab.eu/prod/v1/docs/reference/cli.html#nomad-admin`", "source_url": null, "timestamp": "2025-04-16T08:48:04.351000Z", "method": "discord_thread_heuristic", "score": 0.94, "q_line": 5, "a_start": 7, "a_end": 8}
{"id": "Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41|q9-a11-12", "thread_id": "Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41", "title": "Updating distro-dev after pydantic update", "section": "issues", "question": "What's your uv version?", "proposed_answer": "Yeah I think you'll have to upgrade uv first. If you installed using `curl .. | sh ..` you can use `uv self update`", "source_url": null, "timestamp": "2025-02-10T08:34:08.970000Z", "method": "discord_thread_heuristic", "score": 0.94, "q_line": 9, "a_start": 11, "a_end": 12}
{"id": "Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99|q28-a30-31", "thread_id": "Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99", "title": "Could not find a version that satisfies the requirement networkx==3.3", "section": "issues", "question": "which message of discussion do you was to point out, Nathan?", "proposed_answer": "`sudo dnf install python3.11-devel` for fedora", "source_url": null, "timestamp": "2024-08-19T11:26:40.185000Z", "method": "discord_thread_heuristic", "score": 0.94, "q_line": 28, "a_start": 30, "a_end": 31}
{"id": "Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c|q2-a5-7", "thread_id": "Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c", "title": "Failing test in plugin", "section": "issues", "question": "Does anyone have hints on how to solve this problem?", "proposed_answer": "https://github.com/FAIRmat-NFDI/nomad-schema-plugin-nmr/blob/824fa87a6c395ecf77867f6b89d7a44fb90e095b/src/nomad_nmr_schema/schema_packages/schema_package.py#L370 Maybe this is causing the schema not to be loaded properly. Can you try defining the `init` method as:", "source_url": null, "timestamp": "2025-04-14T08:48:46.049000Z", "method": "discord_thread_heuristic", "score": 0.93, "q_line": 2, "a_start": 5, "a_end": 7}
{"id": "Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99|q11-a16-17", "thread_id": "Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99", "title": "Could not find a version that satisfies the requirement networkx==3.3", "section": "issues", "question": "which python are you using, Ahmed?", "proposed_answer": "https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/Dockerfile?ref_type=heads we install all of the same requirements in Docker on python 3.11", "source_url": null, "timestamp": "2024-08-19T11:26:40.185000Z", "method": "discord_thread_heuristic", "score": 0.93, "q_line": 11, "a_start": 16, "a_end": 17}
{"id": "nomad_oasis_app container unhealthy|2024-05-30T08:08:19.843000Z|df3de3b692|q47-a52-53", "thread_id": "nomad_oasis_app container unhealthy|2024-05-30T08:08:19.843000Z|df3de3b692", "title": "nomad_oasis_app container unhealthy", "section": "issues", "question": "Is it enough to `docker compose pull` afterwards or do I have to re-download the package?", "proposed_answer": "Thats my fault. His MR, but my commit. I wanted to run nomad from a different directory (e.g. the `infra` in the nomad-gui). That was supposed to fix a probem, but I did not account that `__file__` will change to `../site-packages/..` during install and we do not manifest/copy the example uploads.", "source_url": null, "timestamp": "2024-05-30T08:08:19.843000Z", "method": "discord_thread_heuristic", "score": 0.93, "q_line": 47, "a_start": 52, "a_end": 53}
{"id": "Data section of entries in GUI not showing up|2025-09-04T10:36:29.135000Z|0966f48f60|q2-a3-5", "thread_id": "Data section of entries in GUI not showing up|2025-09-04T10:36:29.135000Z|0966f48f60", "title": "Data section of entries in GUI not showing up", "section": "issues", "question": "can you please take a look?", "proposed_answer": "The mainfile of the entry `*.archive.json` contains the data section Created a MR to revert the change: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2625", "source_url": null, "timestamp": "2025-09-04T10:36:29.135000Z", "method": "discord_thread_heuristic", "score": 0.92, "q_line": 2, "a_start": 3, "a_end": 5}
{"id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q117-a118-121", "thread_id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93", "title": "FHIaims parsing issues", "section": "issues", "question": "Are there any specific changes that require a custom GUI that could not be included in the main package?", "proposed_answer": "I'm not sure I understand the question properly. I have some local nomad-lab gui patches, but they are probably not critical for the work I'm doing right now. I have however some other patches (normalizing, datamodel) which I need and it would be also nice to have an image with the fixes from https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2536 Anyway, this is getting offtopic, from the FHIaims issue I'll open a new thread where I'll try to describe in more detail what I'm trying to achieve here (and my ultimate goals). I can confirm the patches you provided work (I could not test it in the oasis, but it fixes running `nomad parse <outfile>` locally).", "source_url": null, "timestamp": "2025-07-24T12:22:12.739000Z", "method": "discord_thread_heuristic", "score": 0.92, "q_line": 117, "a_start": 118, "a_end": 121}
{"id": "Changing ELN data in a parsed non-json entry breaks the parsing.|2025-05-19T15:39:38.226000Z|fe69444cc5|q23-a26-27", "thread_id": "Changing ELN data in a parsed non-json entry breaks the parsing.|2025-05-19T15:39:38.226000Z|fe69444cc5", "title": "Changing ELN data in a parsed non-json entry breaks the parsing.", "section": "issues", "question": "Why should underscores not be allowed in the name?", "proposed_answer": "When you create a new entry from AGE_sample, since there is no `name` populated by the user yet, the internal normalizer will pick the name from the `entry_name`, replaces all underscores with white spaces and fill the name.", "source_url": null, "timestamp": "2025-05-19T15:39:38.226000Z", "method": "discord_thread_heuristic", "score": 0.92, "q_line": 23, "a_start": 26, "a_end": 27}
{"id": "GUI does not show \"repeats\" subsection, visibility and order does not work.|2025-05-15T11:50:54.826000Z|574d77fa31|q12-a15-16", "thread_id": "GUI does not show \"repeats\" subsection, visibility and order does not work.|2025-05-15T11:50:54.826000Z|574d77fa31", "title": "GUI does not show \"repeats\" subsection, visibility and order does not work.", "section": "issues", "question": "How can we hide them? has already mentioned that `visible` flag is not doing anything. Is there other otion(s)?", "proposed_answer": "you can use the `eln` annotation on the section definition for now until we have a fix for display:", "source_url": null, "timestamp": "2025-05-15T11:50:54.826000Z", "method": "discord_thread_heuristic", "score": 0.92, "q_line": 12, "a_start": 15, "a_end": 16}
{"id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q139-a140-142", "thread_id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b", "title": "Unexpected error in the research app", "section": "issues", "question": "Is it sufficient to substitute the first line in the file with the second one to resolve any problem?", "proposed_answer": "The issue with celsius units was fixed in this MR https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2380. 1.3.16dev170 might have the fix, not sure. Our latest release candidate version 1.3.16rc1 for sure has the fix. Could you tell more about what git repository are you referring to? Is this happening in your copy of `nomad-distro-template?`", "source_url": null, "timestamp": "2025-02-28T07:57:57.339000Z", "method": "discord_thread_heuristic", "score": 0.92, "q_line": 139, "a_start": 140, "a_end": 142}
{"id": "Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app|2025-08-07T09:10:48.009000Z|5e08ea8db6|q1-a2-3", "thread_id": "Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app|2025-08-07T09:10:48.009000Z|5e08ea8db6", "title": "Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app", "section": "issues", "question": "When running the app, only one of the menus is working at a given moment, the other one is present but greyed-out and can not be interacted with. If I only have one of the menus, either of them works. Is there a way to have both simultaneously?", "proposed_answer": "Changed the channel name: Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app", "source_url": null, "timestamp": "2025-08-07T09:10:48.009000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 1, "a_start": 2, "a_end": 3}
{"id": "Tabular parser row chemical_formula|2025-06-12T09:58:22.006000Z|b6699b3fd8|q25-a26-27", "thread_id": "Tabular parser row chemical_formula|2025-06-12T09:58:22.006000Z|b6699b3fd8", "title": "Tabular parser row chemical_formula", "section": "issues", "question": "do you also get the following errors when accessing the main .yaml file (in my case tabular-parser-row-mode_mod_JS-2_corr_indentation.archive.yaml)?", "proposed_answer": "you should be able to call the section Sample, it is not protected. If you call it Sample, remember to also change the type in line 65 to '#/Sample'", "source_url": null, "timestamp": "2025-06-12T09:58:22.006000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 25, "a_start": 26, "a_end": 27}
{"id": "Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5|q31-a32-33", "thread_id": "Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5", "title": "Protected Oasis - Restrict GUI/API Access only for Login-User", "section": "issues", "question": "What would make sense is the option to use your own key_cloak, and restrict access to only logged in users. Is that what you want?", "proposed_answer": "I know the name of the scientists working for the Collaborative Research Centre - that not a problem at all. I also use a local keycloak instance to keep track of them and to provide the access (and to delete if necessary). In the future I will also bind the keycloak with our university \"identity management system\" to use a more official login/access control. (If students or PI leave they will automatically loose the account and cannot access the oasis - and vice versa if a new student arrives).", "source_url": null, "timestamp": "2025-02-28T08:48:27.056000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 31, "a_start": 32, "a_end": 33}
{"id": "Warnings and Errors while installing NOMAD|2025-02-18T13:24:07.009000Z|22dfbe511e|q1-a2-3", "thread_id": "Warnings and Errors while installing NOMAD|2025-02-18T13:24:07.009000Z|22dfbe511e", "title": "Warnings and Errors while installing NOMAD", "section": "issues", "question": "How to resolve these?", "proposed_answer": "I'd recommend using nomad-distro-dev to setup a local development environment https://github.com/FAIRmat-NFDI/nomad-distro-dev", "source_url": null, "timestamp": "2025-02-18T13:24:07.009000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 1, "a_start": 2, "a_end": 3}
{"id": "Unit Registry|2024-12-19T15:34:54.028000Z|c5631a8352|q4-a5-6", "thread_id": "Unit Registry|2024-12-19T15:34:54.028000Z|c5631a8352", "title": "Unit Registry", "section": "issues", "question": "do we have ampere_hour registered in our unit system?", "proposed_answer": "You can also check in the nomad-fair repo the dir nomad/units", "source_url": null, "timestamp": "2024-12-19T15:34:54.028000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 4, "a_start": 5, "a_end": 6}
{"id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q58-a59-60", "thread_id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d", "title": "Memory error when I build my container", "section": "issues", "question": "is this the plugin you want to load?", "proposed_answer": "https://github.com/PauloGitHB/sintering_plugin", "source_url": null, "timestamp": "2024-11-15T09:44:36.042000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 58, "a_start": 59, "a_end": 60}
{"id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q95-a96-97", "thread_id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d", "title": "Memory error when I build my container", "section": "issues", "question": "How much RAM do you have available?", "proposed_answer": "From the default plugins listed here: https://github.com/PauloGitHB/nomad-oasis-cemes/blob/main/pyproject.toml#L24-L41", "source_url": null, "timestamp": "2024-11-15T09:44:36.042000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 95, "a_start": 96, "a_end": 97}
{"id": "In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c|q2-a3-4", "thread_id": "In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c", "title": "In and ELN lane, the rendering of API response in json is overlapping with fields.", "section": "issues", "question": "can you have a look?", "proposed_answer": "Yes. I started working on a fix for the problem where the visual overlap here: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2194", "source_url": null, "timestamp": "2024-11-07T15:03:16.142000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 2, "a_start": 3, "a_end": 4}
{"id": "Clarify Display Docs|2024-11-01T14:15:53.587000Z|3650b87949|q26-a27-28", "thread_id": "Clarify Display Docs|2024-11-01T14:15:53.587000Z|3650b87949", "title": "Clarify Display Docs", "section": "issues", "question": "Could you please send me your schema?", "proposed_answer": "https://github.com/FAIRmat-NFDI/nomad-perovskite-solar-cells-database/blob/22-extend-ion-schema/src/perovskite_solar_cell_database/composition.py#L227", "source_url": null, "timestamp": "2024-11-01T14:15:53.587000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 26, "a_start": 27, "a_end": 28}
{"id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q86-a87-88", "thread_id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8", "title": "The new oasis-template doesn't works", "section": "issues", "question": "Which url are you using to access the website?", "proposed_answer": "http://theIPofmymachine/nomad-oasis/gui/", "source_url": null, "timestamp": "2024-10-04T13:34:29.120000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 86, "a_start": 87, "a_end": 88}
{"id": "GET request on processing upload corrupts it|2024-08-20T05:49:44.814000Z|ea7351ce1e|q13-a14-15", "thread_id": "GET request on processing upload corrupts it|2024-08-20T05:49:44.814000Z|ea7351ce1e", "title": "GET request on processing upload corrupts it", "section": "issues", "question": "are you using to upload the data originally?", "proposed_answer": "The data should definitely not be mutated/corrupted by a GET request, so we have to somehow reproduce and fix this. As a workaround, you can keep on using `sleep`.", "source_url": null, "timestamp": "2024-08-20T05:49:44.814000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 13, "a_start": 14, "a_end": 15}
{"id": "k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525|q6-a7-8", "thread_id": "k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525", "title": "k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing", "section": "issues", "question": "2 - Obviously the k-mesh will be completely wrong for non-uniform grids, but hopefully this should be fringe enough to not be a big concern, but another check that the k-points are uniformly spaced could be not that difficult?", "proposed_answer": "3 - if the k_mesh.grid is provided but not the points, the points will be reconstructed (at least for Gamma-centered mesh) https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/normalizing/method.py?ref_type=heads#L378 `points = np.meshgrid(*[np.linspace(0, 1, n) for n in k_mesh.grid])`. This however will create the full grid, so again will be inconsistent with the parsers which provide the symmetry reduced ones into `points` , so IMO we should save this one into `all_points` and duplicate them into points only in case of P1 symmetry.", "source_url": null, "timestamp": "2024-07-12T10:18:27.642000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 6, "a_start": 7, "a_end": 8}
{"id": "The Pipeline of the Example Oasis failed.|2024-07-10T08:57:00.118000Z|2ba075a4a9|q1-a2-3", "thread_id": "The Pipeline of the Example Oasis failed.|2024-07-10T08:57:00.118000Z|2ba075a4a9", "title": "The Pipeline of the Example Oasis failed.", "section": "issues", "question": "Should I just try to rerun the pipeline or can you see the underlying problem?", "proposed_answer": "https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-demo-oasis-distribution/-/jobs", "source_url": null, "timestamp": "2024-07-10T08:57:00.118000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 1, "a_start": 2, "a_end": 3}
{"id": "Worker fails to start with latest develop|2024-07-10T08:56:51.543000Z|ef9f3ee105|q5-a6-7", "thread_id": "Worker fails to start with latest develop|2024-07-10T08:56:51.543000Z|ef9f3ee105", "title": "Worker fails to start with latest develop", "section": "issues", "question": "is this likely the same reason the example oasis deployment failed?", "proposed_answer": "Yes, Markus created an issue for this: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2067", "source_url": null, "timestamp": "2024-07-10T08:56:51.543000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 5, "a_start": 6, "a_end": 7}
{"id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q63-a69-70", "thread_id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57", "title": "HDF5 in NOMAD", "section": "issues", "question": "Can you please have also another look at your example at GitHub and the corresponding message above?", "proposed_answer": "I have just tested it on our production deployment `https://nomad-lab.eu/prod/v1/gui/about/information`. Please have a look at the attached video and let me know if you are facing issues still.", "source_url": null, "timestamp": "2024-05-27T13:17:35.844000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 63, "a_start": 69, "a_end": 70}
{"id": "Referencing another Upload is not allowed|2024-04-02T11:24:50.663000Z|d4a693cc22|q2-a3-4", "thread_id": "Referencing another Upload is not allowed|2024-04-02T11:24:50.663000Z|d4a693cc22", "title": "Referencing another Upload is not allowed", "section": "issues", "question": "Can I ask you more info on why this happens?", "proposed_answer": "It is quite hard to say without additional information. Looks like the schema/parser is doing some actions in the `ServerContext` that causes this, not sure if it is a bug or a feature. Maybe has more insight but I think we would need some way to reproduce this.", "source_url": null, "timestamp": "2024-04-02T11:24:50.663000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 2, "a_start": 3, "a_end": 4}
{"id": "Documentation for elabFTW and Chemotion integration|2024-03-28T09:18:53.204000Z|0e5de407af|q0-a1-2", "thread_id": "Documentation for elabFTW and Chemotion integration|2024-03-28T09:18:53.204000Z|0e5de407af", "title": "Documentation for elabFTW and Chemotion integration", "section": "issues", "question": "Do we have a tutorial or learning page (or any other resources) on how to use elabftw and chemotion integration in NOMAD?", "proposed_answer": "https://nomad-lab.eu/prod/v1/staging/docs/howto/manage/eln.html#elabftw-integration", "source_url": null, "timestamp": "2024-03-28T09:18:53.204000Z", "method": "discord_thread_heuristic", "score": 0.91, "q_line": 0, "a_start": 1, "a_end": 2}
{"id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q67-a71-72", "thread_id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1", "title": "Reproducibility indicators bug from afm nexus file", "section": "issues", "question": "Is this the same file, that contains the links to the sensors?", "proposed_answer": "Now, I have found what was the problem behind the link. There are no fields are available inside the target group (in this case `height_piezo_sensor`). If you add a field in the target group, then linking to the section works. I would say still it is a bug should be resolved in NOMAD.", "source_url": null, "timestamp": "2025-08-11T09:40:29.449000Z", "method": "discord_thread_heuristic", "score": 0.9, "q_line": 67, "a_start": 71, "a_end": 72}
{"id": "Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67|q12-a14-16", "thread_id": "Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67", "title": "Adding plugin to `nomad-distro-dev` fails", "section": "issues", "question": "Can you share some verbose logs?", "proposed_answer": "Ah i think it's because the package is named `nomad-simulation-parsers` bit awkward to have a different name here https://github.com/FAIRmat-NFDI/nomad-parser-plugins-simulation/blob/develop/pyproject.toml#L16C9-L16C33", "source_url": null, "timestamp": "2025-06-13T08:52:00.065000Z", "method": "discord_thread_heuristic", "score": 0.9, "q_line": 12, "a_start": 14, "a_end": 16}
{"id": "Referencing PlotSection: Error \"The item \"figures:0\" could not be found.\"|2025-04-22T09:46:09.559000Z|702cce7a9c|q3-a54-55", "thread_id": "Referencing PlotSection: Error \"The item \"figures:0\" could not be found.\"|2025-04-22T09:46:09.559000Z|702cce7a9c", "title": "Referencing PlotSection: Error \"The item \"figures:0\" could not be found.\"", "section": "issues", "question": "Is there any way to adjust the code?", "proposed_answer": ": Now I tried the code with version `1.3.16` and drafted a [docker image](https://github.com/Bondoki/NOMAD_Oasis_template/tree/plottesting) with the code you provided as [plugin](https://github.com/Bondoki/PlotSectionSchema), with the corresponding [images](https://github.com/Bondoki?tab=packages&repo_name=NOMAD_Oasis_template) for testing purpose. Unfortunately, it's still the same behavior: Then I \"create from Schema\" the `OverviewClass`, select the \"plus button\" to create a subsection `Measurement Example` and hit save, firstly the plot is generated, but secondly if I use the \"Overview-tab\" to \"view the data in the archive\", I got the same error `The item \"figures:0\" could not be found.` This behavior seems to be reminiscent to the general schema class. On the other side, if I create only a `Measurement Example` entity and access the data from that overview tab, everything is working.", "source_url": null, "timestamp": "2025-04-22T09:46:09.559000Z", "method": "discord_thread_heuristic", "score": 0.9, "q_line": 3, "a_start": 54, "a_end": 55}
{"id": "2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d|q8-a26-27", "thread_id": "2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d", "title": "2D array of strings", "section": "issues", "question": "Which lead to my question: Is there a way to keep the 2D structure of string arrays ?", "proposed_answer": "To clarify things, with Python, I access this array, Iload the HDF5 file using `h5py` and I use this \"path\": `file['cell_to']['phase']` (with `cell_to` being the name of the group and `phase` the name of the dataset concerned). I use `silx` (https://www.silx.org/) to visualize the HDF5 and it says that this dataset is of type `compound`, which I don't really know of.", "source_url": null, "timestamp": "2024-06-11T08:27:23.680000Z", "method": "discord_thread_heuristic", "score": 0.9, "q_line": 8, "a_start": 26, "a_end": 27}
{"id": "The need of strg+shift+R|2024-03-01T13:43:17.859000Z|22dfec3545|q0-a11-12", "thread_id": "The need of strg+shift+R|2024-03-01T13:43:17.859000Z|22dfec3545", "title": "The need of strg+shift+R", "section": "issues", "question": "what the expierence with nomad updates is. When i am updating plugins reloads are not helping but we have to do strg+shift+R. Is it possible to force a proper reload of nomad also when plugins have changes? is someone else experiencing that?", "proposed_answer": "If you open the `artifacts.js` alone (e.g. new tab with \"https://nomad-lab.eu/prod/v1/gui/artifacts.js\") and if you wait for 60 seconds before sending, the network tab should always look like this:", "source_url": null, "timestamp": "2024-03-01T13:43:17.859000Z", "method": "discord_thread_heuristic", "score": 0.9, "q_line": 0, "a_start": 11, "a_end": 12}
{"id": "nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52|q34-a36-37", "thread_id": "nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52", "title": "nomad-oasis can not stablely be started", "section": "issues", "question": "what should this part be specified?", "proposed_answer": "This part is the metadata for your deployment and I don't think this part is causing your issue: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/config/models/config.py?ref_type=heads#L249-306", "source_url": null, "timestamp": "2025-07-17T08:58:19.106000Z", "method": "discord_thread_heuristic", "score": 0.89, "q_line": 34, "a_start": 36, "a_end": 37}
{"id": "Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3|q0-a2-3", "thread_id": "Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3", "title": "Solve `mongo_user_group` warning in the application startup", "section": "issues", "question": "It should be possible to manage the warnings in the startup of the application?", "proposed_answer": "For the `nomad.utils.structlo` warning, did you try to update `nomad-distro-dev`? [Recently the `pynxtools==0.10.0` pin has been relaxed](https://github.com/FAIRmat-NFDI/nomad-distro-dev/pull/67) and newer version of `pynxtools>=0.10.8` has fixed this warning", "source_url": null, "timestamp": "2025-07-16T15:57:41.003000Z", "method": "discord_thread_heuristic", "score": 0.89, "q_line": 0, "a_start": 2, "a_end": 3}
{"id": "Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e|q13-a15-16", "thread_id": "Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e", "title": "Where to find available base sections for yaml", "section": "issues", "question": "What code are you using?", "proposed_answer": "for DFT? RSPt: https://www.uu.se/en/department/physics-and-astronomy/research/materials-theory/code-development", "source_url": null, "timestamp": "2025-06-02T09:45:34.629000Z", "method": "discord_thread_heuristic", "score": 0.89, "q_line": 13, "a_start": 15, "a_end": 16}
{"id": "NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520|q0-a2-3", "thread_id": "NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520", "title": "NOMAD Oasis Requests Takes a lot of Time from Institute Intranet", "section": "issues", "question": "While I am using my local client to test the [oasis image with keycloak](https://nomad-lab.eu/prod/v1/docs/howto/oasis/install.html#running-nomad), even though I do not have internet I can able to access Oasis. When I deploy the same image on server and while trying to access with a lab computer that only has intranet connection, it takes ages for Oasis to initialize on browser. Mostly request to websites like `unpkg.com` seems to take lots of time. I don't exactly understand the difference between these two scenarios, can someone help me?", "proposed_answer": "The only other that comes to mind is this issue that we had: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2065. You should only have this problem if you are running a version < 1.3.4. Could you check your NOMAD version from the GUI information page: `gui/about/information` (scroll to the section \"About this distribution\")", "source_url": null, "timestamp": "2024-10-29T16:31:40.296000Z", "method": "discord_thread_heuristic", "score": 0.89, "q_line": 0, "a_start": 2, "a_end": 3}
{"id": "SliderEditQuantity in python plugin schema|2024-10-16T06:16:19.077000Z|d38161544c|q7-a9-10", "thread_id": "SliderEditQuantity in python plugin schema|2024-10-16T06:16:19.077000Z|d38161544c", "title": "SliderEditQuantity in python plugin schema", "section": "issues", "question": "can you comment on this?", "proposed_answer": "There is a fix here https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2167 but it has not been merged yet", "source_url": null, "timestamp": "2024-10-16T06:16:19.077000Z", "method": "discord_thread_heuristic", "score": 0.89, "q_line": 7, "a_start": 9, "a_end": 10}
{"id": "Re-adding deleted file does not process|2024-09-19T15:51:19.857000Z|8a906fdb95|q13-a15-16", "thread_id": "Re-adding deleted file does not process|2024-09-19T15:51:19.857000Z|8a906fdb95", "title": "Re-adding deleted file does not process", "section": "issues", "question": "Does the file which you added has the same binary content and the same filename?", "proposed_answer": "https://fairmat-nfdi.github.io/nexus_definitions/classes/contributed_definitions/NXmicrostructure_score_config.html", "source_url": null, "timestamp": "2024-09-19T15:51:19.857000Z", "method": "discord_thread_heuristic", "score": 0.89, "q_line": 13, "a_start": 15, "a_end": 16}
{"id": "dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db|q8-a10-11", "thread_id": "dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db", "title": "dynamic appearance of app widgets", "section": "issues", "question": "how difficult is it to adapt the app with parallel dashboards?", "proposed_answer": "the current catalysis app on the oasis has a lot of widgets on one page. It would be great if we could make it less overwhelming for the user by allowing them to choose the relevant widgets, by e.g. grouping them. The widgets at the moment have several \"themes\" from top to bottom: reaction (name, reactant, product), catalyst properties (elemental comp., preparation method, catalyst type), reaction conditions (temp., gas concentrations, pressure, whsv), reaction results (scatterplots with T vs. Conversion, T vs. Selectivity, 2 plots with Conversion of a specific example reactant vs. Selectivity of a specific product). Especially one of the last 2 plots will be empty if some filter has been set at the top. I think with either parallel dashboards we can keep the whole page less overwhelming or maybe with some \"toggle/hide\" buttons for a group of widgets, so that they only appear if the users chooses so see them.", "source_url": null, "timestamp": "2024-09-19T10:21:05.589000Z", "method": "discord_thread_heuristic", "score": 0.89, "q_line": 8, "a_start": 10, "a_end": 11}
{"id": "Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99|q3-a5-6", "thread_id": "Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99", "title": "Could not find a version that satisfies the requirement networkx==3.3", "section": "issues", "question": "Are you on python3.11?", "proposed_answer": "https://pypi.org/project/networkx/ it's there on pypi for >= 3.10 though", "source_url": null, "timestamp": "2024-08-19T11:26:40.185000Z", "method": "discord_thread_heuristic", "score": 0.89, "q_line": 3, "a_start": 5, "a_end": 6}
{"id": "Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765|q6-a8-9", "thread_id": "Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765", "title": "Problem when `docker compose up -d` an Oasis", "section": "issues", "question": "Can you share your docker compose file?", "proposed_answer": "Here is the `docker-compose.yaml` file I am using and I am working on Ubuntu 20.04.6 LTS", "source_url": null, "timestamp": "2024-08-02T08:23:26.436000Z", "method": "discord_thread_heuristic", "score": 0.89, "q_line": 6, "a_start": 8, "a_end": 9}
{"id": "`ArchiveQuery` fetches nothing in Oasis|2024-04-18T13:22:06.338000Z|161631014e|q3-a5-6", "thread_id": "`ArchiveQuery` fetches nothing in Oasis|2024-04-18T13:22:06.338000Z|161631014e", "title": "`ArchiveQuery` fetches nothing in Oasis", "section": "issues", "question": "What can be wrong here?", "proposed_answer": "https://nomad.support.ikz-berlin.de/nomad-oasis/gui/user/uploads/upload/id/xO04KmCySVCH6hZusB04zA", "source_url": null, "timestamp": "2024-04-18T13:22:06.338000Z", "method": "discord_thread_heuristic", "score": 0.89, "q_line": 3, "a_start": 5, "a_end": 6}
{"id": "entry_references disappeared|2025-05-23T13:02:27.183000Z|7ddeb9a273|q0-a1-2", "thread_id": "entry_references disappeared|2025-05-23T13:02:27.183000Z|7ddeb9a273", "title": "entry_references disappeared", "section": "issues", "question": "i have the weirdest error, the entry_references section is not there anymore? and with this the queries for referenced entries break, das someone has an idea?", "proposed_answer": "I just checked and `entry_references` are still there in `EntryMetadata`. It is still part of the response object and also you should be able to see it by checking the box `all_defined`. There has been a recent change made here https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/datamodel/datamodel.py?ref_type=heads#L1069", "source_url": null, "timestamp": "2025-05-23T13:02:27.183000Z", "method": "discord_thread_heuristic", "score": 0.88, "q_line": 0, "a_start": 1, "a_end": 2}
{"id": "Parsing Polarizabilities and Dipole Moments|2025-01-23T23:42:28.354000Z|5acd63f9fc|q13-a14-15", "thread_id": "Parsing Polarizabilities and Dipole Moments|2025-01-23T23:42:28.354000Z|5acd63f9fc", "title": "Parsing Polarizabilities and Dipole Moments", "section": "issues", "question": "I am not sure if the 27 GB .zip file I uploaded is still there after the Time Limit Error. I can not see it, but it may just be not accessible for me?", "proposed_answer": "2. I uploaded a subset with only 50 entries as a test, that contains polarizabilities as Single Point calculations; and Dipole Moments in the Molecular Dynamics runs. The link to the upload entry is: https://nomad-lab.eu/prod/v1/gui/user/uploads/upload/id/czXVoqV6RYOVXJhZXnTgLA", "source_url": null, "timestamp": "2025-01-23T23:42:28.354000Z", "method": "discord_thread_heuristic", "score": 0.88, "q_line": 13, "a_start": 14, "a_end": 15}
{"id": "Creating multiple uploads causes some to get stuck \"Waiting\"|2024-08-28T11:24:51.603000Z|7bca1ff866|q32-a33-36", "thread_id": "Creating multiple uploads causes some to get stuck \"Waiting\"|2024-08-28T11:24:51.603000Z|7bca1ff866", "title": "Creating multiple uploads causes some to get stuck \"Waiting\"", "section": "issues", "question": "Is it a bug that these uploads get stuck in \"Waiting\" forever?", "proposed_answer": "Yes, I think you could call it a bug. If the worker crashes, there is probably no easy way to somehow know that the status should be reset. Maybe we could run some cleanup job at regular intervals. That would be very good. Because right now it gets stuck and you can't even delete the upload without running the `nomad admin reset-processing`", "source_url": null, "timestamp": "2024-08-28T11:24:51.603000Z", "method": "discord_thread_heuristic", "score": 0.88, "q_line": 32, "a_start": 33, "a_end": 36}
{"id": "nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52|q102-a105-106", "thread_id": "nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52", "title": "nomad-oasis can not stablely be started", "section": "issues", "question": "can I access them inside app? will nomad_oasis_app return a web interface?", "proposed_answer": "https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/configs/nginx_base_conf#L58-L73", "source_url": null, "timestamp": "2025-07-17T08:58:19.106000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 102, "a_start": 105, "a_end": 106}
{"id": "Trigger unpublished upload reprocessing with the API?|2025-07-10T12:37:04.381000Z|bccb1bfe23|q0-a1-2", "thread_id": "Trigger unpublished upload reprocessing with the API?|2025-07-10T12:37:04.381000Z|bccb1bfe23", "title": "Trigger unpublished upload reprocessing with the API?", "section": "issues", "question": "Is there a way to trigger the reprocessing of an unpublished upload via the API?", "proposed_answer": "`/uploads/{upload_id}/action/process`", "source_url": null, "timestamp": "2025-07-10T12:37:04.381000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 0, "a_start": 1, "a_end": 2}
{"id": "Logout button does not logout north|2025-07-05T19:57:47.090000Z|e55802a0dc|q2-a5-6", "thread_id": "Logout button does not logout north|2025-07-05T19:57:47.090000Z|e55802a0dc", "title": "Logout button does not logout north", "section": "issues", "question": "Can you change this?", "proposed_answer": "https://w0l1d.medium.com/implementing-global-logout-in-keycloak-a-comprehensive-guide-c6f22d4034bd", "source_url": null, "timestamp": "2025-07-05T19:57:47.090000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 2, "a_start": 5, "a_end": 6}
{"id": "Issues with user management?|2025-04-09T10:21:47.198000Z|befffd7d94|q24-a27-28", "thread_id": "Issues with user management?|2025-04-09T10:21:47.198000Z|befffd7d94", "title": "Issues with user management?", "section": "issues", "question": "Could you find any solution to this?", "proposed_answer": "as long as this loads https://nomad-lab.eu/prod/v1/gui/search/entries this might be a different problem", "source_url": null, "timestamp": "2025-04-09T10:21:47.198000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 24, "a_start": 27, "a_end": 28}
{"id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q29-a32-33", "thread_id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c", "title": "Unexpected dependency version on NOMAD develop", "section": "issues", "question": "> Does this also apply eventually to staging and production when the versions are bumped up?", "proposed_answer": "We only have the plugins in nomad-FAIR to run the test suite, there's a related issue for this (https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2255). Once those tests are removed, we can remove the `default_plugins.txt` entirely. So the list on nomad-FAIR doesn't affect any deployments.", "source_url": null, "timestamp": "2025-01-24T11:39:53.165000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 29, "a_start": 32, "a_end": 33}
{"id": "When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702|q34-a35-37", "thread_id": "When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702", "title": "When registering a new account, the confirmation email does not arrive", "section": "issues", "question": "do you know how to behave in these cases when the confirmation email does not arrive?", "proposed_answer": "I suppose this is about the realm `fairdi_nomad_test`, right? Have you tried resetting your password on the login page? Otherwise I can look up your user in keycloak. Yes I did try a reset and it still asks me for a email. My user should be GiovanniCastorina", "source_url": null, "timestamp": "2025-01-16T10:34:58.468000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 34, "a_start": 35, "a_end": 37}
{"id": "Remove default search apps and views|2025-01-07T17:06:10.938000Z|a0ef036b18|q1-a4-7", "thread_id": "Remove default search apps and views|2025-01-07T17:06:10.938000Z|a0ef036b18", "title": "Remove default search apps and views", "section": "issues", "question": "How can I get rid of these default apps, and clean up the menu of the AllEnties App?", "proposed_answer": "I believe this was fixed here: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2239 But I'm not sure if we have made a release since then. I'll check. Indeed it seems like the latest release (1.3.13) is from November 29th and the fix by Lauri is from December 2nd. There are dev releases from as recently as 5 days ago though that you could use until the 1.1.14 release is available.", "source_url": null, "timestamp": "2025-01-07T17:06:10.938000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 1, "a_start": 4, "a_end": 7}
{"id": "Remove default search apps and views|2025-01-07T17:06:10.938000Z|a0ef036b18|q21-a24-25", "thread_id": "Remove default search apps and views|2025-01-07T17:06:10.938000Z|a0ef036b18", "title": "Remove default search apps and views", "section": "issues", "question": "Could you link me your dev-distro repo then?", "proposed_answer": "https://github.com/rettigl/nomad-distro-dev", "source_url": null, "timestamp": "2025-01-07T17:06:10.938000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 21, "a_start": 24, "a_end": 25}
{"id": "Scatterplot: focus on region|2024-11-04T13:23:24.664000Z|9bdeb903e8|q2-a5-6", "thread_id": "Scatterplot: focus on region|2024-11-04T13:23:24.664000Z|9bdeb903e8", "title": "Scatterplot: focus on region", "section": "issues", "question": "Can I tests this in the Oasis? Which scatter plot?", "proposed_answer": "Yes, you can test this in the[Test Oasis](https://nomad-lab.eu/prod/v1/oasis/gui/search/heterogeneouscatalyst?results.properties.catalytic.reaction.reaction_conditions.temperature%5Bgte%5D=%20K&results.properties.catalytic.reaction.reaction_conditions.temperature%5Blte%5D=%20K)", "source_url": null, "timestamp": "2024-11-04T13:23:24.664000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 2, "a_start": 5, "a_end": 6}
{"id": "Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin|2024-10-22T10:58:19.936000Z|41985e2c35|q14-a17-18", "thread_id": "Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin|2024-10-22T10:58:19.936000Z|41985e2c35", "title": "Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin", "section": "issues", "question": "what kind of files do you upload?", "proposed_answer": "https://github.com/nomad-hzb/nomad-hysprint/blob/dde2f9eea9de581ba310d4088ddd673e2d14ea27/src/nomad_hysprint/parsers/__init__.py#L21 this is what is matched", "source_url": null, "timestamp": "2024-10-22T10:58:19.936000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 14, "a_start": 17, "a_end": 18}
{"id": "Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7|q36-a39-40", "thread_id": "Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7", "title": "Publishing an entry from Oasis to Central NOMAD", "section": "issues", "question": "If not, is there any other way to reuse our existing parser for manually importing data via e.g. the GUI of nomad-central to allow us to publish and initial data set with DOI?", "proposed_answer": "This is a bit manual but I assume that developing a parser means that the data file format is not recognised by core parsers and so they will not be recognised when uploaded (at least, this is my case). With the YAML and JSON, I can then upload my unrecognised files and create the entry with the data contained in the JSON file, which act as a descriptive tag for the unrecognised files. However, when doing so, I need to point to the schema file in the JSON file with the `m_def` metadata to reference the YAML file with `\"m_def\": \"../uploads/{id}/raw/my_schema.archive.yaml#SectionName\"` (if the schema file is in an other upload, following https://support.hdfgroup.org/documentation/hdf5-docs/advanced_topics/intro_SWMR.html)", "source_url": null, "timestamp": "2024-08-29T07:38:20.591000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 36, "a_start": 39, "a_end": 40}
{"id": "Cache Pub Chem Queries|2024-04-16T12:43:43.375000Z|9bcd1deaf9|q12-a15-16", "thread_id": "Cache Pub Chem Queries|2024-04-16T12:43:43.375000Z|9bcd1deaf9", "title": "Cache Pub Chem Queries", "section": "issues", "question": "Can you share a zoom link?", "proposed_answer": "https://hu-berlin.zoom-x.de/j/?pwd=VUZxOGtCOGo0Vm1WdFczMk1waVk5Zz09", "source_url": null, "timestamp": "2024-04-16T12:43:43.375000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 12, "a_start": 15, "a_end": 16}
{"id": "Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5|q12-a15-16", "thread_id": "Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5", "title": "Accessing Section Definitions via API", "section": "issues", "question": "So, when you deserialize your JSON response into a Python `dict`, can you access `response['data']['archive']['data']`?", "proposed_answer": "You can check them out at our [API dashboard](https://nomad-lab.eu/prod/v1/gui/analyze/apis), but from your use case I assume you want [`entries/archive/query`](https://nomad-lab.eu/prod/v1/api/v1/extensions/docs#/entries%2Farchive).", "source_url": null, "timestamp": "2024-04-12T07:49:39.151000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 12, "a_start": 15, "a_end": 16}
{"id": "Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5|q29-a32-33", "thread_id": "Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5", "title": "Accessing Section Definitions via API", "section": "issues", "question": "is there a more elegant way then to parse this file?", "proposed_answer": "import json resp = requests.get(\"https://nomad-hzb-ce.de/nomad-oasis/gui/artifacts.js\")", "source_url": null, "timestamp": "2024-04-12T07:49:39.151000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 29, "a_start": 32, "a_end": 33}
{"id": "NOMAD can't deal with ISO8601 time formats?|2024-03-15T07:54:51.424000Z|7937b7ad2b|q2-a3-4", "thread_id": "NOMAD can't deal with ISO8601 time formats?|2024-03-15T07:54:51.424000Z|7937b7ad2b", "title": "NOMAD can't deal with ISO8601 time formats?", "section": "issues", "question": "Should we change the time stamp to not include \"+\" or is this something that can be fixed?", "proposed_answer": "This is on https://nomad-lab.eu/prod/v1", "source_url": null, "timestamp": "2024-03-15T07:54:51.424000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 2, "a_start": 3, "a_end": 4}
{"id": "NOMAD can't deal with ISO8601 time formats?|2024-03-15T07:54:51.424000Z|7937b7ad2b|q6-a9-10", "thread_id": "NOMAD can't deal with ISO8601 time formats?|2024-03-15T07:54:51.424000Z|7937b7ad2b", "title": "NOMAD can't deal with ISO8601 time formats?", "section": "issues", "question": "Can you clarify what you mean \"can't deal\"? You cannot upload the file? Through the UI? Or you can upload, but then it is not picked up for processing? Or its processed, but you cannot open it in the GUI?", "proposed_answer": "This helped to pin poin the issue. There is an url encoding problem in our h5grove app. Here is the github issue (https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1952) and the fix (https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/1749).", "source_url": null, "timestamp": "2024-03-15T07:54:51.424000Z", "method": "discord_thread_heuristic", "score": 0.87, "q_line": 6, "a_start": 9, "a_end": 10}
{"id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q57-a59-62", "thread_id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1", "title": "Reproducibility indicators bug from afm nexus file", "section": "issues", "question": "Can you please try it?", "proposed_answer": "Yeah, this is a bug. This is not happening with other SPM family member e.g., STM. In that experiment the `bias_sweep` group of the `reprocibility_indicators` can redirect the reference group properly. But for AFM for some reasons it fails. I will drive deeper why it behaving wired. I've found another possible issue in the scan environment section, where only two sensors are stored by linking. In practice, if I try to store all the three section required by the scan environment section only two of them will be effectively stored. There are a few sensors available to link the sensors exists under the instrument group.", "source_url": null, "timestamp": "2025-08-11T09:40:29.449000Z", "method": "discord_thread_heuristic", "score": 0.86, "q_line": 57, "a_start": 59, "a_end": 62}
{"id": "Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67|q28-a34-35", "thread_id": "Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67", "title": "Adding plugin to `nomad-distro-dev` fails", "section": "issues", "question": "Is it possible that the names `nomad-simulation-parsers` and `nomad-simulations` clash somehow?", "proposed_answer": "By the way, I just had to manually install `nptyping` and `toposort` just now, they apparently are missing from the general installation requirements.", "source_url": null, "timestamp": "2025-06-13T08:52:00.065000Z", "method": "discord_thread_heuristic", "score": 0.86, "q_line": 28, "a_start": 34, "a_end": 35}
{"id": "VSCode debugger for plugins|2024-04-03T10:00:43.669000Z|63b7730b26|q7-a9-12", "thread_id": "VSCode debugger for plugins|2024-04-03T10:00:43.669000Z|63b7730b26", "title": "VSCode debugger for plugins", "section": "issues", "question": "Does it work if you pip install the plugin instead?", "proposed_answer": "Well, I mean the plugin. You mentioned $PYTHONPATH above which I never use. I just pip install the plugin into my venv. Did that first. suggested adding the `$PYTHONPATH` to the venv Okay, then I'm not sure. I haven't run the debugger in a while. Sorry.", "source_url": null, "timestamp": "2024-04-03T10:00:43.669000Z", "method": "discord_thread_heuristic", "score": 0.86, "q_line": 7, "a_start": 9, "a_end": 12}
{"id": "Suggestions in Inputfields|2025-09-03T09:15:45.189000Z|2e3ea963c1|q0-a1-2", "thread_id": "Suggestions in Inputfields|2025-09-03T09:15:45.189000Z|2e3ea963c1", "title": "Suggestions in Inputfields", "section": "issues", "question": "can someone tell me when the Terms WIdgets with Input fields allows suggestions based on already type information?", "proposed_answer": "I believe this is only possible for those terms defined in the results.py file in the central software, which have an added property `a_elasticsearch =[Elasticsearch(suggestion='default'),]` as e.g. here:", "source_url": null, "timestamp": "2025-09-03T09:15:45.189000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 0, "a_start": 1, "a_end": 2}
{"id": "Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67|q6-a7-8", "thread_id": "Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67", "title": "Adding plugin to `nomad-distro-dev` fails", "section": "issues", "question": "Is it in the same `packages/` subdirectory?", "proposed_answer": "Yes, it is. It should be found via `members = [\"packages/*\"]`", "source_url": null, "timestamp": "2025-06-13T08:52:00.065000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 6, "a_start": 7, "a_end": 8}
{"id": "Include PDF in Oasis|2025-06-05T10:04:09.333000Z|d38858399c|q9-a10-12", "thread_id": "Include PDF in Oasis|2025-06-05T10:04:09.333000Z|d38858399c", "title": "Include PDF in Oasis", "section": "issues", "question": "do you mean the browser annotation or which kind of quantity did you use to store the data? do you by any chance still have a some code snippets that i can have a look at?", "proposed_answer": "yes the `a_` annotation `batch_plan_pdf = Quantity(type=str, a_browser=dict(adaptor='RawFileAdaptor'))`", "source_url": null, "timestamp": "2025-06-05T10:04:09.333000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 9, "a_start": 10, "a_end": 12}
{"id": "is the structure display broken in develop?|2025-06-03T11:41:04.468000Z|a8258e1bc2|q1-a2-3", "thread_id": "is the structure display broken in develop?|2025-06-03T11:41:04.468000Z|a8258e1bc2", "title": "is the structure display broken in develop?", "section": "issues", "question": "Could you please share a link?", "proposed_answer": "It seems today the issue resolved itself. not sure if it was an update from my browser or a recent fix, today both develop deployment and most recent develop locally work again for me.", "source_url": null, "timestamp": "2025-06-03T11:41:04.468000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 1, "a_start": 2, "a_end": 3}
{"id": "Can't login to distro-dev|2025-04-14T13:53:10.381000Z|37d17c5a03|q4-a5-6", "thread_id": "Can't login to distro-dev|2025-04-14T13:53:10.381000Z|37d17c5a03", "title": "Can't login to distro-dev", "section": "issues", "question": "Do you use a gmail address?", "proposed_answer": "In fact, I see two entries with your name, one named test. However, they are both in the production realm. That's the one you access when you use the main nomad site.", "source_url": null, "timestamp": "2025-04-14T13:53:10.381000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 4, "a_start": 5, "a_end": 6}
{"id": "Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c|q63-a64-65", "thread_id": "Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c", "title": "Mongo DB collection warning is weird", "section": "issues", "question": "What do you mean with \"did not have anything in the group\"?", "proposed_answer": "That the extra group `mongo_user_group` was empty", "source_url": null, "timestamp": "2025-03-07T10:17:12.565000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 63, "a_start": 64, "a_end": 65}
{"id": "pynxtools with config files - wrong way of passing the config file?|2025-02-27T13:54:49.583000Z|38a2e2719f|q8-a9-10", "thread_id": "pynxtools with config files - wrong way of passing the config file?|2025-02-27T13:54:49.583000Z|38a2e2719f", "title": "pynxtools with config files - wrong way of passing the config file?", "section": "issues", "question": "Is there now a better or different way to pass a config file?", "proposed_answer": "Ok. Small update. The version of pynxtools-xps was not the problem. Now I reinstalled from my local copy of it, and I still get the crash", "source_url": null, "timestamp": "2025-02-27T13:54:49.583000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 8, "a_start": 9, "a_end": 10}
{"id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q19-a20-21", "thread_id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9", "title": "Uploads are getting directly stuck in nomad-distro-dev", "section": "issues", "question": "Can you share a screenshot of the terminal you are using and the error message?", "proposed_answer": "ah you'll have to delete the venv folder and try again", "source_url": null, "timestamp": "2025-01-09T14:23:39.252000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 19, "a_start": 20, "a_end": 21}
{"id": "Setup error on \"nomad-distro-dev\"|2025-01-08T14:54:17.574000Z|58cb8ee208|q133-a134-135", "thread_id": "Setup error on \"nomad-distro-dev\"|2025-01-08T14:54:17.574000Z|58cb8ee208", "title": "Setup error on \"nomad-distro-dev\"", "section": "issues", "question": "What's the output for `docker ps`?", "proposed_answer": "In browser developer console, what's the output for `window.nomadEnv`", "source_url": null, "timestamp": "2025-01-08T14:54:17.574000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 133, "a_start": 134, "a_end": 135}
{"id": "Include new modifications of plugins in the Oasis Docker image|2024-12-12T14:30:13.125000Z|8ee3628afe|q9-a18-19", "thread_id": "Include new modifications of plugins in the Oasis Docker image|2024-12-12T14:30:13.125000Z|8ee3628afe", "title": "Include new modifications of plugins in the Oasis Docker image", "section": "issues", "question": "Are you using any version tags for your plugin?", "proposed_answer": "` \"nomad-txrm-parser @ git+https://github.com/Guillaume-Gaisne/nomad-TXRM_parser.git\",`", "source_url": null, "timestamp": "2024-12-12T14:30:13.125000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 9, "a_start": 18, "a_end": 19}
{"id": "Problem when launching the Oasis from the documentation|2024-12-06T10:51:26.395000Z|a0216bee32|q6-a7-8", "thread_id": "Problem when launching the Oasis from the documentation|2024-12-06T10:51:26.395000Z|a0216bee32", "title": "Problem when launching the Oasis from the documentation", "section": "issues", "question": "Is the link pointing to a wrong version of the oasis, that it is not working nicely ?", "proposed_answer": "I am working with a personal oasis, so I am used to make these steps to create an oasis.", "source_url": null, "timestamp": "2024-12-06T10:51:26.395000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 6, "a_start": 7, "a_end": 8}
{"id": "Error while running the Tutorial|2024-11-19T09:00:42.371000Z|55a178e6ca|q4-a5-6", "thread_id": "Error while running the Tutorial|2024-11-19T09:00:42.371000Z|55a178e6ca", "title": "Error while running the Tutorial", "section": "issues", "question": "Where did you run it?", "proposed_answer": "Directly running it on the example oasis using the clicking on the link", "source_url": null, "timestamp": "2024-11-19T09:00:42.371000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 4, "a_start": 5, "a_end": 6}
{"id": "Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd|q19-a20-21", "thread_id": "Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd", "title": "Uploads are not displayed, despite being in the volumes folder", "section": "issues", "question": "Can you quickly tell me where I have to do this?", "proposed_answer": "Okay then you can try to export bundle using cli and then import to your test oasis using cli as well.", "source_url": null, "timestamp": "2024-10-09T19:25:25.125000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 19, "a_start": 20, "a_end": 21}
{"id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q45-a46-47", "thread_id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8", "title": "The new oasis-template doesn't works", "section": "issues", "question": "What do you mean with my upload ?", "proposed_answer": "If you were uploading a specific file that causes this issue, or if this error comes up when you try to create a new upload", "source_url": null, "timestamp": "2024-10-04T13:34:29.120000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 45, "a_start": 46, "a_end": 47}
{"id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q72-a73-74", "thread_id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8", "title": "The new oasis-template doesn't works", "section": "issues", "question": "Do you see anything in the browser console log?", "proposed_answer": "In chrome it should be right click -> inspect, and you should see that side bar", "source_url": null, "timestamp": "2024-10-04T13:34:29.120000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 72, "a_start": 73, "a_end": 74}
{"id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q76-a77-78", "thread_id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8", "title": "The new oasis-template doesn't works", "section": "issues", "question": "What do you see in network tab?", "proposed_answer": "This is how it looks for me when I click Upload", "source_url": null, "timestamp": "2024-10-04T13:34:29.120000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 76, "a_start": 77, "a_end": 78}
{"id": "Creating multiple uploads causes some to get stuck \"Waiting\"|2024-08-28T11:24:51.603000Z|7bca1ff866|q6-a8-10", "thread_id": "Creating multiple uploads causes some to get stuck \"Waiting\"|2024-08-28T11:24:51.603000Z|7bca1ff866", "title": "Creating multiple uploads causes some to get stuck \"Waiting\"", "section": "issues", "question": "do you have any idea what is happening here? Are we overloading the oasis by pushing too many uploads at the same time?", "proposed_answer": "We don't get the issue when we create the upload in the GUI and use the file dialog to upload the zip file. Running `nomad admin reset-processing` it needed to reset 13 Entry processes in and 14 Upload processes", "source_url": null, "timestamp": "2024-08-28T11:24:51.603000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 6, "a_start": 8, "a_end": 10}
{"id": "Uploading process continues forever!|2024-05-22T12:30:19.255000Z|9e206ccef9|q8-a9-10", "thread_id": "Uploading process continues forever!|2024-05-22T12:30:19.255000Z|9e206ccef9", "title": "Uploading process continues forever!", "section": "issues", "question": "could you have a quick look on it, please?", "proposed_answer": "You need to open the interactive terminal on your nomad-app container and reset the processing.", "source_url": null, "timestamp": "2024-05-22T12:30:19.255000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 8, "a_start": 9, "a_end": 10}
{"id": "Unwanted warning for NumberEditQuantity of unit='dimensionless'|2024-04-30T14:42:44.988000Z|16a758717d|q11-a12-13", "thread_id": "Unwanted warning for NumberEditQuantity of unit='dimensionless'|2024-04-30T14:42:44.988000Z|16a758717d", "title": "Unwanted warning for NumberEditQuantity of unit='dimensionless'", "section": "issues", "question": "Does is work if you simply ommit the unit in the schema?", "proposed_answer": "It works fine in that case, however, the quantity is not of type `pint.Quantity` in that case", "source_url": null, "timestamp": "2024-04-30T14:42:44.988000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 11, "a_start": 12, "a_end": 13}
{"id": "Metainfo Browser for Schema Plugins|2024-04-18T09:36:17.035000Z|227c642bc1|q1-a5-6", "thread_id": "Metainfo Browser for Schema Plugins|2024-04-18T09:36:17.035000Z|227c642bc1", "title": "Metainfo Browser for Schema Plugins", "section": "issues", "question": "The normal metainfo browser should show plugins as a \"source\" when installed. But I guess you want something, where you don't have a nomad installation and just get a browser, e.g. `nomad admin run metainfo-browser`?", "proposed_answer": "For install plugins you can use the normal metainfo browser, e.g. here is the `nomad_measurments` on the example oasis: <https://nomad-lab.eu/prod/v1/oasis/gui/analyze/metainfo/nomad_measurements>. Maybe we can improve the metainfo browser, but there is no plan for an external tool/service.", "source_url": null, "timestamp": "2024-04-18T09:36:17.035000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 1, "a_start": 5, "a_end": 6}
{"id": "Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.|2024-04-02T14:04:10.722000Z|8eb0f03f1d|q4-a5-6", "thread_id": "Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.|2024-04-02T14:04:10.722000Z|8eb0f03f1d", "title": "Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.", "section": "issues", "question": "Does it persist? I get some weird timeout errors when working from home with the NOMAD keycloak which gives the same error. Usually waiting a minute and then closing the error resolves this. If that doesn't work, could you check the logs and see if there is some more info there?", "proposed_answer": "for your answers. Yes the troubleshooting is indeed hard, because the error does not occur in a reproducible manner. Sometimes everything works fine, sometimes not. And I have the feeling that the behaviour also depends on the used browser and if I e.g. open uploads in a new tab or the same tab. Edge seems to work slightly better than Firefox and often in new tabs the error occurs more often.", "source_url": null, "timestamp": "2024-04-02T14:04:10.722000Z", "method": "discord_thread_heuristic", "score": 0.85, "q_line": 4, "a_start": 5, "a_end": 6}
{"id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q1-a3-4", "thread_id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1", "title": "Reproducibility indicators bug from afm nexus file", "section": "issues", "question": "can you please help here?", "proposed_answer": "Image lower left: GUI shows the sections and list of concepts in that section (next to the selected column). In this case, the section has only one field available there which is `cantilever_tip_temperature` that field contains the data and clicking the you may see the data on the nexus file (in most left column).", "source_url": null, "timestamp": "2025-08-11T09:40:29.449000Z", "method": "discord_thread_heuristic", "score": 0.84, "q_line": 1, "a_start": 3, "a_end": 4}
{"id": "displaying non-symmetric tensors in GUI|2025-07-29T14:01:43.413000Z|c94a836dd8|q12-a14-15", "thread_id": "displaying non-symmetric tensors in GUI|2025-07-29T14:01:43.413000Z|c94a836dd8", "title": "displaying non-symmetric tensors in GUI", "section": "issues", "question": "What would be then the best practice for the use cases, in which we wish to skip transposing the tensors in the GUI, because we need exactly the tensor in the archive and showing a `Matrix.T` would be scientifically false?", "proposed_answer": "if we let the parser transpose the data as `Matrix.T`, GUI displays the data correctly ✅ , but the stored data in the archive is now transposed ⛔", "source_url": null, "timestamp": "2025-07-29T14:01:43.413000Z", "method": "discord_thread_heuristic", "score": 0.84, "q_line": 12, "a_start": 14, "a_end": 15}
{"id": "ArchiveQuery raising errors in nomad-lab v1.3.15|2025-03-26T15:19:44.146000Z|c02881671c|q35-a42-43", "thread_id": "ArchiveQuery raising errors in nomad-lab v1.3.15|2025-03-26T15:19:44.146000Z|c02881671c", "title": "ArchiveQuery raising errors in nomad-lab v1.3.15", "section": "issues", "question": "How do you mean?", "proposed_answer": "Also control whether to use api or directly contact keycloak to get token via option `from_api` in archive query.", "source_url": null, "timestamp": "2025-03-26T15:19:44.146000Z", "method": "discord_thread_heuristic", "score": 0.84, "q_line": 35, "a_start": 42, "a_end": 43}
{"id": "Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff|q42-a44-45", "thread_id": "Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff", "title": "Permission denied when creating new Jupyter Notebook in uploads folder", "section": "issues", "question": "Do you have an idea what could be the problem here?", "proposed_answer": "~~Looking at your description, you might be accidentally doing the wrong thing: you need to first navigate inside the upload folder, then right-click and press \"New Notebook\": this will create it inside the folder. The way you described it will try to create it in the \"uploads\" folder which is not permitted. A Jupyter user does not have write priviledges for that.~~", "source_url": null, "timestamp": "2024-11-20T11:04:36.071000Z", "method": "discord_thread_heuristic", "score": 0.84, "q_line": 42, "a_start": 44, "a_end": 45}
{"id": "Help with parse_section()|2024-06-17T08:27:08.519000Z|2a61c0228e|q30-a32-33", "thread_id": "Help with parse_section()|2024-06-17T08:27:08.519000Z|2a61c0228e", "title": "Help with parse_section()", "section": "issues", "question": "Is it preferred not to use m_def?", "proposed_answer": "both are equivalent, so I think you can use whatever feels more comfortable, but probably Theodore knows more", "source_url": null, "timestamp": "2024-06-17T08:27:08.519000Z", "method": "discord_thread_heuristic", "score": 0.84, "q_line": 30, "a_start": 32, "a_end": 33}
{"id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q53-a55-56", "thread_id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57", "title": "HDF5 in NOMAD", "section": "issues", "question": "Why does the processing of this 1.7MB example .h5 file take several minutes?", "proposed_answer": "I also get alway a \"Unexpected error: \"[object Object] (500)\". Please try again and let us know, if this error keeps happening.\" in a red bar in the web-interface when uploading the schema + .h5 file", "source_url": null, "timestamp": "2024-05-27T13:17:35.844000Z", "method": "discord_thread_heuristic", "score": 0.84, "q_line": 53, "a_start": 55, "a_end": 56}
{"id": "pyBIS error when running setup_dev_env.sh|2024-05-13T09:51:10.077000Z|027d978143|q7-a9-11", "thread_id": "pyBIS error when running setup_dev_env.sh|2024-05-13T09:51:10.077000Z|027d978143", "title": "pyBIS error when running setup_dev_env.sh", "section": "issues", "question": "Does `git clone https://github.com/FAIRmat-NFDI/fairmat-pybis.git/` work?", "proposed_answer": "But not during the pip install I'll try again later. Maybe it was something temporary", "source_url": null, "timestamp": "2024-05-13T09:51:10.077000Z", "method": "discord_thread_heuristic", "score": 0.84, "q_line": 7, "a_start": 9, "a_end": 11}
{"id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q82-a87-88", "thread_id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93", "title": "FHIaims parsing issues", "section": "issues", "question": "The deployments are usually the same 1.3.16 version though, but I guess the electronicparser versions might be different?", "proposed_answer": "In the `uv.lock` file it should tell which versions of the packages are installed. Here's the link for the template: https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/uv.lock#L3504", "source_url": null, "timestamp": "2025-07-24T12:22:12.739000Z", "method": "discord_thread_heuristic", "score": 0.83, "q_line": 82, "a_start": 87, "a_end": 88}
{"id": "publishing data from a NOMAD-oasis|2025-05-07T12:27:13.463000Z|6a04a5868f|q8-a18-19", "thread_id": "publishing data from a NOMAD-oasis|2025-05-07T12:27:13.463000Z|6a04a5868f", "title": "publishing data from a NOMAD-oasis", "section": "issues", "question": "How is this currently possible (Publish it on local NOMAD than publishing on/to central NOMAD)?", "proposed_answer": "Regarding the question about to publish in an Oasis. Yes, you can do that (on your own risks and rules), but to generate a DOI in those datasets you will need to configure your NOMAD installation to have a DOI provider (https://nomad-lab.eu/prod/v1/docs/reference/config.html#datacite). Also, I guess you will need to have your Oasis openly available (not only on your intranet).", "source_url": null, "timestamp": "2025-05-07T12:27:13.463000Z", "method": "discord_thread_heuristic", "score": 0.83, "q_line": 8, "a_start": 18, "a_end": 19}
{"id": "Customization of an app|2025-02-24T07:24:54.477000Z|a04c15214a|q2-a7-8", "thread_id": "Customization of an app|2025-02-24T07:24:54.477000Z|a04c15214a", "title": "Customization of an app", "section": "issues", "question": "Is there a way to fix this issue?", "proposed_answer": "https://nomad-lab.eu/prod/v1/docs/howto/plugins/apps.html#column", "source_url": null, "timestamp": "2025-02-24T07:24:54.477000Z", "method": "discord_thread_heuristic", "score": 0.83, "q_line": 2, "a_start": 7, "a_end": 8}
{"id": "Saving bug in old GUI; normalisation to workflow2|2024-12-14T17:44:41.436000Z|d28b697242|q14-a19-20", "thread_id": "Saving bug in old GUI; normalisation to workflow2|2024-12-14T17:44:41.436000Z|d28b697242", "title": "Saving bug in old GUI; normalisation to workflow2", "section": "issues", "question": "What should have happened instead?", "proposed_answer": "We could address this in the MR: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2053", "source_url": null, "timestamp": "2024-12-14T17:44:41.436000Z", "method": "discord_thread_heuristic", "score": 0.83, "q_line": 14, "a_start": 19, "a_end": 20}
{"id": "Parser plugin error|2024-05-24T14:33:02.169000Z|0ce0e65647|q10-a13-15", "thread_id": "Parser plugin error|2024-05-24T14:33:02.169000Z|0ce0e65647", "title": "Parser plugin error", "section": "issues", "question": "do you get the same ?", "proposed_answer": "In my parser class, I had beside the `parse` function a custom `__init__`. had the same issue and I told him to try removing it", "source_url": null, "timestamp": "2024-05-24T14:33:02.169000Z", "method": "discord_thread_heuristic", "score": 0.83, "q_line": 10, "a_start": 13, "a_end": 15}
{"id": "Plugin does not show up in Oasis installation|2024-05-14T11:08:47.371000Z|08ac9da2df|q10-a15-16", "thread_id": "Plugin does not show up in Oasis installation|2024-05-14T11:08:47.371000Z|08ac9da2df", "title": "Plugin does not show up in Oasis installation", "section": "issues", "question": "can you check if there is something in the logs of you nomad app?", "proposed_answer": "[:12:11 +0000] [7] [INFO] Listening at: http://0.0.0.0:8000 (7)", "source_url": null, "timestamp": "2024-05-14T11:08:47.371000Z", "method": "discord_thread_heuristic", "score": 0.83, "q_line": 10, "a_start": 15, "a_end": 16}
{"id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q44-a45-46", "thread_id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1", "title": "Reproducibility indicators bug from afm nexus file", "section": "issues", "question": "again about this point and just to clarification, how is it possible that the groups in the scan_environment section are created correctly? I mean, in that case those groups are defined in the xml without any specification of the quantity to report but when I create the link from the sensors defined in the main part of the instrument I'm able to see all quantities contained in the referenced sections. What are the differences to the case of the NXcomponent class used to refer the oscillator?", "proposed_answer": "Can it be because some of these are explicitly defined in and inherited from `NXspm`? See https://fairmat-nfdi.github.io/nexus_definitions/classes/contributed_definitions/NXspm.html#nxspm-entry-instrument-scan-environment-group", "source_url": null, "timestamp": "2025-08-11T09:40:29.449000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 44, "a_start": 45, "a_end": 46}
{"id": "How to create an Admin Account on our Oasis?|2025-07-16T11:33:32.132000Z|79c53f52a5|q3-a4-5", "thread_id": "How to create an Admin Account on our Oasis?|2025-07-16T11:33:32.132000Z|79c53f52a5", "title": "How to create an Admin Account on our Oasis?", "section": "issues", "question": "👍 and how do we find the user id of the account we want to make a admin user?", "proposed_answer": "You could use the API: https://nomad-lab.eu/prod/v1/staging/api/v1/extensions/docs#operations-tag-users", "source_url": null, "timestamp": "2025-07-16T11:33:32.132000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 3, "a_start": 4, "a_end": 5}
{"id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q31-a32-33", "thread_id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab", "title": "Create multiple entry from parser or new entry in normalizer", "section": "issues", "question": "Maybe let's start from the beginning. Do you need to have ELN functionality in any of the entries after parsing?", "proposed_answer": "If you don't need any ELN functionality you should be able to use a parser and `child_archives`. However, I have never done this myself so I would then refer you to someone from Area C/D. Perhaps could explain what's going wrong there.", "source_url": null, "timestamp": "2025-06-14T07:31:20.113000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 31, "a_start": 32, "a_end": 33}
{"id": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007|q22-a23-24", "thread_id": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007", "title": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12", "section": "issues", "question": "0) At the moment NOMAD Oasis version 1.3.16 is the most stable recent one, nor?", "proposed_answer": "1) How to I download/get/install the most recent NOMAD Oasis image version (==1.3.16) without any modification? What exact commands I have to run besides `docker compose pull` and `docker compose up -d`? At the moment it only downloads all images and nomad-lab with v1.3.15.", "source_url": null, "timestamp": "2025-06-06T14:58:44.249000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 22, "a_start": 23, "a_end": 24}
{"id": "Jupyter launch, unexpected error (504)|2025-04-15T11:49:41.936000Z|8709bf1672|q12-a15-16", "thread_id": "Jupyter launch, unexpected error (504)|2025-04-15T11:49:41.936000Z|8709bf1672", "title": "Jupyter launch, unexpected error (504)", "section": "issues", "question": "Do you mean this https://dash.plotly.com/dash-in-jupyter?", "proposed_answer": "and I mean this sideways question I'll try managing myself, was just curious if you tried it already", "source_url": null, "timestamp": "2025-04-15T11:49:41.936000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 12, "a_start": 15, "a_end": 16}
{"id": "Limiting the Oasis workers to save memory|2025-04-09T08:09:43.694000Z|70654d8f14|q15-a16-17", "thread_id": "Limiting the Oasis workers to save memory|2025-04-09T08:09:43.694000Z|70654d8f14", "title": "Limiting the Oasis workers to save memory", "section": "issues", "question": "I am in a similar situation that I want to limit the number of workers for our oasis, as the machine has very many cores. Where and how can I configure this?", "proposed_answer": "We set the limit on number of tasks https://github.com/FAIRmat-NFDI/nomad-distro-template/pull/85", "source_url": null, "timestamp": "2025-04-09T08:09:43.694000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 15, "a_start": 16, "a_end": 17}
{"id": "Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94|q95-a98-99", "thread_id": "Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94", "title": "Adding h5view of data file to entry overview", "section": "issues", "question": "is this the updated def?", "proposed_answer": "if you want to plot only a dataset use the signal field", "source_url": null, "timestamp": "2025-04-03T09:05:50.630000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 95, "a_start": 98, "a_end": 99}
{"id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q40-a41-42", "thread_id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4", "title": "distro-dev not running on latest develop", "section": "issues", "question": "Hrmm, I still get the error. Do I need to delete venv/cache and run setup again?", "proposed_answer": "yep, can you try deleting the `uv.lock` file too", "source_url": null, "timestamp": "2025-04-01T12:49:10.385000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 40, "a_start": 41, "a_end": 42}
{"id": "Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5|q19-a26-27", "thread_id": "Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5", "title": "Protected Oasis - Restrict GUI/API Access only for Login-User", "section": "issues", "question": "Is not the option oasis:allowed_users: doing this?", "proposed_answer": "Problem statement/use case: But also within the \"University network\" the \"public part\" of the oasis - the gui/search interface (e.g. https://nomad-lab.eu/prod/v1/oasis/gui/search/eln ) and also the public API can be accessed by all people who know the IP address of the dedicated oasis (and maybe they are not part of any associated working groups). As there is a very nice convenient feature for \"Uploads in Step3\" with the checkbox: \"Edit visibility and access; Enabling this will allow all users, including guests without an account, to view the upload even before it is published.\" to share unpublished quite recent research data between the working groups for review not bothering you to select a special group or person. Also \"enabling visibility of unpublished data\" allows for data changes e.g. some data have been updated in contrast to \"published data in the oasis can not be altered (step 5)\". So, I am searching for a workflow to share all (\"not yet publishable\") data to everybody, who has access to the oasis, but not exposing recent research data to the world/university.", "source_url": null, "timestamp": "2025-02-28T08:48:27.056000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 19, "a_start": 26, "a_end": 27}
{"id": "Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41|q2-a3-8", "thread_id": "Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41", "title": "Updating distro-dev after pydantic update", "section": "issues", "question": "Can you try `uv python install 3.12.8`?", "proposed_answer": "Or `3.12.9` I get: ``` Searching for Python versions matching: Python 3.12.8 error: No download found for request: cpython-3.12.8-linux-x86_64-gnu", "source_url": null, "timestamp": "2025-02-10T08:34:08.970000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 2, "a_start": 3, "a_end": 8}
{"id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q39-a40-41", "thread_id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c", "title": "Unexpected dependency version on NOMAD develop", "section": "issues", "question": "E.g., I don't think nomad-lab relies on MDanalysis anymore, but plugins like simulation workflow schema, which is in the central deployments, do depend on this. Do we still need the dependency in requirements-plugins.txt, or it's enough that the plugin depends on it within it's toml?", "proposed_answer": "2. `FAIRmat-NFDI/nomad-distro-dev` is only used to configure your local development environments, so it has no effect on any deployments.", "source_url": null, "timestamp": "2025-01-24T11:39:53.165000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 39, "a_start": 40, "a_end": 41}
{"id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q46-a47-48", "thread_id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c", "title": "Unexpected dependency version on NOMAD develop", "section": "issues", "question": "It looks like these were updated a week ago. But the atomistic parsers has several versions above this, e.g., v1.0.3 from 3 weeks ago. Why is this not updating in the requirements and how can I fix this?", "proposed_answer": "The toml gives a valid set of distributions, the `requirements.txt` is generated using the `scripts/generate_python_dependencies.sh` script. But if you want a specific version of nomad-parser-plugins-atomistic in the requirements file, you can bump up the version in the toml file and generate the requirements txt file.", "source_url": null, "timestamp": "2025-01-24T11:39:53.165000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 46, "a_start": 47, "a_end": 48}
{"id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q48-a49-50", "thread_id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c", "title": "Unexpected dependency version on NOMAD develop", "section": "issues", "question": "I don't want a specific version though, I just always want the latest release version. I thought this would already happen with `\"nomad-parser-plugins-atomistic>=1.0\"` in the toml. The question is why is this stuck at the 1.0.2 release?", "proposed_answer": "In the `scripts/generate_python_dependencies.sh` it will only upgrade nomad-lab and nomad-lab-gui, no plugins are upgraded automatically so they will always keep their locked version. The intention here was that no other dependency gets automatically updated and individual plugin authors can upgrade their plugins as they see fit and we have a more controlled roll out. Otherwise, if random plugins get updated automatically during the nightly run, the pipeline might start failing due to changes in the plugin.", "source_url": null, "timestamp": "2025-01-24T11:39:53.165000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 48, "a_start": 49, "a_end": 50}
{"id": "New plugin does not show up in a nomad-distro-dev|2025-01-21T16:08:15.456000Z|9f9ff6cfc7|q4-a5-6", "thread_id": "New plugin does not show up in a nomad-distro-dev|2025-01-21T16:08:15.456000Z|9f9ff6cfc7", "title": "New plugin does not show up in a nomad-distro-dev", "section": "issues", "question": "- the parser is triggered when you drag and drop your csv file in the upload, is something happening?", "proposed_answer": "- did you create your classes inheriting from \"EntryData\" like in the [examples](https://github.com/FAIRmat-NFDI/AreaA-Examples/blob/7dd9ab0b3c257fa8f734327f3cf3be334007968e/workshop_01-25/schema/plotting.py#L58) ?", "source_url": null, "timestamp": "2025-01-21T16:08:15.456000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 4, "a_start": 5, "a_end": 6}
{"id": "DEV Deployment stalls on upload|2025-01-21T10:41:48.261000Z|07f5df7e4e|q15-a18-19", "thread_id": "DEV Deployment stalls on upload|2025-01-21T10:41:48.261000Z|07f5df7e4e", "title": "DEV Deployment stalls on upload", "section": "issues", "question": "Are these all uploads that are left in a limbo state because of failed processing? They are not published or anything?", "proposed_answer": "It could be that there is some sort of memory bug or something that is subtle, but in my experience the hanging is somewhat rare and random, so I wouldn't really want to spend time digging into this. Perhaps we can start keeping a more detailed log of these issues to try to find any correlations.", "source_url": null, "timestamp": "2025-01-21T10:41:48.261000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 15, "a_start": 18, "a_end": 19}
{"id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q43-a44-45", "thread_id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8", "title": "The new oasis-template doesn't works", "section": "issues", "question": "Can you share the upload?", "proposed_answer": "I am able to create a Sintering upload", "source_url": null, "timestamp": "2024-10-04T13:34:29.120000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 43, "a_start": 44, "a_end": 45}
{"id": "More a question than an issue: data curation.|2024-09-18T08:20:59.171000Z|31ca795375|q9-a10-11", "thread_id": "More a question than an issue: data curation.|2024-09-18T08:20:59.171000Z|31ca795375", "title": "More a question than an issue: data curation.", "section": "issues", "question": "> * Imagine you have a setup that directly outputs data to your NOMAD OASIS, can you then modify, delete, annotate the data sets, add some field to it etc.?", "proposed_answer": "Being admin of your oasis, you can essentially re-run/re-process entries at will (https://nomad-lab.eu/prod/v1/staging/docs/howto/oasis/admin.html). You could have a script running at a different moment in time to run your entries to modify, delete, annotate fields... This can be done creating and maintaining a parser plugin.", "source_url": null, "timestamp": "2024-09-18T08:20:59.171000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 9, "a_start": 10, "a_end": 11}
{"id": "Help with parse_section()|2024-06-17T08:27:08.519000Z|2a61c0228e|q18-a26-27", "thread_id": "Help with parse_section()|2024-06-17T08:27:08.519000Z|2a61c0228e", "title": "Help with parse_section()", "section": "issues", "question": "Can anyone give me some direction as to what might be going wrong here?", "proposed_answer": "You can also use `section_def` without the need of adding `m_def`, something like:", "source_url": null, "timestamp": "2024-06-17T08:27:08.519000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 18, "a_start": 26, "a_end": 27}
{"id": "Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed|q53-a54-55", "thread_id": "Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed", "title": "Mkdocs Error when updating Nomad", "section": "issues", "question": "Mh, looks exactly like mine. Does `python -c \"from mkdocs.__main__ import cli\"` work for you? This is what your original error message is about, line 5 of the shell command. Is your `which python` also pointing to `/home/<USER>/software/nomad/.pyenv/bin/python3.9`. This is very weird. But I guess you can like with using `python -m mkdocs` as an alternative?", "proposed_answer": "No, `python -c \"from mkdocs.__main__ import cli\"` produces the same error. `import mkdocs` works, so I could try the second options.", "source_url": null, "timestamp": "2024-05-07T09:51:39.304000Z", "method": "discord_thread_heuristic", "score": 0.82, "q_line": 53, "a_start": 54, "a_end": 55}
{"id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1|q9-a35-36", "thread_id": "Reproducibility indicators bug from afm nexus file|2025-08-11T09:40:29.449000Z|631491f6f1", "title": "Reproducibility indicators bug from afm nexus file", "section": "issues", "question": "could you make the file available somewhere to reproduce? Is this on a local oasis, or a central installation?", "proposed_answer": "The problem is related to the linking. Essentially, in `/NXafm/ENTRY/reproducibility_indicators`, we only define `cantilever_oscillator`, but none of its sub-items. NOMAD will then only try to parse the `cantilever_oscillator` group from the link, but not anything that is inside that Hdf5 group. This is the general case for links, if you want to have all sub-elements also show up at the link destination, you need to explicitly define them in application definition as well.", "source_url": null, "timestamp": "2025-08-11T09:40:29.449000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 9, "a_start": 35, "a_end": 36}
{"id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q88-a113-114", "thread_id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93", "title": "FHIaims parsing issues", "section": "issues", "question": "can you confirm the version you have in your repo?", "proposed_answer": "So for example `nomad-lab[parsing, infrastructure]==1.3.16` works, but `nomad-lab[parsing, infrastructure] @ git+https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR.git does not even though it is the same version (will crash on start due to missing /opt/venv/lib/python3.12/site-packages/nomad/app/static/gui). So I assume I'm still missing some extra steps...", "source_url": null, "timestamp": "2025-07-24T12:22:12.739000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 88, "a_start": 113, "a_end": 114}
{"id": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271|q32-a66-67", "thread_id": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271", "title": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage", "section": "issues", "question": "Do you think this could work for you?", "proposed_answer": "Note that the fixes are enabled only in our `develop` deployment, which contains nightly updates for our development purposes. So if you want to reprocess something yourself, you need to go to: http://nomad-lab.eu/prod/v1/develop/gui", "source_url": null, "timestamp": "2025-05-16T07:42:17.401000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 32, "a_start": 66, "a_end": 67}
{"id": "PlotSection for Static Image|2025-04-17T04:24:57.503000Z|1ce8a0f30c|q0-a6-7", "thread_id": "PlotSection for Static Image|2025-04-17T04:24:57.503000Z|1ce8a0f30c", "title": "PlotSection for Static Image", "section": "issues", "question": "I asked myself if there's any built-in functionality to plot/show an image such as `.tif` as these are the results of [SEM measurements](https://en.wikipedia.org/wiki/Scanning_electron_microscope)?", "proposed_answer": "``` python # see https://plotly.com/python/images/#zoom-on-static-images fig = go.Figure() scale_factor = 1.0 fig.add_trace( go.Scatter( x=[0, img_width * scale_factor], y=[0, img_height * scale_factor], mode=\"markers\", marker_opacity=0 ) ) # Configure axes fig.update_xaxes( visible=False, range=[0, img_width * scale_factor] ) fig.update_yaxes( visible=False, range=[0, img_height * scale_factor], # the scaleanchor attribute ensures that the aspect ratio stays constant scaleanchor=\"x\" ) # Add image fig.add_layout_image( dict( x=0, sizex=img_width * scale_factor, y=img_height * scale_factor, sizey=img_height * scale_factor, xref=\"x\", yref=\"y\", opacity=1.0, layer=\"below\", sizing=\"stretch\", source=uri) ) ```", "source_url": null, "timestamp": "2025-04-17T04:24:57.503000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 0, "a_start": 6, "a_end": 7}
{"id": "Server in Jupyter lab in North tool is not starting up|2025-01-07T10:00:09.162000Z|a94c31d7e2|q2-a16-17", "thread_id": "Server in Jupyter lab in North tool is not starting up|2025-01-07T10:00:09.162000Z|a94c31d7e2", "title": "Server in Jupyter lab in North tool is not starting up", "section": "issues", "question": "Can you try the develop instance to see if that works?", "proposed_answer": "I think with `develop` they mean this: https://nomad-lab.eu/prod/v1/develop/gui/ which works for me.", "source_url": null, "timestamp": "2025-01-07T10:00:09.162000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 2, "a_start": 16, "a_end": 17}
{"id": "Uploading non compressed files with python|2024-12-02T08:36:17.305000Z|e305a682fb|q3-a34-35", "thread_id": "Uploading non compressed files with python|2024-12-02T08:36:17.305000Z|e305a682fb", "title": "Uploading non compressed files with python", "section": "issues", "question": "Which doesn't appear if I compress my nexus file into a zip file for example. However, from what I checked, the requests module seems to be able to take all kind of file (or maybe by AI helper lied, which happens). Do you know if there's an option I should modify in the call to requests?", "proposed_answer": "Oki, cool. so I updated the `update_to_NOMAD` function as follow. Should I/can I update the NOMAD documentation accordingly? I think others may bump into the same problem.", "source_url": null, "timestamp": "2024-12-02T08:36:17.305000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 3, "a_start": 34, "a_end": 35}
{"id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q38-a88-89", "thread_id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d", "title": "Memory error when I build my container", "section": "issues", "question": "When you add a plugin do you just add it to project.optional-dependencies or you do something else ?", "proposed_answer": "it's the `Template Repository Initialisation` step mentioned here: https://github.com/PauloGitHB/nomad-oasis-cemes?tab=readme-ov-file#nomad-oasis-distribution-template", "source_url": null, "timestamp": "2024-11-15T09:44:36.042000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 38, "a_start": 88, "a_end": 89}
{"id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q77-a88-89", "thread_id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d", "title": "Memory error when I build my container", "section": "issues", "question": "can you share your `docker-compose.yml` file?", "proposed_answer": "it's the `Template Repository Initialisation` step mentioned here: https://github.com/PauloGitHB/nomad-oasis-cemes?tab=readme-ov-file#nomad-oasis-distribution-template", "source_url": null, "timestamp": "2024-11-15T09:44:36.042000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 77, "a_start": 88, "a_end": 89}
{"id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q79-a85-86", "thread_id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8", "title": "The new oasis-template doesn't works", "section": "issues", "question": "Are there any logs for `docker logs nomad_oasis_app`?", "proposed_answer": "some people needed to chaneg the IP in this file `/etc/docker/daemon.json `", "source_url": null, "timestamp": "2024-10-04T13:34:29.120000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 79, "a_start": 85, "a_end": 86}
{"id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q31-a76-77", "thread_id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459", "title": "Adapting existing Oasis to new plugin mechanism", "section": "issues", "question": "How do you mean they are not compatible? You mean regarding the nomad-lab dependency?", "proposed_answer": "I now commented on <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1610> and <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1955>. The relevant people here, should be part of the respective issues. My idea would be to work on the \"button\" and on the action you do (e.g. creating entries, updating entries) together and to finally document such a use-base with a how-to in the documentation.", "source_url": null, "timestamp": "2024-10-01T12:39:26.289000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 31, "a_start": 76, "a_end": 77}
{"id": "docker-compose up error with nomad-lab 3.11 setup|2024-08-13T12:18:42.333000Z|98252609bf|q12-a30-31", "thread_id": "docker-compose up error with nomad-lab 3.11 setup|2024-08-13T12:18:42.333000Z|98252609bf", "title": "docker-compose up error with nomad-lab 3.11 setup", "section": "issues", "question": "what do you mean the \"full logs\"?", "proposed_answer": "Maybe here: https://nomad-lab.eu/prod/v1/develop/docs/howto/develop/setup.html#install-docker", "source_url": null, "timestamp": "2024-08-13T12:18:42.333000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 12, "a_start": 30, "a_end": 31}
{"id": "2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d|q63-a106-107", "thread_id": "2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d", "title": "2D array of strings", "section": "issues", "question": "What about `np.str_`?", "proposed_answer": "Well, based on the Traceback, it seems that using the `nomad parse` command reach out at some point the `/home/<USER>/Desktop/.venvdev/lib/python3.9/site-packages/nomad/cli/parse.py` file which seems to contain the code that is running for the the `parse` argument and in there, the call to `EntryMetadata` seems to be made through this line `entry_archive.metadata.apply_archive_metadata(entry_archive)` (line 88)", "source_url": null, "timestamp": "2024-06-11T08:27:23.680000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 63, "a_start": 106, "a_end": 107}
{"id": "2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d|q87-a106-107", "thread_id": "2D array of strings|2024-06-11T08:27:23.680000Z|5aa374769d", "title": "2D array of strings", "section": "issues", "question": "What I am guessing is that you are populating `EntryMetadata`, i.e., the `EntryArchive.metadata` section. In my case, I am populating `EntryData`/`EntryArchive.data`. Is that true?", "proposed_answer": "Well, based on the Traceback, it seems that using the `nomad parse` command reach out at some point the `/home/<USER>/Desktop/.venvdev/lib/python3.9/site-packages/nomad/cli/parse.py` file which seems to contain the code that is running for the the `parse` argument and in there, the call to `EntryMetadata` seems to be made through this line `entry_archive.metadata.apply_archive_metadata(entry_archive)` (line 88)", "source_url": null, "timestamp": "2024-06-11T08:27:23.680000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 87, "a_start": 106, "a_end": 107}
{"id": "Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed|q6-a37-38", "thread_id": "Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed", "title": "Mkdocs Error when updating Nomad", "section": "issues", "question": "Is mkdocs installed in your python environment?", "proposed_answer": "You original error messages sounds a bit like the mkdocs package is missing completely. I guess you cannot run `mkdocs` at all. Is `mkdocs --help` already failing? What happens if you try to run the mkdocs package instead of the shell command: `python -m mkdocs build`.", "source_url": null, "timestamp": "2024-05-07T09:51:39.304000Z", "method": "discord_thread_heuristic", "score": 0.81, "q_line": 6, "a_start": 37, "a_end": 38}
{"id": "Migrating to new plugin mechanism - Problem with Parser|2025-06-23T12:49:00.346000Z|990628677f|q2-a4-5", "thread_id": "Migrating to new plugin mechanism - Problem with Parser|2025-06-23T12:49:00.346000Z|990628677f", "title": "Migrating to new plugin mechanism - Problem with Parser", "section": "issues", "question": "I guess the reason is that I changed the name of my parser in the new plugin (to: 'nomad_perolab_umr.parsers:cicci_txt_parser_entry_point') . For schemas there is the chance to give alias names. Is there something similar for parsers?", "proposed_answer": "Note sure if this works for parser packages too? Could you give it a try and report back here? https://nomad-lab.eu/prod/v1/staging/docs/howto/plugins/schema_packages.html#schema-package-aliases", "source_url": null, "timestamp": "2025-06-23T12:49:00.346000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 2, "a_start": 4, "a_end": 5}
{"id": "Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e|q2-a4-5", "thread_id": "Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e", "title": "Where to find available base sections for yaml", "section": "issues", "question": "Or, is there a basic section with DFT simulation metainfo fields one could inherit from?", "proposed_answer": "But it can be a bit difficult to find what you are looking for. If you are looking specifically for sections inheriting from the `BaseSection` parent class you can look at all the inheriting sections of that one: https://nomad-lab.eu/prod/v1/gui/analyze/metainfo/nomad/section_definitions", "source_url": null, "timestamp": "2025-06-02T09:45:34.629000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 2, "a_start": 4, "a_end": 5}
{"id": "ArchiveQuery error|2025-05-05T13:43:22.936000Z|5e3ea3852f|q13-a14-15", "thread_id": "ArchiveQuery error|2025-05-05T13:43:22.936000Z|5e3ea3852f", "title": "ArchiveQuery error", "section": "issues", "question": "Can you try switching to nomad v1.3.15 locally?", "proposed_answer": "Sure but it wasn't working on 1.3.14 either", "source_url": null, "timestamp": "2025-05-05T13:43:22.936000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 13, "a_start": 14, "a_end": 15}
{"id": "Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c|q38-a39-40", "thread_id": "Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c", "title": "Failing test in plugin", "section": "issues", "question": "Can you test if removing the scaling factor in your case fixes the issue?", "proposed_answer": "the great shout! I simply translated the units from the magres specification document, as they are found in the magres file which we parse. I did make sure that the units I defined were found in the pint repo definitions, but it makes sense that the scaling factor is unexpected. When I declare the unit as m^3/mol, it works fine.", "source_url": null, "timestamp": "2025-04-14T08:48:46.049000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 38, "a_start": 39, "a_end": 40}
{"id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q38-a39-40", "thread_id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4", "title": "distro-dev not running on latest develop", "section": "issues", "question": "What version should I go for?", "proposed_answer": "`>=0.3.2`", "source_url": null, "timestamp": "2025-04-01T12:49:10.385000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 38, "a_start": 39, "a_end": 40}
{"id": "Apps dissapear in NOMAD Oasis 1.3.15|2025-03-25T09:49:02.502000Z|408040b015|q5-a6-7", "thread_id": "Apps dissapear in NOMAD Oasis 1.3.15|2025-03-25T09:49:02.502000Z|408040b015", "title": "Apps dissapear in NOMAD Oasis 1.3.15", "section": "issues", "question": "Can you DM me the `nomad.yaml` with the secrets removed?", "proposed_answer": "yes this is correct and this is a valuable fix in 1.3.15, you need to explicitly enable APPs, otherwise all available apps are shown, which was a mess 😄", "source_url": null, "timestamp": "2025-03-25T09:49:02.502000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 5, "a_start": 6, "a_end": 7}
{"id": "Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c|q42-a51-52", "thread_id": "Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c", "title": "Mongo DB collection warning is weird", "section": "issues", "question": "What does `docker ps` say on your system?", "proposed_answer": "So you should be able to use the snippet without the `docker exec`.", "source_url": null, "timestamp": "2025-03-07T10:17:12.565000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 42, "a_start": 51, "a_end": 52}
{"id": "Overwriting subsections with quantities not possible?|2024-12-02T15:18:28.067000Z|5ce0be9857|q0-a4-5", "thread_id": "Overwriting subsections with quantities not possible?|2024-12-02T15:18:28.067000Z|5ce0be9857", "title": "Overwriting subsections with quantities not possible?", "section": "issues", "question": "Is it intended that MetaInfo does not allow to overwrite a SubSection with a Quantity?", "proposed_answer": "`nomad.metainfo.metainfo.MetainfoError: Cannot inherit from different property types.`", "source_url": null, "timestamp": "2024-12-02T15:18:28.067000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 0, "a_start": 4, "a_end": 5}
{"id": "Pint and pydantic update|2024-11-18T12:03:55.550000Z|9743355b6f|q2-a4-5", "thread_id": "Pint and pydantic update|2024-11-18T12:03:55.550000Z|9743355b6f", "title": "Pint and pydantic update", "section": "issues", "question": "⁠ Are there any plans if/when these changes will come?", "proposed_answer": "The main problem is that both updates require quite a bit of work, as in the case of Pydantic, we need to update our code to use the new interface, and in the case of Pint, we would need to change several of our parsers due to this issue: https://github.com/hgrecco/pint/issues/1809", "source_url": null, "timestamp": "2024-11-18T12:03:55.550000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 2, "a_start": 4, "a_end": 5}
{"id": "Clarify Display Docs|2024-11-01T14:15:53.587000Z|3650b87949|q19-a23-24", "thread_id": "Clarify Display Docs|2024-11-01T14:15:53.587000Z|3650b87949", "title": "Clarify Display Docs", "section": "issues", "question": "What if pass it as a dict?", "proposed_answer": "remove the models SectionDisplayAnnotation and Filter and use dict", "source_url": null, "timestamp": "2024-11-01T14:15:53.587000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 19, "a_start": 23, "a_end": 24}
{"id": "NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520|q17-a18-19", "thread_id": "NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520", "title": "NOMAD Oasis Requests Takes a lot of Time from Institute Intranet", "section": "issues", "question": "Do you think this is caused by the lack of SSL certification?", "proposed_answer": "PS. also it is interesting that no matter what I do(clear cache, hard reset etc.), a single request is always loaded from memory cache (data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAQAAAAfQ//73v/+BiOh/AAA=) I prevented it by blocking.", "source_url": null, "timestamp": "2024-10-29T16:31:40.296000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 17, "a_start": 18, "a_end": 19}
{"id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q49-a53-54", "thread_id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8", "title": "The new oasis-template doesn't works", "section": "issues", "question": "could you try cloning the repo and see if you can reproduce this?", "proposed_answer": "try stuff like changing the browser you use, deleting the cache, updating the version of your browser if available", "source_url": null, "timestamp": "2024-10-04T13:34:29.120000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 49, "a_start": 53, "a_end": 54}
{"id": "inconsistencies between develop deployment and local development|2024-09-17T10:20:47.494000Z|5c8eda6b6f|q7-a9-10", "thread_id": "inconsistencies between develop deployment and local development|2024-09-17T10:20:47.494000Z|5c8eda6b6f", "title": "inconsistencies between develop deployment and local development", "section": "issues", "question": "I did make an update to the atomisticparser plugin version yesterday which could cause this, but again I am expecting this to have been updated already. Is there a way to check the plugin version on the deployment?", "proposed_answer": "I've created a PR to update the atomistic parser https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-distro/-/merge_requests/35", "source_url": null, "timestamp": "2024-09-17T10:20:47.494000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 7, "a_start": 9, "a_end": 10}
{"id": "Plugin Loaded twice|2024-09-04T10:23:06.791000Z|351b42840e|q11-a15-16", "thread_id": "Plugin Loaded twice|2024-09-04T10:23:06.791000Z|351b42840e", "title": "Plugin Loaded twice", "section": "issues", "question": "Could the three of you , , coordinate to test this? Now that I'm removing the duplication of the metainfo, I'm no longer sure if entries that use the old alias will be displayed properly. Maybe you could test this branch with entries that use both old and new m_defs?", "proposed_answer": "I guess this is `workflow` vs `workflow2`", "source_url": null, "timestamp": "2024-09-04T10:23:06.791000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 11, "a_start": 15, "a_end": 16}
{"id": "\"This entry does not exist.\" upon publishing|2024-08-19T11:24:33.397000Z|c54f76332e|q7-a9-10", "thread_id": "\"This entry does not exist.\" upon publishing|2024-08-19T11:24:33.397000Z|c54f76332e", "title": "\"This entry does not exist.\" upon publishing", "section": "issues", "question": "This particular upload should now work, could you confirm that everything is as it should?", "proposed_answer": "update. Unfortunately I'm still getting the same problem. I've just uploaded and published this: https://nomad-lab.eu/prod/v1/test/gui/user/uploads/upload/id/n5E4GskfTyadQqeufkpaYQ", "source_url": null, "timestamp": "2024-08-19T11:24:33.397000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 7, "a_start": 9, "a_end": 10}
{"id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q31-a33-34", "thread_id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57", "title": "HDF5 in NOMAD", "section": "issues", "question": "But the Quantity obj does store the path though? Cuz it uses that to read, right?", "proposed_answer": "there is also a basesection `HDF5Normalizer` that you can inherit/use in your custom schema. You can deop your h5 file, define path to the sections of your hdf5 file with proper quantity annotations.", "source_url": null, "timestamp": "2024-05-27T13:17:35.844000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 31, "a_start": 33, "a_end": 34}
{"id": "Website is broken|2024-04-30T09:17:15.715000Z|df2b4fdef6|q4-a5-6", "thread_id": "Website is broken|2024-04-30T09:17:15.715000Z|df2b4fdef6", "title": "Website is broken", "section": "issues", "question": "Can someone else confirm that it's a Firefox issue?", "proposed_answer": "Yeah, same issue for me here here with Firefox 125.0.1", "source_url": null, "timestamp": "2024-04-30T09:17:15.715000Z", "method": "discord_thread_heuristic", "score": 0.8, "q_line": 4, "a_start": 5, "a_end": 6}
{"id": "Exact str match of schema quantity via API|2025-07-01T06:54:57.792000Z|54f8e787d8|q4-a5-11", "thread_id": "Exact str match of schema quantity via API|2025-07-01T06:54:57.792000Z|54f8e787d8", "title": "Exact str match of schema quantity via API", "section": "issues", "question": "Is there any solution for that?", "proposed_answer": "we figured out that the trick is: ```python tank = \"manual tank\" query = { \"owner\": \"visible\", \"query\": { \"entry_type\": \"PVcomB_HFDip\", \"upload_id\": \"3HK8UmRCR3OhVqQySD1kAA\", \"search_quantities\": { \"id\": \"data.process.used_tank#nomad_pvcomb.schema_packages.pvcomb_schema_package.PVcomB_HFDip\", \"str_value:all\":tank.split(\" \") } }} ```", "source_url": null, "timestamp": "2025-07-01T06:54:57.792000Z", "method": "discord_thread_heuristic", "score": 0.79, "q_line": 4, "a_start": 5, "a_end": 11}
{"id": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007|q24-a31-32", "thread_id": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007", "title": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12", "section": "issues", "question": "2) You are certain not to change uv.lock for the docker-build to change also the nomad-version to 1.3.16 in [lines 3550](https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/uv.lock#L3550)?", "proposed_answer": "🤦‍♂️ Now, I understand that you are referring to 🙃 I changed the `pyproject.toml`, push it to github, and the workflow on github created a docker-image for `app` and `worker` and also for `north with jupyter`, but the `docker-compose.yaml` was not updated in the process, see [here](https://github.com/Bondoki/NOMAD_Oasis_template). I fixed it manually and added a keycloak-section with adjusted `nginx_base.conf`. Now it seems to run.. and I can test the plug-in 🙃 Thanks for your patience 🙂", "source_url": null, "timestamp": "2025-06-06T14:58:44.249000Z", "method": "discord_thread_heuristic", "score": 0.79, "q_line": 24, "a_start": 31, "a_end": 32}
{"id": "Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94|q37-a38-40", "thread_id": "Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94", "title": "Adding h5view of data file to entry overview", "section": "issues", "question": "Is there something that I am doing wrong? If I can display the HDF5 file in DATA page is can't it just be replicated in the OVERVIEW page?", "proposed_answer": "only a quantity that will contain the reference to your data and in the root section def add the annotation,```class MySection(EntryData): m_def = Section(a_h5web=H5WebAnnotation(axes='time', signal='value')) value = Quantity( type=HDF5Dataset, unit='dimensionless', shape=[], a_h5web=H5WebAnnotation( long_name='power', errors='value_e' ), ) archive.data = MySection(value='{filename}#{path_to_data}') ```", "source_url": null, "timestamp": "2025-04-03T09:05:50.630000Z", "method": "discord_thread_heuristic", "score": 0.79, "q_line": 37, "a_start": 38, "a_end": 40}
{"id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q5-a6-10", "thread_id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4", "title": "distro-dev not running on latest develop", "section": "issues", "question": "How do I clear the uv cache?", "proposed_answer": "``` uv cache prune # removes unused deps uv cache clean # removes the entire cache ```", "source_url": null, "timestamp": "2025-04-01T12:49:10.385000Z", "method": "discord_thread_heuristic", "score": 0.79, "q_line": 5, "a_start": 6, "a_end": 10}
{"id": "Adding users to an Oasis|2025-03-25T14:05:19.493000Z|3665580603|q6-a7-8", "thread_id": "Adding users to an Oasis|2025-03-25T14:05:19.493000Z|3665580603", "title": "Adding users to an Oasis", "section": "issues", "question": "What should one do next? I have added those lines and then launched my Oasis from command line, and then asked a colleague to put in her browser the address which appears in my Oasis.... and her browser yields an error, apparently related to firewall settings or something of that kind. So, which is the correct procedure to allow other users to access \"our\" Oasis? Thanks a lot. Best. Elena", "proposed_answer": "I think without this `allowed_users` every registed nomad account can access an oasis, if the required ports are accessible. The `allowed_users` list allows you to restrict this. See also the discussion here: https://discord.com/channels//", "source_url": null, "timestamp": "2025-03-25T14:05:19.493000Z", "method": "discord_thread_heuristic", "score": 0.79, "q_line": 6, "a_start": 7, "a_end": 8}
{"id": "When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702|q15-a16-19", "thread_id": "When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702", "title": "When registering a new account, the confirmation email does not arrive", "section": "issues", "question": "do you have the correct keycloak realm in your nomad.yaml in the root folder of your nomad-dev-distro?", "proposed_answer": "``` keycloak: realm_name: fairdi_nomad_test ```", "source_url": null, "timestamp": "2025-01-16T10:34:58.468000Z", "method": "discord_thread_heuristic", "score": 0.79, "q_line": 15, "a_start": 16, "a_end": 19}
{"id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q5-a6-7", "thread_id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9", "title": "Uploads are getting directly stuck in nomad-distro-dev", "section": "issues", "question": "which sounds okay. Where do you reckon I can find more information where the error occurs. Thanks!", "proposed_answer": "in your nomad.yaml you can try adjusting the console_log_level https://nomad-lab.eu/prod/v1/docs/reference/config.html#services_1", "source_url": null, "timestamp": "2025-01-09T14:23:39.252000Z", "method": "discord_thread_heuristic", "score": 0.79, "q_line": 5, "a_start": 6, "a_end": 7}
{"id": "Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99|q6-a9-11", "thread_id": "Could not find a version that satisfies the requirement networkx==3.3|2024-08-19T11:26:40.185000Z|66deb72d99", "title": "Could not find a version that satisfies the requirement networkx==3.3", "section": "issues", "question": "Can you share the full error message?", "proposed_answer": "ahhh I see I'm on python 3.9 still I didn't try with newer but Amir did", "source_url": null, "timestamp": "2024-08-19T11:26:40.185000Z", "method": "discord_thread_heuristic", "score": 0.79, "q_line": 6, "a_start": 9, "a_end": 11}
{"id": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c|q35-a42-43", "thread_id": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c", "title": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis", "section": "issues", "question": "The original `1000:991` is using group 991 which on most distros should be the docker group. You can check what your docker group id is and maybe change the value. But was it a new install? Did is work before? Any changes made to the config? Or did it just suddenly break?", "proposed_answer": "Wrong command. It has to be `upgrade-db` <https://jupyterhub.readthedocs.io/en/stable/howto/upgrading.html#upgrade-jupyterhub-database>. Please make a quick MR for these mistakes. You can set it to automerge.", "source_url": null, "timestamp": "2024-03-14T16:47:18.591000Z", "method": "discord_thread_heuristic", "score": 0.79, "q_line": 35, "a_start": 42, "a_end": 43}
{"id": "NotImplementedError for context.raw_path_exists method|2024-03-05T08:41:33.756000Z|b6ee07562e|q1-a4-6", "thread_id": "NotImplementedError for context.raw_path_exists method|2024-03-05T08:41:33.756000Z|b6ee07562e", "title": "NotImplementedError for context.raw_path_exists method", "section": "issues", "question": "Because of this difference, the pytests fail during the plugin testing, when using `archive.m_context.raw_path_exists(file_path)`. Can we harmonize these two methods for the classes `ServerContext` and `Context`?", "proposed_answer": "https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/commit/2e8d24cd139cef1a9b9b7244c25c00b4333f28f1 I guess we only implemented the Context interface based on what was necessary. The ServerContext is the more complete one as this is used all the time in the app. The ClientContext is only used by, well, clients. E.g. when you do the \"nomad parse\" command, or the ArchiveQuery instantiates metainfo object froms the retrieved data. For the first case \"nomad parse\" you commit seems ok. For the second one, it does not hurt, but it also does not help, as the raw data is most likely not locally available. I guess it complements the existing `raw..` functions. Feel free to create an MR and merge this.", "source_url": null, "timestamp": "2024-03-05T08:41:33.756000Z", "method": "discord_thread_heuristic", "score": 0.79, "q_line": 1, "a_start": 4, "a_end": 6}
{"id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q14-a17-18", "thread_id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab", "title": "Create multiple entry from parser or new entry in normalizer", "section": "issues", "question": "for the solution! It's working now. Is there anything to keep in mind while using `create_archive`? The function is writing to the file systems directly, which I think is discouraged by Nomad. Additionally, does it work for normalizers as well?", "proposed_answer": "You can find the (very) preliminary documentation on the `update_entry` context here: https://github.com/FAIRmat-NFDI/nomad-docs/blob/main/docs/howto/develop/normalizing.md", "source_url": null, "timestamp": "2025-06-14T07:31:20.113000Z", "method": "discord_thread_heuristic", "score": 0.78, "q_line": 14, "a_start": 17, "a_end": 18}
{"id": "Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80|q10-a13-14", "thread_id": "Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80", "title": "Publishing DFT calculations from RSPt", "section": "issues", "question": "> Can you give me a rough idea of the scope and user base of this code?", "proposed_answer": "We will have to ask our partner for whom we are doing the publishing. We are doing it for a common EU project [MaMMoS](https://github.com/mammos-project), for which we wanted to use NOMAD as a platform to openly store and publish our data. Within this project we have all kinds of data, including ab initio, simulations and measurement data. Our partner who is working with this code are from Uppsala university. We can definitely work together on the parser and can organise an initial meeting to discuss that with them.", "source_url": null, "timestamp": "2025-06-03T09:29:11.049000Z", "method": "discord_thread_heuristic", "score": 0.78, "q_line": 10, "a_start": 13, "a_end": 14}
{"id": "Pydantic validation errors in QueryEditQuantity|2025-05-27T13:12:35.697000Z|2f312b39c1|q10-a13-14", "thread_id": "Pydantic validation errors in QueryEditQuantity|2025-05-27T13:12:35.697000Z|2f312b39c1", "title": "Pydantic validation errors in QueryEditQuantity", "section": "issues", "question": "and I were trying to debug. Maybe can help us identify the issue?", "proposed_answer": "I found the issue. In the models for operators, the name of the field is `op` while the actual name of the operator is defined as an alias (for example [here](https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/app/v1/models/models.py?ref_type=heads#L118) ). When `model_dump()` is used, it defaults to using `op` as the key.", "source_url": null, "timestamp": "2025-05-27T13:12:35.697000Z", "method": "discord_thread_heuristic", "score": 0.78, "q_line": 10, "a_start": 13, "a_end": 14}
{"id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q128-a131-132", "thread_id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b", "title": "Unexpected error in the research app", "section": "issues", "question": "- You mentioned that you are here using `nomad-distro-dev`, is this correct?", "proposed_answer": "I am using that same use case (headless server for nomad-distro-dev), and added a note to the readme about the required port forwardings: https://github.com/FAIRmat-NFDI/nomad-distro-dev/blob/21a1566fc1d8ced3757d2d2b60ecea64f24f4b58/README.md?plain=1#L311", "source_url": null, "timestamp": "2025-02-28T07:57:57.339000Z", "method": "discord_thread_heuristic", "score": 0.78, "q_line": 128, "a_start": 131, "a_end": 132}
{"id": "Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41|q18-a19-21", "thread_id": "Updating distro-dev after pydantic update|2025-02-10T08:34:08.970000Z|c613510c41", "title": "Updating distro-dev after pydantic update", "section": "issues", "question": "Just a quick question about the pydantic upgrade in advance: you mentioned that you need to possibly update your plugins (and you list some simulation plugins there). What updates do you expect are necessary, just some dependencies in the toml, or actually something in the code?", "proposed_answer": "It'd be something like this, but I didn't notice any issues in any of the plugins used in our deployments so I don't think there'd be any changes necessary https://github.com/nomad-coe/electronic-parsers/commit/2340fb753390cd15b9e9feaa96d6af452fca7e41 Or do you mean on your end to use the plugins? If it's listed as a `>=some_version`, uv will automatically resolve to a greater version that is compatible. Otherwise it'll show an error message and updating the toml will fix it", "source_url": null, "timestamp": "2025-02-10T08:34:08.970000Z", "method": "discord_thread_heuristic", "score": 0.78, "q_line": 18, "a_start": 19, "a_end": 21}
{"id": "k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525|q8-a11-12", "thread_id": "k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525", "title": "k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing", "section": "issues", "question": "4 - https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/normalizing/method.py?ref_type=heads#L370 k_mesh.dimensionality will be 3 for all of 1x1x1, 1x1x6, and 20x20x1 grids... is this expected and it is the way the metainfo definition is intended?", "proposed_answer": "In the second case we could use the normalized reciprocal cell, get list of symmetry operations and extend the points to all_points, while double checking that the weights/multiplicities match. And only from `all_points` we could try guessing the grid (preferably after also checking the uniform spacings).", "source_url": null, "timestamp": "2024-07-12T10:18:27.642000Z", "method": "discord_thread_heuristic", "score": 0.78, "q_line": 8, "a_start": 11, "a_end": 12}
{"id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q83-a86-87", "thread_id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57", "title": "HDF5 in NOMAD", "section": "issues", "question": "Would sharing a dataset with you help?", "proposed_answer": "The official and staging deployments lag behind the latest fixes but it will be available in the next release. It should be available though on the develop deployment `https://nomad-lab.eu/prod/v1/develop/gui/about/information`, but I have to warn about the features are experimental there and not stable", "source_url": null, "timestamp": "2024-05-27T13:17:35.844000Z", "method": "discord_thread_heuristic", "score": 0.78, "q_line": 83, "a_start": 86, "a_end": 87}
{"id": "nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52|q26-a32-33", "thread_id": "nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52", "title": "nomad-oasis can not stablely be started", "section": "issues", "question": "what should be the nomad-oasis look like if it is successfully deployed? Does it have a web page interface when access it through web browser? can you show me the result if nomad-oasis is successfully deployed?", "proposed_answer": "deployment_url: \"https://localhost/api\"", "source_url": null, "timestamp": "2025-07-17T08:58:19.106000Z", "method": "discord_thread_heuristic", "score": 0.77, "q_line": 26, "a_start": 32, "a_end": 33}
{"id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q25-a26-27", "thread_id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab", "title": "Create multiple entry from parser or new entry in normalizer", "section": "issues", "question": "1. How I need to create reference between the `ChildEntryArchive` and `ParentEntryArchive`?", "proposed_answer": "2. Does `ParentEntryArchive` keep track of the `ChildEntryArchive` list? Or How to I create the reference manually? (back to question 1)", "source_url": null, "timestamp": "2025-06-14T07:31:20.113000Z", "method": "discord_thread_heuristic", "score": 0.77, "q_line": 25, "a_start": 26, "a_end": 27}
{"id": "Recreating deleted upload on OASIS|2025-06-05T13:46:57.198000Z|e887b8bdd4|q2-a3-4", "thread_id": "Recreating deleted upload on OASIS|2025-06-05T13:46:57.198000Z|e887b8bdd4", "title": "Recreating deleted upload on OASIS", "section": "issues", "question": "Hrmm, okay, I guess the main issue is if there are references to this upload and we then create a new one. Is there a way to recreate and upload with a specified ID?", "proposed_answer": "The way to recreate everythign with an existing ID would be to move the correctly named folder into .volumes, re-add the data (uploads + entries) into MongoDB from a backup, and run `nomad admin uploads index <upload-id>` to get ES populated with data about the entries.", "source_url": null, "timestamp": "2025-06-05T13:46:57.198000Z", "method": "discord_thread_heuristic", "score": 0.77, "q_line": 2, "a_start": 3, "a_end": 4}
{"id": "Include PDF in Oasis|2025-06-05T10:04:09.333000Z|d38858399c|q0-a1-2", "thread_id": "Include PDF in Oasis|2025-06-05T10:04:09.333000Z|d38858399c", "title": "Include PDF in Oasis", "section": "issues", "question": "if it is possible to generate pdfs in my parser and then parse them into my oasis so that i am able to access them later on. Is that in any way possible? If so how would my schema need to look like ?", "proposed_answer": "Absolutely. You can attach they python code which generates the PDF to your parser or normalize method and use the `archive.m_context.raw_file()` context to create the file. If you want the PDFs to be searchable you can either add them as a file edit quantity in an ELN or create a simple matching parser which matches them. If you tell me a bit more about your use case I might be able to give you some advice on which way to go.", "source_url": null, "timestamp": "2025-06-05T10:04:09.333000Z", "method": "discord_thread_heuristic", "score": 0.77, "q_line": 0, "a_start": 1, "a_end": 2}
{"id": "Unexpected error from elastic search when sorting search results by column|2025-04-14T07:58:16.455000Z|6aaa0f990a|q5-a6-7", "thread_id": "Unexpected error from elastic search when sorting search results by column|2025-04-14T07:58:16.455000Z|6aaa0f990a", "title": "Unexpected error from elastic search when sorting search results by column", "section": "issues", "question": "```Any idea on what might be causing this issue?", "proposed_answer": "Interestingly, it seems that for fields that do not use schema (such as `'mainfile'` or `'entry_id'`) \"load more\" works regardless of the sorting order. Or it may be related not to the schema but to the fact that all entries have `mainfile` and `entry_id`, while not all of them have `___file_time`.", "source_url": null, "timestamp": "2025-04-14T07:58:16.455000Z", "method": "discord_thread_heuristic", "score": 0.77, "q_line": 5, "a_start": 6, "a_end": 7}
{"id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q10-a11-12", "thread_id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9", "title": "Uploads are getting directly stuck in nomad-distro-dev", "section": "issues", "question": "for your answers, but I am still not really sure what I should do. I only have the 3 infrastructure containers (elastic, rabbitmq, mongo) running and don't see anything special in their logs. The rest is running in the terminal. How can I check if the worker is running properly?", "proposed_answer": "You can check the status of the services by running `docker ps`. This will show \"STATUS\" column that you can check.", "source_url": null, "timestamp": "2025-01-09T14:23:39.252000Z", "method": "discord_thread_heuristic", "score": 0.77, "q_line": 10, "a_start": 11, "a_end": 12}
{"id": "Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4|q53-a61-62", "thread_id": "Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4", "title": "Can't get dev installation to work", "section": "issues", "question": "could this be an issue?", "proposed_answer": "If you have an idea on how the docs could be made clearer I'm sure that Ahmed would appreciate a PR here: https://github.com/FAIRmat-NFDI/nomad-distro-dev 😊", "source_url": null, "timestamp": "2024-12-16T09:34:40.791000Z", "method": "discord_thread_heuristic", "score": 0.77, "q_line": 53, "a_start": 61, "a_end": 62}
{"id": "Error while running the Tutorial|2024-11-19T09:00:42.371000Z|55a178e6ca|q14-a16-17", "thread_id": "Error while running the Tutorial|2024-11-19T09:00:42.371000Z|55a178e6ca", "title": "Error while running the Tutorial", "section": "issues", "question": "When running a north tool for the first time, the docker image will be downloaded. This takes a few minutes, and you might get a 504 timeout error. But if you retry after a couple minutes, the tool should start out fine, like happened here. is there any way to pre-download the images during the distribution bootup? That would be nice, at least for the most used tools like jupyter.", "proposed_answer": "As far as I know, the test Oasis runs on k8. The build is based on the `test-oasis` brach of `nomad-distro`: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-distro/-/tree/test-oasis?ref_type=heads. I think the deployment command is somewhere in here: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-distro/-/blob/test-oasis/.gitlab-ci.yml?ref_type=heads, but you should ask for details.", "source_url": null, "timestamp": "2024-11-19T09:00:42.371000Z", "method": "discord_thread_heuristic", "score": 0.77, "q_line": 14, "a_start": 16, "a_end": 17}
{"id": "Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8|q3-a4-5", "thread_id": "Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8", "title": "Weird GUI data info", "section": "issues", "question": "But I agree in some cases it looks strange that only the first word is capitalized, maybe all words should be capitalized. Would that be better?", "proposed_answer": "Well, I am thinking on usage: when you look into the metainfo tab, at the end you want to resolve some path `run.calculation.band_structure_electronic`, which with these changes is not clear at all.", "source_url": null, "timestamp": "2024-03-01T12:53:07.703000Z", "method": "discord_thread_heuristic", "score": 0.77, "q_line": 3, "a_start": 4, "a_end": 5}
{"id": "nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52|q46-a50-51", "thread_id": "nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52", "title": "nomad-oasis can not stablely be started", "section": "issues", "question": "regarding the ssl access, is it possible that I sccessfully access your services without the ssl, through http directly?", "proposed_answer": "yep the `app` https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/docker-compose.yaml#L93 is separate from `proxy` (nginx) https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/docker-compose.yaml#L190", "source_url": null, "timestamp": "2025-07-17T08:58:19.106000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 46, "a_start": 50, "a_end": 51}
{"id": "Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94|q9-a11-13", "thread_id": "Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94", "title": "Adding h5view of data file to entry overview", "section": "issues", "question": "However this does not change anything in the overview section. Is there something that I am missing?", "proposed_answer": "an `HDF5Reference` quantity, you can populate a path to the dataset in the hdf5 file. In the GUI, under the DATA tab, you can then open the specified dataset in a new lane on the right. https://nomad-lab.eu/prod/v1/docs/howto/customization/hdf5.html#hdf5reference here you can find an example", "source_url": null, "timestamp": "2025-04-03T09:05:50.630000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 9, "a_start": 11, "a_end": 13}
{"id": "Specialized Scatterplots with JMESPath syntax|2025-03-18T10:02:19.551000Z|c675628b74|q3-a7-8", "thread_id": "Specialized Scatterplots with JMESPath syntax|2025-03-18T10:02:19.551000Z|c675628b74", "title": "Specialized Scatterplots with JMESPath syntax", "section": "issues", "question": "Maybe there is an extra whitespace?", "proposed_answer": "Is it expected that the elemental composition from results/material cannot be used as a marker in a scatterplot? I am trying to get a plot with the mass_fraction of a certain element as marker color in a scatterplot, but it doesn't seem to work. I can use e.g. `results.material.n_elements` or use a label e.g. `results.material.elements[-1]` but if I try to use `results.material.elemental_composition[0].element` I get as marker \"undefined\" and if I change from `element` to `mass_fraction` I get an empty plot.", "source_url": null, "timestamp": "2025-03-18T10:02:19.551000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 3, "a_start": 7, "a_end": 8}
{"id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q12-a16-17", "thread_id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c", "title": "Unexpected dependency version on NOMAD develop", "section": "issues", "question": "Or should I use the same dynamic settings depending on the python version in `nomad-schema-plugin-simulation-workflow` as in `nomad-parser-plugin-atomistic`?", "proposed_answer": "The versions on develop is set here: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-distro/-/blob/main/requirements.txt?ref_type=heads#L156", "source_url": null, "timestamp": "2025-01-24T11:39:53.165000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 12, "a_start": 16, "a_end": 17}
{"id": "Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a|q12-a54-55", "thread_id": "Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a", "title": "Issue Starting Nomad Server: Connection Problems with Services", "section": "issues", "question": "Can you try the following to see if you're able to establish a connection with elasticsearch using the python client?", "proposed_answer": "So I think that Elasticsearch is running fine, but could be that somehow WSL is not letting some of the connections through. I think you are following the `nomad-distro-dev` setup in which Elasticsearch is run through docker, and then the app+worker are run as a separate python process that is outside docker. Is this right?", "source_url": null, "timestamp": "2025-01-11T15:49:58.163000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 12, "a_start": 54, "a_end": 55}
{"id": "Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a|q30-a54-55", "thread_id": "Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a", "title": "Issue Starting Nomad Server: Connection Problems with Services", "section": "issues", "question": "Can you try printing out the auth and host?", "proposed_answer": "So I think that Elasticsearch is running fine, but could be that somehow WSL is not letting some of the connections through. I think you are following the `nomad-distro-dev` setup in which Elasticsearch is run through docker, and then the app+worker are run as a separate python process that is outside docker. Is this right?", "source_url": null, "timestamp": "2025-01-11T15:49:58.163000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 30, "a_start": 54, "a_end": 55}
{"id": "Writing tests for plugin and parsing|2024-10-30T11:44:42.436000Z|112339db3e|q1-a3-5", "thread_id": "Writing tests for plugin and parsing|2024-10-30T11:44:42.436000Z|112339db3e", "title": "Writing tests for plugin and parsing", "section": "issues", "question": "`nomad.search.QueryValidationError: results.eln.lab_ids is not a doc quantity` why is it not a doc quantity?", "proposed_answer": "So during your test you are doing an API call or using the `nomad.search` module I guess? I don't think that by default any of the search infrastructure is setup for tests, as typically you only work with the data contained in the archive itself. That is probably why you are getting that error. But I could probably say more if I see the test code that you are trying to run. I think the safest and fastest choice for tests is to mock some of the API/search calls. You can learn more about mocking with pytest here: https://docs.pytest.org/en/stable/how-to/monkeypatch.html", "source_url": null, "timestamp": "2024-10-30T11:44:42.436000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 1, "a_start": 3, "a_end": 5}
{"id": "Safe type casting causes error converting json null to numpy float|2024-09-05T07:25:05.497000Z|0ba81e2463|q2-a24-25", "thread_id": "Safe type casting causes error converting json null to numpy float|2024-09-05T07:25:05.497000Z|0ba81e2463", "title": "Safe type casting causes error converting json null to numpy float", "section": "issues", "question": "Can you have a look?", "proposed_answer": "if then all `null` values will break we can not update", "source_url": null, "timestamp": "2024-09-05T07:25:05.497000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 2, "a_start": 24, "a_end": 25}
{"id": "Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7|q25-a29-30", "thread_id": "Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7", "title": "Publishing an entry from Oasis to Central NOMAD", "section": "issues", "question": "So my question here: how is it different (if it really is) in this case to transfer the metadata to the central NOMAD, compared to only the `results` section let's say ?", "proposed_answer": "I work in a project called MaMMoS where we have to share data of magnetic materials from our Oasis to central-NOMAD. We have set up the Oasis and a distro-dev to test code for first parsers (https://github.com/MaMMoS-project/nomad-mammos-plugin).", "source_url": null, "timestamp": "2024-08-29T07:38:20.591000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 25, "a_start": 29, "a_end": 30}
{"id": "Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765|q22-a40-41", "thread_id": "Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765", "title": "Problem when `docker compose up -d` an Oasis", "section": "issues", "question": "Is there anything unexpected in the GUI response?", "proposed_answer": "whats the output when you run `docker ps`?", "source_url": null, "timestamp": "2024-08-02T08:23:26.436000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 22, "a_start": 40, "a_end": 41}
{"id": "Link to entries broken when changing the Oasis Docker image|2024-07-05T08:07:55.017000Z|bbd7de0589|q8-a25-26", "thread_id": "Link to entries broken when changing the Oasis Docker image|2024-07-05T08:07:55.017000Z|bbd7de0589", "title": "Link to entries broken when changing the Oasis Docker image", "section": "issues", "question": "Is this a normal behaviour or am I doing something wrong when I am trying to visualize the modifications I made on my image by deleting the repo and the Docker image systematically ?", "proposed_answer": "It is possible see the entries and have the files deleted. Depending on the view the GUI shows information from mongodb or from elasticsearch. With our default docker-compose based install the mongo and elastic databases are put into a docker volume. If you use the same docker installation, but removed all the files, the docker volumes will still be there. If you want to delete everything, you also need to delete the docker volumes. By default the volumes are called `nomad_oasis_elastic`, `nomad_oasis_mongo` and `nomad_oasis_rabbitmq`. With `docker volume rm ..` you can delete volumes. If you routinely want to wipe all data in your NOMAD installation, you can also use the nomad cli from within one of the containers:", "source_url": null, "timestamp": "2024-07-05T08:07:55.017000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 8, "a_start": 25, "a_end": 26}
{"id": "Issues with referencing quantities|2024-05-28T08:11:58.541000Z|c3f37c1674|q27-a60-61", "thread_id": "Issues with referencing quantities|2024-05-28T08:11:58.541000Z|c3f37c1674", "title": "Issues with referencing quantities", "section": "issues", "question": "How is it using `QuantityReference`?", "proposed_answer": "Could you check if some part of your code modifies the shape of `KMesh.points`? In the original definition it seems to not have any shape. Otherwise it could be some deeper problem in the wrong shape being somehow set.", "source_url": null, "timestamp": "2024-05-28T08:11:58.541000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 27, "a_start": 60, "a_end": 61}
{"id": "nomad.yaml: issue with new plugin mechanism|2024-05-22T15:47:19.297000Z|5cd4333fd8|q2-a6-7", "thread_id": "nomad.yaml: issue with new plugin mechanism|2024-05-22T15:47:19.297000Z|5cd4333fd8", "title": "nomad.yaml: issue with new plugin mechanism", "section": "issues", "question": "- Furthermore, the `plugins/include` section of the `nomad.yaml` apparently is an \"include only\". (I wasn't aware of that tbh!) The only way to get our plugin running was to omit it completely, which includes every available plugin. Then it is correctly shown in the metainfo browser or the about/information page. - We have not found a way yet to use it with the include section. Maybe somebody knows more?", "proposed_answer": "Ok, it seems things moved around after Lauri's changes. Joe has a branch where he took notes on how to properly clone a plugin, and set it up with the local installation: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/1813", "source_url": null, "timestamp": "2024-05-22T15:47:19.297000Z", "method": "discord_thread_heuristic", "score": 0.76, "q_line": 2, "a_start": 6, "a_end": 7}
{"id": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007|q17-a18-20", "thread_id": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007", "title": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12", "section": "issues", "question": "I tested the [nomad-distro-template](https://github.com/FAIRmat-NFDI/nomad-distro-template), but it seems to download/install/set-up the oasis version 1.3.15? As I looked in the docker-compose and referring images: the [main](https://github.com/fairmat-nfdi/nomad-distro-template/pkgs/container/nomad-distro-template) does not contain a specific version number. How do I know with NOMAD Oasis version is in the \"main-package\"?", "proposed_answer": "You can specify it as a version constraint in dependencies: https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/pyproject.toml#L23 `nomad-lab[parsing, infrastructure]>=1.3.16` or", "source_url": null, "timestamp": "2025-06-06T14:58:44.249000Z", "method": "discord_thread_heuristic", "score": 0.75, "q_line": 17, "a_start": 18, "a_end": 20}
{"id": "Error 500 when creating a new upload|2025-04-07T07:48:07.283000Z|f79556187d|q18-a20-21", "thread_id": "Error 500 when creating a new upload|2025-04-07T07:48:07.283000Z|f79556187d", "title": "Error 500 when creating a new upload", "section": "issues", "question": "Subsidiary question: when the IT person followed the installation steps, they could not run the `docker compose pull` apparently and faced a `permission denied` error. They could only create the image with `docker build . -t oasis-docker-image:main --no-cache --target final`. Have you heard of any similar problem with other users ?", "proposed_answer": "Step 3 from https://github.com/AddMorePower/oasis-docker-image?tab=readme-ov-file#for-a-new-oasis should fix this", "source_url": null, "timestamp": "2025-04-07T07:48:07.283000Z", "method": "discord_thread_heuristic", "score": 0.75, "q_line": 18, "a_start": 20, "a_end": 21}
{"id": "[Tutorial 13] Error docker github|2024-07-22T16:29:11.550000Z|f5445b9ee8|q2-a11-12", "thread_id": "[Tutorial 13] Error docker github|2024-07-22T16:29:11.550000Z|f5445b9ee8", "title": "[Tutorial 13] Error docker github", "section": "issues", "question": "Can you rerun it and check if the issue persists?", "proposed_answer": "runner curl https://gitlab-registry.mpcdf.mpg.de/v2/", "source_url": null, "timestamp": "2024-07-22T16:29:11.550000Z", "method": "discord_thread_heuristic", "score": 0.75, "q_line": 2, "a_start": 11, "a_end": 12}
{"id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q20-a22-23", "thread_id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57", "title": "HDF5 in NOMAD", "section": "issues", "question": "or is there a way to fix a \"path\", https://nomad-lab.eu/prod/v1/staging/docs/howto/customization/hdf5.html#:~:text=path%20%3D%20%27external.h5%23path/to/data%27, in the definition of the Quantity? Like when I make a Quantity(type=HDFReference), can I in my schema fix that whenever Nomad writes this data it directs it to HDF5 in my specfied path?", "proposed_answer": "1. `HDF5Reference` points to a specific dataset within a hdf5 filepath you have to pass when parsing / reading.", "source_url": null, "timestamp": "2024-05-27T13:17:35.844000Z", "method": "discord_thread_heuristic", "score": 0.75, "q_line": 20, "a_start": 22, "a_end": 23}
{"id": "Problem with search function in NOMAD Oasis|2024-05-17T17:04:47.074000Z|6241db1751|q138-a140-141", "thread_id": "Problem with search function in NOMAD Oasis|2024-05-17T17:04:47.074000Z|6241db1751", "title": "Problem with search function in NOMAD Oasis", "section": "issues", "question": "I have no idea why this solution works, but it does. Maybe you have an idea?", "proposed_answer": "It's good that you found a solution with which you can proceed, but it is clear that we need to improve the `search` interface. I will create a GitLab issue to add a possibility to use plain dictionaries as the query, and also to fix the problem with the custom quantities not being visible during reprocessing.", "source_url": null, "timestamp": "2024-05-17T17:04:47.074000Z", "method": "discord_thread_heuristic", "score": 0.75, "q_line": 138, "a_start": 140, "a_end": 141}
{"id": "Problem with `nomad-distro-dev`|2024-10-24T15:21:28.021000Z|3f0f258a83|q8-a23-25", "thread_id": "Problem with `nomad-distro-dev`|2024-10-24T15:21:28.021000Z|3f0f258a83", "title": "Problem with `nomad-distro-dev`", "section": "issues", "question": "Could you check which version of `typing-extensions `you have? Can be done e.g. with `pip list`. The nomad package requires `typing-extensions==4.12.2`, but it could be that e.g. some of the plugins downgrade on your machine. Any further ideas here?", "proposed_answer": "Created a MR for this https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2181 Yep, that's `4.4.0` for me. I'll keep that in mind, we might need to do some updating on the `nomad-parser-plugins-atomistic`.", "source_url": null, "timestamp": "2024-10-24T15:21:28.021000Z", "method": "discord_thread_heuristic", "score": 0.74, "q_line": 8, "a_start": 23, "a_end": 25}
{"id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q5-a10-11", "thread_id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459", "title": "Adapting existing Oasis to new plugin mechanism", "section": "issues", "question": "3. I also use baseclasses. I remember there was a discussion about this before here in Discord with . I think I have to use a different NOMAD image, but I am not quite sure which one and in which file I have to change it?", "proposed_answer": "4. If the plugins are defined in the entrypoints, they will be automatically loaded, so there's nothing to be added to the `nomad.yaml` file. The documentation [here](https://nomad-lab.eu/prod/v1/develop/docs/howto/plugins/plugins.html#plugin-entry-points) might be helpful", "source_url": null, "timestamp": "2024-10-01T12:39:26.289000Z", "method": "discord_thread_heuristic", "score": 0.74, "q_line": 5, "a_start": 10, "a_end": 11}
{"id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q71-a76-77", "thread_id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459", "title": "Adapting existing Oasis to new plugin mechanism", "section": "issues", "question": "In the end this button is nothing more than a temporary boolean+save. Like said, a checkbox plus pressing save would be similar. The button would probably also come along as a \"EditQuantity\" component. The backend part (e.g. setting it to false again) would still be done with in `normalize` during processing. Something like this should not be to hard to add. I also guess, this is exactly what has in his code, right?", "proposed_answer": "I now commented on <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1610> and <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1955>. The relevant people here, should be part of the respective issues. My idea would be to work on the \"button\" and on the action you do (e.g. creating entries, updating entries) together and to finally document such a use-base with a how-to in the documentation.", "source_url": null, "timestamp": "2024-10-01T12:39:26.289000Z", "method": "discord_thread_heuristic", "score": 0.74, "q_line": 71, "a_start": 76, "a_end": 77}
{"id": "Fail to launch jupyter hub on oasis|2024-08-28T12:48:11.408000Z|941875913b|q3-a8-9", "thread_id": "Fail to launch jupyter hub on oasis|2024-08-28T12:48:11.408000Z|941875913b", "title": "Fail to launch jupyter hub on oasis", "section": "issues", "question": "Unfortunately I am no longer on site with the users so will take a bit of time to check this but I'll ask the IT admin. For my understanding, is it that it tries to mount these folders in the jupyter container and then can't access them?", "proposed_answer": "You can also run the `nomad admin uploads integrity --missing-storage` command in the container to check if there are uploads with missing (or inaccesible) directories.", "source_url": null, "timestamp": "2024-08-28T12:48:11.408000Z", "method": "discord_thread_heuristic", "score": 0.74, "q_line": 3, "a_start": 8, "a_end": 9}
{"id": "Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765|q11-a16-17", "thread_id": "Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765", "title": "Problem when `docker compose up -d` an Oasis", "section": "issues", "question": "In the app section of the docker compose file, instead of your custom image, could you try with our official one?", "proposed_answer": "For now you can try changing nomad-fair:latest to nomad-fair:v1.3.4 here https://github.com/Guillaume-Gaisne/AMP_internal_Oasis_image/blob/main/Dockerfile", "source_url": null, "timestamp": "2024-08-02T08:23:26.436000Z", "method": "discord_thread_heuristic", "score": 0.74, "q_line": 11, "a_start": 16, "a_end": 17}
{"id": "Nexus Search very slow for larger entry numbers|2025-03-27T17:23:32.159000Z|ea977dcfa4|q17-a18-20", "thread_id": "Nexus Search very slow for larger entry numbers|2025-03-27T17:23:32.159000Z|ea977dcfa4", "title": "Nexus Search very slow for larger entry numbers", "section": "issues", "question": "That makes sense, that can probably also speed up other widgets, as for histograms/terms you even only need one search quantity, no?", "proposed_answer": "In the case of histogram/terms the code is not even looking at the search quantity values directly, but we instead use the internal aggregation calls from ES. So these widgets don't have the same performance problem. This indeed seems to help a lot. I drafted this solution here as a starting point: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2408", "source_url": null, "timestamp": "2025-03-27T17:23:32.159000Z", "method": "discord_thread_heuristic", "score": 0.73, "q_line": 17, "a_start": 18, "a_end": 20}
{"id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q15-a18-19", "thread_id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9", "title": "Uploads are getting directly stuck in nomad-distro-dev", "section": "issues", "question": "ok thanks, I have the devcontainer extension already installed in my VS Code, but I am not quite sure how to use it. Is there a straightforward approach or a page where this is described?", "proposed_answer": "Great, I think I am getting closer to the solution. I reopened the project in a devcontainer. But now my question is in which terminal should I enter the `uv run poe start` command. In the bash terminal it does not work. (Sorry for my stupid questions, but I am really not an expert in this stuff). Really appreciate your help.", "source_url": null, "timestamp": "2025-01-09T14:23:39.252000Z", "method": "discord_thread_heuristic", "score": 0.73, "q_line": 15, "a_start": 18, "a_end": 19}
{"id": "Customized nexus subsection is not rendered|2024-07-17T13:19:45.414000Z|d8cdf78efe|q2-a5-6", "thread_id": "Customized nexus subsection is not rendered|2024-07-17T13:19:45.414000Z|d8cdf78efe", "title": "Customized nexus subsection is not rendered", "section": "issues", "question": "Am I missing to add anything configuration parameters in the nomad.yaml config file? Or this is a bug?", "proposed_answer": "services: api_host: 'localhost' api_base_path: '/fairdi/nomad/latest' oasis: is_oasis: true uses_central_user_management: true north: jupyterhub_crypt_key: '0a6d514e175a9ee6cbc525561afd18598f4fc570ab6e5fa26402e8721bb1308c' enabled: true meta: deployment: 'oasis' deployment_url: 'https://my-oasis.org/api' maintainer_email: 'me mongo: db_name: nomad_oasis_v1 elastic: entries_index: nomad_oasis_entries_v1 materials_index: nomad_oasis_materials_v1", "source_url": null, "timestamp": "2024-07-17T13:19:45.414000Z", "method": "discord_thread_heuristic", "score": 0.73, "q_line": 2, "a_start": 5, "a_end": 6}
{"id": "k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525|q27-a30-31", "thread_id": "k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing|2024-07-12T10:18:27.642000Z|c6b7f00525", "title": "k_line_density wrong in (not only) case of symmetry reduced k-grid - issues in k_mesh normalizing", "section": "issues", "question": "2. The other is *symmetry*. I think that unfolding the grid goes beyond the scope of a fix (though we may consider it as a feature in the future). I would see to leverage the *multiplicities* here. You mentioned that for OpenMX these would trivial to set (all 1 I assume)?", "proposed_answer": "4. Very astute observation! I'd say it depends: if a 2D system is modeled in 3D, the extra dimension should not count. If it is truly 3D, then the dimension with only 1 k-point may indeed be the precision bottleneck, and `k_line_density` should point that out. Typically codes are pretty clear on a system's dimensionality, but some (e.g. VASP) always defaults to 3D, or the user does not leverage the dimension control well... Luckily we have a package that ascertains the dimensionality independently: MatID by Now it just becomes a question of the normalization order: `MethodNormalizer` has to come after. We might also call the analyzer 2x, but it's a pretty expensive routine.", "source_url": null, "timestamp": "2024-07-12T10:18:27.642000Z", "method": "discord_thread_heuristic", "score": 0.73, "q_line": 27, "a_start": 30, "a_end": 31}
{"id": "Link to entries broken when changing the Oasis Docker image|2024-07-05T08:07:55.017000Z|bbd7de0589|q22-a25-26", "thread_id": "Link to entries broken when changing the Oasis Docker image|2024-07-05T08:07:55.017000Z|bbd7de0589", "title": "Link to entries broken when changing the Oasis Docker image", "section": "issues", "question": "So if the files folder has been deleted, how is it that the entries in the Explore page are still showing up ?", "proposed_answer": "It is possible see the entries and have the files deleted. Depending on the view the GUI shows information from mongodb or from elasticsearch. With our default docker-compose based install the mongo and elastic databases are put into a docker volume. If you use the same docker installation, but removed all the files, the docker volumes will still be there. If you want to delete everything, you also need to delete the docker volumes. By default the volumes are called `nomad_oasis_elastic`, `nomad_oasis_mongo` and `nomad_oasis_rabbitmq`. With `docker volume rm ..` you can delete volumes. If you routinely want to wipe all data in your NOMAD installation, you can also use the nomad cli from within one of the containers:", "source_url": null, "timestamp": "2024-07-05T08:07:55.017000Z", "method": "discord_thread_heuristic", "score": 0.73, "q_line": 22, "a_start": 25, "a_end": 26}
{"id": "Missing help text in some EditQuantity(s)|2024-06-28T09:53:34.561000Z|07e5e54ac1|q0-a10-11", "thread_id": "Missing help text in some EditQuantity(s)|2024-06-28T09:53:34.561000Z|07e5e54ac1", "title": "Missing help text in some EditQuantity(s)", "section": "issues", "question": "Is there a reason by some EditQuantity(s) do not have help-text icon to show the description?", "proposed_answer": "https://nomad-lab.eu/prod/v1/staging/gui/dev/editquantity", "source_url": null, "timestamp": "2024-06-28T09:53:34.561000Z", "method": "discord_thread_heuristic", "score": 0.73, "q_line": 0, "a_start": 10, "a_end": 11}
{"id": "errors in the gui  whenquantitities are of type=np.float64 and no unit given|2024-03-01T12:38:57.676000Z|289ca000c4|q15-a16-19", "thread_id": "errors in the gui  whenquantitities are of type=np.float64 and no unit given|2024-03-01T12:38:57.676000Z|289ca000c4", "title": "errors in the gui  whenquantitities are of type=np.float64 and no unit given", "section": "issues", "question": "I think this is also for But he is going on vacation for two weeks. How critical is this to you?", "proposed_answer": "it's is quite a bug since a `NumberEditQuantity` without a unit is used almost in all schemas, I wold say I can imagine. I try to look into it. I merged a fix. https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/1702", "source_url": null, "timestamp": "2024-03-01T12:38:57.676000Z", "method": "discord_thread_heuristic", "score": 0.73, "q_line": 15, "a_start": 16, "a_end": 19}
{"id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q70-a72-76", "thread_id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93", "title": "FHIaims parsing issues", "section": "issues", "question": "You should use a published package, using git dependencies won't work since the gui would not be available. do you know when the fix was made?", "proposed_answer": "In that case, in the pyproject.toml you can set the lower bound for nomad-lab to 1.3.16 which was released a month ago. `\"nomad-lab[parsing, infrastructure]>=1.3.16\",` OK, building it with \"nomad-lab[parsing, infrastructure]>=1.3.16\" works. To be honest I was assuming that if I don't explicitly specify package version like for nomad-lab[parsing, infrastructure] it will already give me the latest released version? Anyway, with \"nomad-lab[parsing, infrastructure]>=1.3.16\" and develop electronic-parsers (latest version in https://github.com/ondracka/nomad-distro-template) I'm still getting the FHIaims normalizing error from above.", "source_url": null, "timestamp": "2025-07-24T12:22:12.739000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 70, "a_start": 72, "a_end": 76}
{"id": "Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3|q18-a19-20", "thread_id": "Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3", "title": "Solve `mongo_user_group` warning in the application startup", "section": "issues", "question": "So the right way is python3 merge.py --host localhost?", "proposed_answer": "Don't forget the other options from the example, `--db-name nomad_v1` and `--action ..`", "source_url": null, "timestamp": "2025-07-16T15:57:41.003000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 18, "a_start": 19, "a_end": 20}
{"id": "Modify the archive structure?|2025-07-14T08:02:22.296000Z|0a0bcf7fd2|q5-a6-7", "thread_id": "Modify the archive structure?|2025-07-14T08:02:22.296000Z|0a0bcf7fd2", "title": "Modify the archive structure?", "section": "issues", "question": "Anyway could you expain more about the last point? What does it mean that the latest updates allows to search in a different way the quantities?", "proposed_answer": "You can add them as searchable quantities in apps: https://nomad-lab.eu/prod/v1/staging/docs/howto/plugins/apps.html#loading-quantity-definitions-into-an-app", "source_url": null, "timestamp": "2025-07-14T08:02:22.296000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 5, "a_start": 6, "a_end": 7}
{"id": "Limiting the Oasis workers to save memory|2025-04-09T08:09:43.694000Z|70654d8f14|q2-a8-9", "thread_id": "Limiting the Oasis workers to save memory|2025-04-09T08:09:43.694000Z|70654d8f14", "title": "Limiting the Oasis workers to save memory", "section": "issues", "question": "Cool! Any idea what could be a reasonable value for this?", "proposed_answer": "I guess the max memory per child has a similar effdect. But, there is another important setting. This is the one i was mentioning once. This one specifically mentiones the use case of memory leaks: https://docs.celeryq.dev/en/latest/userguide/workers.html#max-tasks-per-child-setting", "source_url": null, "timestamp": "2025-04-09T08:09:43.694000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 2, "a_start": 8, "a_end": 9}
{"id": "Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c|q38-a39-40", "thread_id": "Mongo DB collection warning is weird|2025-03-07T10:17:12.565000Z|86f9155d8c", "title": "Mongo DB collection warning is weird", "section": "issues", "question": ": Could you share the migration script with Laurenz?", "proposed_answer": "Here is the snippet: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/snippets/181", "source_url": null, "timestamp": "2025-03-07T10:17:12.565000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 38, "a_start": 39, "a_end": 40}
{"id": "Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5|q33-a39-40", "thread_id": "Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5", "title": "Protected Oasis - Restrict GUI/API Access only for Login-User", "section": "issues", "question": "But anyway, that's not the point: the oasis has a \"public search\" functionality - also the API giving you all information about (meta)data without an account. This \"public sharing\" feature is nice and good for the official NOMAD instance, but not adequate for \"data sharing between several groups in a protected environment\". I want to restrict the API call and \"gui/search\" functionality only known users to promoted \"sharing (not publishing) with the Collaborative Research Centre\". Does this help what I am aiming for?", "proposed_answer": "👍 Thank you! Now, I understand why `allowed_users` for an oasis should to the same thing. I've not be aware of, that this also will restrict the API call. I will check that in the file `nomad/app/v1/routers/auth.py` But as you facing the same problem as I am, maybe a more general approach without relying on nomad.yaml would be great. Thanks again for the advice 🙂", "source_url": null, "timestamp": "2025-02-28T08:48:27.056000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 33, "a_start": 39, "a_end": 40}
{"id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q136-a137-138", "thread_id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b", "title": "Unexpected error in the research app", "section": "issues", "question": "So to ruturn and close the issue we installed a 1.3.16dev170 this version allows for the correct visualization of the celsius units in the research app, i.e. the bug is already corrected in this version?", "proposed_answer": "Moreover when I try to take upto date my local repository in the merge I take the conflict in the pyproject", "source_url": null, "timestamp": "2025-02-28T07:57:57.339000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 136, "a_start": 137, "a_end": 138}
{"id": "search widget showing more then 5 hits?|2025-02-24T08:50:06.398000Z|7eb3b96db9|q13-a14-15", "thread_id": "search widget showing more then 5 hits?|2025-02-24T08:50:06.398000Z|7eb3b96db9", "title": "search widget showing more then 5 hits?", "section": "issues", "question": "```Should the suggestions work in the Filter Menu?", "proposed_answer": "There was an issue with the suggestions that was fixed recently in `develop`. The fix is also available in 1.3.14 which is released today.", "source_url": null, "timestamp": "2025-02-24T08:50:06.398000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 13, "a_start": 14, "a_end": 15}
{"id": "Customization of an app|2025-02-24T07:24:54.477000Z|a04c15214a|q56-a57-58", "thread_id": "Customization of an app|2025-02-24T07:24:54.477000Z|a04c15214a", "title": "Customization of an app", "section": "issues", "question": "Ok thanks for the help but in absence of a fix I opted for a different solution, i.e. describe every step as an entrydata and than link every step through a reference. The last point that remain is: when the filters are applied there is a way to concatenate various filters referred to the same quantity with an \"and\" option instead of an or?", "proposed_answer": "currently no way to change the default query mode. This is mostly an oversight from our part. Let me create a feature request out of this and we try to get this into release 1.3.15.", "source_url": null, "timestamp": "2025-02-24T07:24:54.477000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 56, "a_start": 57, "a_end": 58}
{"id": "Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a|q28-a29-30", "thread_id": "Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a", "title": "Issue Starting Nomad Server: Connection Problems with Services", "section": "issues", "question": "So the connection to Elasticsearch seems to be working, what could be the problem then? What could I check during the server startup process?", "proposed_answer": "The url could possibly be different, in `packages/nomad-FAIR/infrastructure.py` there's a function called `setup_elastic`", "source_url": null, "timestamp": "2025-01-11T15:49:58.163000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 28, "a_start": 29, "a_end": 30}
{"id": "Unit Registry|2024-12-19T15:34:54.028000Z|c5631a8352|q10-a11-12", "thread_id": "Unit Registry|2024-12-19T15:34:54.028000Z|c5631a8352", "title": "Unit Registry", "section": "issues", "question": "Now its working. is there \"Percentage\" as unit there?", "proposed_answer": "Anyway, I am looking here for the list: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/blob/develop/nomad/units/default_en.txt?ref_type=heads", "source_url": null, "timestamp": "2024-12-19T15:34:54.028000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 10, "a_start": 11, "a_end": 12}
{"id": "Authorization issue?|2024-12-03T14:06:17.738000Z|83c2b6ccd7|q1-a9-10", "thread_id": "Authorization issue?|2024-12-03T14:06:17.738000Z|83c2b6ccd7", "title": "Authorization issue?", "section": "issues", "question": "Does/did anyone else have this issue?", "proposed_answer": "Didn't solve it unfortunately. I'll try a system reboot.", "source_url": null, "timestamp": "2024-12-03T14:06:17.738000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 1, "a_start": 9, "a_end": 10}
{"id": "Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff|q26-a27-28", "thread_id": "Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff", "title": "Permission denied when creating new Jupyter Notebook in uploads folder", "section": "issues", "question": "more about how/where are you trying to create the notebook? Are you trying to upload it inside an existing upload? Are you trying to create it during processing? Are you trying to create it inside one of the NORTH tools?", "proposed_answer": "I started the JupyterLab in the NORTH Tools and then navigated there to the upload folder (in the left menu) and then in the upload folder clicked right -> \"New Notebook\". And then the eroor appears: `Permission denied: uploads/xxx-test-north-788ekQCHQh2G83Atjn-nCw/Untitled.ipynb`", "source_url": null, "timestamp": "2024-11-20T11:04:36.071000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 26, "a_start": 27, "a_end": 28}
{"id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q71-a76-77", "thread_id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d", "title": "Memory error when I build my container", "section": "issues", "question": "What's the output for ` docker image inspect ghcr.io/paulogithb/nomad-oasis-cemes:main`?", "proposed_answer": "Error: No such image: ghcr.io/paulogithb/nomad-oasis-cemes:main", "source_url": null, "timestamp": "2024-11-15T09:44:36.042000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 71, "a_start": 76, "a_end": 77}
{"id": "Unintuitive behavior of entry selection after SelectAll|2024-11-06T09:46:27.241000Z|978a2b109e|q2-a3-4", "thread_id": "Unintuitive behavior of entry selection after SelectAll|2024-11-06T09:46:27.241000Z|978a2b109e", "title": "Unintuitive behavior of entry selection after SelectAll", "section": "issues", "question": "Yes, that is indeed unexpected. Could you create an issue about this in Gitlab?", "proposed_answer": "Done: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2191", "source_url": null, "timestamp": "2024-11-06T09:46:27.241000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 2, "a_start": 3, "a_end": 4}
{"id": "# not escaped in API calls|2024-10-30T10:45:15.019000Z|64a53a1248|q3-a4-5", "thread_id": "# not escaped in API calls|2024-10-30T10:45:15.019000Z|64a53a1248", "title": "# not escaped in API calls", "section": "issues", "question": "That might very well be the case. Could you create an issue about that in Gitlab?", "proposed_answer": "https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2183", "source_url": null, "timestamp": "2024-10-30T10:45:15.019000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 3, "a_start": 4, "a_end": 5}
{"id": "nomad parse not working in 13.10|2024-10-28T15:43:27.988000Z|c3418adc85|q16-a17-18", "thread_id": "nomad parse not working in 13.10|2024-10-28T15:43:27.988000Z|c3418adc85", "title": "nomad parse not working in 13.10", "section": "issues", "question": "Not sure what this mean. Was this the MR that broke this or is this a MR that aims to fix it?", "proposed_answer": "https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2130", "source_url": null, "timestamp": "2024-10-28T15:43:27.988000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 16, "a_start": 17, "a_end": 18}
{"id": "nomad parse not working in 13.10|2024-10-28T15:43:27.988000Z|c3418adc85|q25-a26-27", "thread_id": "nomad parse not working in 13.10|2024-10-28T15:43:27.988000Z|c3418adc85", "title": "nomad parse not working in 13.10", "section": "issues", "question": "This was merged, how did you test this?", "proposed_answer": "I ran `nomad parse tests/data/test_sample.archive.yaml --show-archive > normalized.archive.json` in my plugin environment, which has 'nomad-lab 1.3.11.dev19+g5dacb5963.d20241022 /home/<USER>/nomad' installed", "source_url": null, "timestamp": "2024-10-28T15:43:27.988000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 25, "a_start": 26, "a_end": 27}
{"id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q3-a4-5", "thread_id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459", "title": "Adapting existing Oasis to new plugin mechanism", "section": "issues", "question": "2. Previously my highest folder in the structure was called “UMR_schemas”. Now it is called “schema_packages”. I would prefer to use “schema_packages” but I am not sure If this will lead to a problem, because the already existing archives refer to the definitions in “UMR_schemas…”. Am I right in thinking that this is a problem? And how can I solve it? I think I have read something about aliases in the docs (https://nomad-lab.eu/prod/v1/docs/howto/plugins/schema_packages.html) . Should I use those in this case?", "proposed_answer": "I also already prepared my own image (https://github.com/AG-SEK/nomad-oasis-perolab-umr-image) using the other Github template. At the time of the recording of the tutorial there was still a “plugins.txt” file in the image repository. If I understood it correctly now instead of adding the plugins to the “plugin.txt” file, one needs to write them in the plugins list in the “pyproject.toml”. I did it and it seems to work. But I have some more issues related to the image:", "source_url": null, "timestamp": "2024-10-01T12:39:26.289000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 3, "a_start": 4, "a_end": 5}
{"id": "Change Affiliation|2024-09-10T06:49:20.188000Z|ab02169519|q0-a1-2", "thread_id": "Change Affiliation|2024-09-10T06:49:20.188000Z|ab02169519", "title": "Change Affiliation", "section": "issues", "question": "Back when I first registered on NOMAD, I did not enter the full name of my affiliation and the address. How can I change it?", "proposed_answer": "this is something also commented in this issue: https://github.com/nomad-coe/nomad/issues/112", "source_url": null, "timestamp": "2024-09-10T06:49:20.188000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 0, "a_start": 1, "a_end": 2}
{"id": "Change Affiliation|2024-09-10T06:49:20.188000Z|ab02169519|q10-a11-12", "thread_id": "Change Affiliation|2024-09-10T06:49:20.188000Z|ab02169519", "title": "Change Affiliation", "section": "issues", "question": "Based on your screenshot you might using a newer version of keycloak where this issue is already fixed. Are you using keycloak.v2 theme for Account theme?", "proposed_answer": "I'm guessing but I think you will see keycloak.v3. As far as I can see v2 is not even maintained/supported anymore so we might need to update keycloak to fix this issue.", "source_url": null, "timestamp": "2024-09-10T06:49:20.188000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 10, "a_start": 11, "a_end": 12}
{"id": "Plugin Loaded twice|2024-09-04T10:23:06.791000Z|351b42840e|q8-a9-10", "thread_id": "Plugin Loaded twice|2024-09-04T10:23:06.791000Z|351b42840e", "title": "Plugin Loaded twice", "section": "issues", "question": "I can try to investigate this. You are seeing when using the `main` branch of `nomad-measurements`?", "proposed_answer": "Ok, I think I figured it out. The aliasing mechanism is causing this duplication. This issue is being tracked in [#2122](https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2122) Should be fairly easy to solve, I keep you updated.", "source_url": null, "timestamp": "2024-09-04T10:23:06.791000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 8, "a_start": 9, "a_end": 10}
{"id": "Creating multiple uploads causes some to get stuck \"Waiting\"|2024-08-28T11:24:51.603000Z|7bca1ff866|q23-a31-32", "thread_id": "Creating multiple uploads causes some to get stuck \"Waiting\"|2024-08-28T11:24:51.603000Z|7bca1ff866", "title": "Creating multiple uploads causes some to get stuck \"Waiting\"", "section": "issues", "question": "Should this not be reset with a docke compose down and up?", "proposed_answer": "👍 In production we use something like 128Gb for heavy processing.. But this all depends on what sort of data you are dealing with and how smart the parsers are", "source_url": null, "timestamp": "2024-08-28T11:24:51.603000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 23, "a_start": 31, "a_end": 32}
{"id": "Docker build fails for latest develop|2024-07-10T07:48:13.050000Z|af964aa462|q28-a29-30", "thread_id": "Docker build fails for latest develop|2024-07-10T07:48:13.050000Z|af964aa462", "title": "Docker build fails for latest develop", "section": "issues", "question": "Here is the log, I build without args (really just docker build . in the nomad source tree). I've been doing it like this in the past few years, Do I need some args now?", "proposed_answer": "``` docker build . --target final --build-arg SETUPTOOLS_SCM_PRETEND_VERSION=1.3.3```", "source_url": null, "timestamp": "2024-07-10T07:48:13.050000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 28, "a_start": 29, "a_end": 30}
{"id": "The entry does not exist problem in archive|2024-06-14T04:39:22.417000Z|6d25ec3b83|q0-a1-2", "thread_id": "The entry does not exist problem in archive|2024-06-14T04:39:22.417000Z|6d25ec3b83", "title": "The entry does not exist problem in archive", "section": "issues", "question": "Any idea why I'm getting \"The entry does not exist\" here?", "proposed_answer": "https://nomad-lab.eu/prod/v1/gui/upload/id/wTBuDynkQ8WdfJ4Rrw5IDg/entry/id/XKEQiB-r0f1QjlfNBzsrLrsICGJq", "source_url": null, "timestamp": "2024-06-14T04:39:22.417000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 0, "a_start": 1, "a_end": 2}
{"id": "Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed|q41-a42-43", "thread_id": "Mkdocs Error when updating Nomad|2024-05-07T09:51:39.304000Z|a36875abed", "title": "Mkdocs Error when updating Nomad", "section": "issues", "question": "Maybe its just the shell command. Maybe it comes from a different environment or uses the wrong environment. Something mixed up in the PATH or PYTHONPATH maybe. What do you get when running `which mkdocs` and what does the file look like?", "proposed_answer": "`which mkdocs` points to the correct environment: `/home/<USER>/software/nomad/.pyenv/bin/mkdocs`", "source_url": null, "timestamp": "2024-05-07T09:51:39.304000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 41, "a_start": 42, "a_end": 43}
{"id": "Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5|q23-a24-26", "thread_id": "Accessing Section Definitions via API|2024-04-12T07:49:39.151000Z|a1940510b5", "title": "Accessing Section Definitions via API", "section": "issues", "question": "Ok, so i did manage to use a post request, retrieve the data via 'entries/archive/query' and i can access response['data']['archive']['data']. I could not quite figure out how to convert my dict back into a nomad object yet. Do i have to import the 'EntryArchive'-class from nomad?", "proposed_answer": "Great! Indeed, you should import `EntryArchive` and then feed in `response['data']['archive']`. Note that the conversion may fail if you filtered out parts of the response. One addtional question what we are looking for is information which is in here: https://nomad-hzb-ce.de/nomad-oasis/gui/artifacts.js stuff like this.", "source_url": null, "timestamp": "2024-04-12T07:49:39.151000Z", "method": "discord_thread_heuristic", "score": 0.72, "q_line": 23, "a_start": 24, "a_end": 26}
{"id": "Tabular parser row chemical_formula|2025-06-12T09:58:22.006000Z|b6699b3fd8|q4-a26-27", "thread_id": "Tabular parser row chemical_formula|2025-06-12T09:58:22.006000Z|b6699b3fd8", "title": "Tabular parser row chemical_formula", "section": "issues", "question": "How should this be done? What am I doing wrong?", "proposed_answer": "you should be able to call the section Sample, it is not protected. If you call it Sample, remember to also change the type in line 65 to '#/Sample'", "source_url": null, "timestamp": "2025-06-12T09:58:22.006000Z", "method": "discord_thread_heuristic", "score": 0.71, "q_line": 4, "a_start": 26, "a_end": 27}
{"id": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007|q1-a14-15", "thread_id": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007", "title": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12", "section": "issues", "question": "Btw: the official python version for NOMAD is Python3.12, nor?", "proposed_answer": "Building the image for deployment via `nomad-FAIR` repo is no longer supported, please use https://github.com/FAIRmat-NFDI/nomad-distro-template to build deployment docker images", "source_url": null, "timestamp": "2025-06-06T14:58:44.249000Z", "method": "discord_thread_heuristic", "score": 0.71, "q_line": 1, "a_start": 14, "a_end": 15}
{"id": "Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e|q8-a11-12", "thread_id": "Where to find available base sections for yaml|2025-06-02T09:45:34.629000Z|6904b3e81e", "title": "Where to find available base sections for yaml", "section": "issues", "question": "How come?", "proposed_answer": "You can click on the \"ALL INHERITING SECTIONS\" and they should load", "source_url": null, "timestamp": "2025-06-02T09:45:34.629000Z", "method": "discord_thread_heuristic", "score": 0.71, "q_line": 8, "a_start": 11, "a_end": 12}
{"id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q38-a40-42", "thread_id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b", "title": "Unexpected error in the research app", "section": "issues", "question": "Great job on the PPM introduction! Regarding the Oasis update, I spoke with the lab manager, and he told me that we have installed version 1.3.14dev160—if I understood correctly. So, could the fix already be effective?", "proposed_answer": "The fix for the error with the menu+Celsius unit is available from `1.3.16.dev106` onwards. Let me know if you need help in installing this. If I understand the question correctly, you would want to hide some ELN fields based on a boolean value? I think changing the GUI layout conditionally is not currently possible, but you can in your normalize functions create a warning/error if some of the required data is not available.", "source_url": null, "timestamp": "2025-02-28T07:57:57.339000Z", "method": "discord_thread_heuristic", "score": 0.71, "q_line": 38, "a_start": 40, "a_end": 42}
{"id": "In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c|q8-a23-24", "thread_id": "In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c", "title": "In and ELN lane, the rendering of API response in json is overlapping with fields.", "section": "issues", "question": "As to the second problem: Now I have the referenced entry (Identifier_exp3b6a73f710eaf793557ddecb76d0f73e.archive.json), but I would also need to know the class for \"Experiment identifier\" in order to know what types of references `entry_reference` accepts. Is this part of some custom schema, or part of our base sections?", "proposed_answer": "Sorry, I'm answering late here.To find out why a reference to an Entity does not work in your schema, we will have to try and create a minimal non-working example. Do you have the Python code that defines `pynxtools.Identifier.reference`? I could not spot anything wrong in this link that you sent me: https://nomad-lab.eu/prod/v1/oasis/gui/analyze/metainfo/pynxtools/section_definitions", "source_url": null, "timestamp": "2024-11-07T15:03:16.142000Z", "method": "discord_thread_heuristic", "score": 0.71, "q_line": 8, "a_start": 23, "a_end": 24}
{"id": "Scatterplot: focus on region|2024-11-04T13:23:24.664000Z|9bdeb903e8|q10-a14-15", "thread_id": "Scatterplot: focus on region|2024-11-04T13:23:24.664000Z|9bdeb903e8", "title": "Scatterplot: focus on region", "section": "issues", "question": "Any idea what we should do in the case where we filter for a value in an array? Maybe we keep all the entries that contain at least one match in the array, but we should simply zoom in on the region and not show the rest of the points that did not match. Or do you think we should require all of the points in that array to match the criterion?", "proposed_answer": "The reason why the \"Filter\" results may seem weird is the fact that filters simply return all entries where there is some data that fullfills the query. It does not care about where in the data the condition is filled, and will always just return the full entry. This is why if you create a filter based on `results.properties.catalytic.reaction.reaction_conditions.temperature` `results.properties.catalytic.reaction.reactants.gas_concentration_in`, the plot may also contain other reactions, simply because they are included in this entry where one of the reactions matched the query.", "source_url": null, "timestamp": "2024-11-04T13:23:24.664000Z", "method": "discord_thread_heuristic", "score": 0.71, "q_line": 10, "a_start": 14, "a_end": 15}
{"id": "Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin|2024-10-22T10:58:19.936000Z|41985e2c35|q25-a29-30", "thread_id": "Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin|2024-10-22T10:58:19.936000Z|41985e2c35", "title": "Uploads not processing on a NOMAD Oasis with HBZ_hysprint plugin", "section": "issues", "question": "And also, back to my original question: is it the expected behavior when there is no parser for a certain file format? Like no error message / no crash / just not starting the processing at all (but the uploaded files can be previewed) ?", "proposed_answer": "in the nomad.yaml file: https://nomad-lab.eu/prod/v1/docs/reference/config.html#entrypoints", "source_url": null, "timestamp": "2024-10-22T10:58:19.936000Z", "method": "discord_thread_heuristic", "score": 0.71, "q_line": 25, "a_start": 29, "a_end": 30}
{"id": "`ArchiveQuery` fetches nothing in Oasis|2024-04-18T13:22:06.338000Z|161631014e|q11-a36-37", "thread_id": "`ArchiveQuery` fetches nothing in Oasis|2024-04-18T13:22:06.338000Z|161631014e", "title": "`ArchiveQuery` fetches nothing in Oasis", "section": "issues", "question": "Would you expect something, even if the authentication does not work?", "proposed_answer": "But for entries belonging to certain entry classes from plugins, I run into an AttributeError when accessing `context.name`. The error is attached along. ran into the similar error and it seems it was resolved here https://discord.com/channels//", "source_url": null, "timestamp": "2024-04-18T13:22:06.338000Z", "method": "discord_thread_heuristic", "score": 0.71, "q_line": 11, "a_start": 36, "a_end": 37}
{"id": "Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8|q12-a56-57", "thread_id": "Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8", "title": "Weird GUI data info", "section": "issues", "question": "is being fixed with https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/1693 ?", "proposed_answer": "Issue: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1915", "source_url": null, "timestamp": "2024-03-01T12:53:07.703000Z", "method": "discord_thread_heuristic", "score": 0.71, "q_line": 12, "a_start": 56, "a_end": 57}
{"id": "Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app|2025-08-07T09:10:48.009000Z|5e08ea8db6|q3-a5-6", "thread_id": "Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app|2025-08-07T09:10:48.009000Z|5e08ea8db6", "title": "Conflicting `MenuItemTerms` with the same `search_quantity` in plugin/app", "section": "issues", "question": "I think this should in general be possible. The issue might be related to us using the same query key for items within a menu. Could you create an issue in Gtlab for this with a minimal app example that demonstrates the problem?", "proposed_answer": "https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2389", "source_url": null, "timestamp": "2025-08-07T09:10:48.009000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 3, "a_start": 5, "a_end": 6}
{"id": "Automated test to mimic GUI functionalities|2025-06-27T09:10:50.283000Z|f17ee76551|q1-a3-4", "thread_id": "Automated test to mimic GUI functionalities|2025-06-27T09:10:50.283000Z|f17ee76551", "title": "Automated test to mimic GUI functionalities", "section": "issues", "question": "I was just wondering if any way/packages can enable us to mock the uploading and processing of data as it happens on the NOMAD GUI?", "proposed_answer": "I am asking this from my experience working on this PR: Add LOBSTER workflow schema by naik-aakash · Pull Request #60 · nomad-coe/workflow-parsers -- https://github.com/nomad-coe/workflow-parsers/pull/60 , where one cannot easily add a test for the workflow that is being populated using the child archives approach.", "source_url": null, "timestamp": "2025-06-27T09:10:50.283000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 1, "a_start": 3, "a_end": 4}
{"id": "Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80|q19-a21-22", "thread_id": "Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80", "title": "Publishing DFT calculations from RSPt", "section": "issues", "question": "2. The answer to this depends a little if you are working on an Oasis or the central NOMAD, and also what the desired \"search functionalities\" are (understanding that they will certainly be limited). Do you want to simply be able to query for RSPt calculations that were uploaded or something more than that?", "proposed_answer": "2. At the moment, if we understood it correctly, publishing from an OASIS is not possible, because ours is not connected to DOI, and transfer of data from OASIS to central NOMAD is not yet functioning, please correct me if we got it wrong. But that's what we've clarified so far that's why we moved to trying putting our data directly on the central NOMAD, because we need it published in June. About \"desired search functionalities\" the answer is all possible. I'm not sure I know all term correctly, but in the best case scenario would be great to be able to use some search tools from NOMAD except for search by text in a search field. If you have time, we could shortly explain it in a zoom/teams/discord call. It is difficult to explain via messages, cause I'm not sure if I'm using the right terminology.", "source_url": null, "timestamp": "2025-06-03T09:29:11.049000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 19, "a_start": 21, "a_end": 22}
{"id": "TOML parse error|2025-05-05T12:24:25.554000Z|d21caab30f|q7-a9-10", "thread_id": "TOML parse error|2025-05-05T12:24:25.554000Z|d21caab30f", "title": "TOML parse error", "section": "issues", "question": "This command returns: `error: unexpected argument 'uv' found`. Do you possibly mean the `-P` flag (`--upgrade-package`)?", "proposed_answer": "So far, this seems to have done the trick, the TOML error seems to be gone for now. I will keep an eye out for `uv` versions in our plugins, maybe we have a setting somewhere that we shouldn't.", "source_url": null, "timestamp": "2025-05-05T12:24:25.554000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 7, "a_start": 9, "a_end": 10}
{"id": "Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c|q24-a26-27", "thread_id": "Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c", "title": "Failing test in plugin", "section": "issues", "question": "I tried changing the unit back to *dimensionless* locally and it seemed to fix the error. Any ideas why?", "proposed_answer": "I don’t know exactly the reason, but you could try removing the unit attribute completely and check if the error persists", "source_url": null, "timestamp": "2025-04-14T08:48:46.049000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 24, "a_start": 26, "a_end": 27}
{"id": "Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c|q40-a42-43", "thread_id": "Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c", "title": "Failing test in plugin", "section": "issues", "question": "In the test data and test script, I should also change the test value. It is just a single float value at the moment, but the expected input in reality is a [3,3] tensor. I intend to define something like, [n11, n12, n13; n21, n22, n23; n31, n32, n33]. When parsed, this is how we would store the value anyway. Have you got any feedback to define it better or is assumption alright?", "proposed_answer": "Follow-up. I have changed the test value in the YAML file and the test py script to a tensor as expected from a magres file (refer here: https://github.com/FAIRmat-NFDI/nomad-schema-plugin-nmr/pull/1/commits/d632ab1f82af149a069127a528800be4051a304b). All tests have passed now. Thank you for your help.", "source_url": null, "timestamp": "2025-04-14T08:48:46.049000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 40, "a_start": 42, "a_end": 43}
{"id": "questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173|q0-a2-3", "thread_id": "questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173", "title": "questions about nomad-distro-dev", "section": "issues", "question": "I am trying to setup a new nomad-distro-dev according to the readme. When I try to spin up docker I get a conflict, probably due to my other nomad installation. Do I really have to delete the other container? Then it will delete my test uploads, wont it?", "proposed_answer": "[+] Running 1/4 ✔ Network nomad-distro-dev_default Created 0.0s ⠋ Container nomad_mongo Creating 0.0s ⠋ Container nomad_rabbitmq Creating 0.0s ⠋ Container nomad_elastic Creating 0.0s Error response from daemon: Conflict. The container name \"/nomad_elastic\" is already in use by container \"972f2ad34a79d8798a5639026d7253bfdd8ca9eb4179b2278cca5ad2fcb0c890\". You have to remove (or rename) that container to be able to reuse that name.```", "source_url": null, "timestamp": "2025-01-16T11:42:39.899000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 0, "a_start": 2, "a_end": 3}
{"id": "questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173|q22-a24-25", "thread_id": "questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173", "title": "questions about nomad-distro-dev", "section": "issues", "question": "Ahmed, how about adding this note to the documentation?", "proposed_answer": "I added it last friday - https://github.com/FAIRmat-NFDI/nomad-distro-dev/pull/32", "source_url": null, "timestamp": "2025-01-16T11:42:39.899000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 22, "a_start": 24, "a_end": 25}
{"id": "Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4|q16-a18-19", "thread_id": "Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4", "title": "Can't get dev installation to work", "section": "issues", "question": "Well, it's not too urgent. Is your fork up to date and one I could also try for testing?", "proposed_answer": "I think it should be up to date now: https://github.com/hampusnasstrom/nomad-distro-dev", "source_url": null, "timestamp": "2024-12-16T09:34:40.791000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 16, "a_start": 18, "a_end": 19}
{"id": "dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db|q11-a13-14", "thread_id": "dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db", "title": "dynamic appearance of app widgets", "section": "issues", "question": "But the filters will still be relevant from one \"group\" of widgets to the next, so I dont think parallel apps is what we need in this case?", "proposed_answer": "I think I get the idea. How would you prefer the switching between the different dashborads to happen, and would swithincg the dashboard affect anything else, e.g. would it toggle on/off some filters? Would you also use this feature if it was available? I would want to get an idea of how relevant this feature is for users.", "source_url": null, "timestamp": "2024-09-19T10:21:05.589000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 11, "a_start": 13, "a_end": 14}
{"id": "Working with HDF5Datasets|2024-09-10T11:11:53.012000Z|8b84a352a0|q23-a25-26", "thread_id": "Working with HDF5Datasets|2024-09-10T11:11:53.012000Z|8b84a352a0", "title": "Working with HDF5Datasets", "section": "issues", "question": "sure that could also be a possibility, joe can you pls note this in the issue?", "proposed_answer": "https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2129", "source_url": null, "timestamp": "2024-09-10T11:11:53.012000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 23, "a_start": 25, "a_end": 26}
{"id": "set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851|q11-a13-14", "thread_id": "set default display unit for arrays read from file|2024-08-05T10:01:34.812000Z|9fbe062851", "title": "set default display unit for arrays read from file", "section": "issues", "question": "Did you open any issue that I can create a MR?", "proposed_answer": "[here](https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2087)", "source_url": null, "timestamp": "2024-08-05T10:01:34.812000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 11, "a_start": 13, "a_end": 14}
{"id": "Standard way of writing `m_proxy_value`|2024-04-10T17:08:30.668000Z|169773661f|q10-a17-18", "thread_id": "Standard way of writing `m_proxy_value`|2024-04-10T17:08:30.668000Z|169773661f", "title": "Standard way of writing `m_proxy_value`", "section": "issues", "question": "From what I am reading here, I could imagine a ReferenceEditQuantity that is already using the form with `/` so that the normalisation does not have to add it later on. But how big of an issue is this?", "proposed_answer": "here's the link to the issue on gitlab: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1992", "source_url": null, "timestamp": "2024-04-10T17:08:30.668000Z", "method": "discord_thread_heuristic", "score": 0.7, "q_line": 10, "a_start": 17, "a_end": 18}
{"id": "Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67|q19-a24-25", "thread_id": "Adding plugin to `nomad-distro-dev` fails|2025-06-13T08:52:00.065000Z|97d00eca67", "title": "Adding plugin to `nomad-distro-dev` fails", "section": "issues", "question": "> Is this similar to the 'nomad-parser-plugins-atomistic' vs 'atomisticparsers' issue?", "proposed_answer": "to have to start this up again. `uv sync` completed successfully once, but when I tried to run `nomad parse`, I got a new (similar) error:", "source_url": null, "timestamp": "2025-06-13T08:52:00.065000Z", "method": "discord_thread_heuristic", "score": 0.69, "q_line": 19, "a_start": 24, "a_end": 25}
{"id": "questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173|q5-a10-11", "thread_id": "questions about nomad-distro-dev|2025-01-16T11:42:39.899000Z|0bfbcd7173", "title": "questions about nomad-distro-dev", "section": "issues", "question": "Next question, the `uv add packages/nomad-parser-plugins-electronic` command does not work. I have added multiple packages, and now when I do the `uv add ..` it always complains about some other packages missing in `tool.uv.sources` do you have to immediately run the uv add command after running each single `git submodule add ..`?", "proposed_answer": "Ah yes, it doesn't support adding multiple submodules at the same time followed by `uv add`. If you're using `uv add`, you'll have to run it after adding each submodule individually.", "source_url": null, "timestamp": "2025-01-16T11:42:39.899000Z", "method": "discord_thread_heuristic", "score": 0.69, "q_line": 5, "a_start": 10, "a_end": 11}
{"id": "Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a|q5-a6-7", "thread_id": "Issue Starting Nomad Server: Connection Problems with Services|2025-01-11T15:49:58.163000Z|2a28391b7a", "title": "Issue Starting Nomad Server: Connection Problems with Services", "section": "issues", "question": "When I execute the uv run poe start command, I receive an error indicating a \"connection refused\" to Elasticsearch. I have attached the Errore.txt file with the full error message for more details. Here is what I have already tried:", "proposed_answer": "1. Verified that Elasticsearch is running using docker ps and responds to http://localhost:9200.", "source_url": null, "timestamp": "2025-01-11T15:49:58.163000Z", "method": "discord_thread_heuristic", "score": 0.69, "q_line": 5, "a_start": 6, "a_end": 7}
{"id": "Uploads to NOMAD never finish processing|2024-10-21T08:04:29.181000Z|ffe6f2bd9f|q21-a25-27", "thread_id": "Uploads to NOMAD never finish processing|2024-10-21T08:04:29.181000Z|ffe6f2bd9f", "title": "Uploads to NOMAD never finish processing", "section": "issues", "question": "is going to put in a fix, which makes the Gromacs2024 a bit more clear, and avoid some of this error-causing processing, so that we somewhat ease the situation.", "proposed_answer": "Alright.. Could you comment about this also into this issue: https://discord.com/channels// I now reset and reprocessed upload `U__GD1aNQ4KbKmOsHKOtjA`. Could you check if it looks any better now? I don't have access to it.", "source_url": null, "timestamp": "2024-10-21T08:04:29.181000Z", "method": "discord_thread_heuristic", "score": 0.69, "q_line": 21, "a_start": 25, "a_end": 27}
{"id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q77-a80-81", "thread_id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93", "title": "FHIaims parsing issues", "section": "issues", "question": "you mentioned that it is working on the central deployment right?", "proposed_answer": "fix here https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2536", "source_url": null, "timestamp": "2025-07-24T12:22:12.739000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 77, "a_start": 80, "a_end": 81}
{"id": "Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3|q5-a8-9", "thread_id": "Solve `mongo_user_group` warning in the application startup|2025-07-16T15:57:41.003000Z|8703d64ac3", "title": "Solve `mongo_user_group` warning in the application startup", "section": "issues", "question": "I don't know about those mongoDB warnings (also not getting them locally), maybe you could comment on this?", "proposed_answer": "Here you are: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/snippets/181", "source_url": null, "timestamp": "2025-07-16T15:57:41.003000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 5, "a_start": 8, "a_end": 9}
{"id": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271|q47-a57-58", "thread_id": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271", "title": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage", "section": "issues", "question": "Could you maybe delete or reset the stuck uploads listed above (p0e-3HI3S9KRRuj4nlJV6g, i--gEvRjQiO0Zcly-CZ9vA, qmxnv-8LQxGkAp8ax-Do0A, iJ65x2AJTbeUi9-QOhH4Mw, HlLtWCYtQ1ClTJi5WQvbJQ, SYNGBFqBQcm6r8bnmSNtNA, g51pcc7SRny8AQIyDcHNUw, W7_BDGRrQzKRpJGbkWkFoA), such that I can delete them?", "proposed_answer": "Indeed it looks like I cannot process these uploads even when I assign significant resources for them specifically. I tried now also `qmxnv-8LQxGkAp8ax-Do0A`, but still getting stuck.", "source_url": null, "timestamp": "2025-05-16T07:42:17.401000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 47, "a_start": 57, "a_end": 58}
{"id": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271|q58-a66-67", "thread_id": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271", "title": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage", "section": "issues", "question": "We need to figure out what is happening with the processing and where is it getting stuck. do you have somone from Area C that could take on this task? what is your smallest upload that does not get processed? And how large is it roughly in terms of disk space?", "proposed_answer": "Note that the fixes are enabled only in our `develop` deployment, which contains nightly updates for our development purposes. So if you want to reprocess something yourself, you need to go to: http://nomad-lab.eu/prod/v1/develop/gui", "source_url": null, "timestamp": "2025-05-16T07:42:17.401000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 58, "a_start": 66, "a_end": 67}
{"id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q3-a33-34", "thread_id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b", "title": "Unexpected error in the research app", "section": "issues", "question": "I've encountered the same issue with this plugin. The debug screen appears to reference internal functions.. could this be an internal bug?", "proposed_answer": "The fix has now been merged to the `develop` branch (https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2380).", "source_url": null, "timestamp": "2025-02-28T07:57:57.339000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 3, "a_start": 33, "a_end": 34}
{"id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q46-a131-132", "thread_id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b", "title": "Unexpected error in the research app", "section": "issues", "question": "Now we have a repository with the content presented in the image. Now the questions are two: where we have to install an external plugin as the one presented in the issue and how to update the repository to prevent the bug?", "proposed_answer": "I am using that same use case (headless server for nomad-distro-dev), and added a note to the readme about the required port forwardings: https://github.com/FAIRmat-NFDI/nomad-distro-dev/blob/21a1566fc1d8ced3757d2d2b60ecea64f24f4b58/README.md?plain=1#L311", "source_url": null, "timestamp": "2025-02-28T07:57:57.339000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 46, "a_start": 131, "a_end": 132}
{"id": "Setup error on \"nomad-distro-dev\"|2025-01-08T14:54:17.574000Z|58cb8ee208|q123-a126-127", "thread_id": "Setup error on \"nomad-distro-dev\"|2025-01-08T14:54:17.574000Z|58cb8ee208", "title": "Setup error on \"nomad-distro-dev\"", "section": "issues", "question": "That error usually means the API server is not reachable for the frontend. But based on the logs it seems like the server runs without issues. Is localhost:8000 accessible?", "proposed_answer": "this works, but running uv run poe start afterwards stll throws :", "source_url": null, "timestamp": "2025-01-08T14:54:17.574000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 123, "a_start": 126, "a_end": 127}
{"id": "Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4|q7-a15-16", "thread_id": "Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4", "title": "Can't get dev installation to work", "section": "issues", "question": "Hrmm, strange. What do you have in your `nomad.yaml`?", "proposed_answer": "Assuming you don't have any data you want to keep you could try a `docker compose down --volumes` and then `docker compose up -d`. That should solve the resource already exists I think. But I'm not sure that's your issue here. Unfortunately Ahmed is on vacation.", "source_url": null, "timestamp": "2024-12-16T09:34:40.791000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 7, "a_start": 15, "a_end": 16}
{"id": "In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c|q4-a7-8", "thread_id": "In and ELN lane, the rendering of API response in json is overlapping with fields.|2024-11-07T15:03:16.142000Z|ed0ccae27c", "title": "In and ELN lane, the rendering of API response in json is overlapping with fields.", "section": "issues", "question": "I'm not entirely sure what could be wrong in the second image. Can you maybe send me that JSON file and also link/message the definition for the `entity reference` definition in your schema?", "proposed_answer": "When you have time, you can test the fix for the overlap problem here: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2204", "source_url": null, "timestamp": "2024-11-07T15:03:16.142000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 4, "a_start": 7, "a_end": 8}
{"id": "Problem with SectionProxy|2024-10-14T14:24:47.620000Z|b15d624100|q10-a13-14", "thread_id": "Problem with SectionProxy|2024-10-14T14:24:47.620000Z|b15d624100", "title": "Problem with SectionProxy", "section": "issues", "question": "Any ideas what I am missing out here?", "proposed_answer": "But that's what I exactly don't want to /can not do and why I want to use the SectionProxy. Otherwise I could use a normal Reference.🤔", "source_url": null, "timestamp": "2024-10-14T14:24:47.620000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 10, "a_start": 13, "a_end": 14}
{"id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q56-a66-67", "thread_id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8", "title": "The new oasis-template doesn't works", "section": "issues", "question": "What browser do you use ?", "proposed_answer": "The problem is that we do not reproduce your error and we are able to use your image", "source_url": null, "timestamp": "2024-10-04T13:34:29.120000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 56, "a_start": 66, "a_end": 67}
{"id": "dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db|q4-a7-8", "thread_id": "dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db", "title": "dynamic appearance of app widgets", "section": "issues", "question": "this looks really good. Is this already part of a MR?", "proposed_answer": "https://nomad-lab.eu/prod/rae/gui/search", "source_url": null, "timestamp": "2024-09-19T10:21:05.589000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 4, "a_start": 7, "a_end": 8}
{"id": "Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7|q9-a12-13", "thread_id": "Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7", "title": "Publishing an entry from Oasis to Central NOMAD", "section": "issues", "question": "2. So, in the case of the second option above, we would have to upload the data with YAML schema attached, is it correct ?", "proposed_answer": "I added some more information to your docs. It is not yet perfect. You can see it already on our develop deployment here: <https://nomad-lab.eu/prod/v1/develop/docs/explanation/oasis.html>. These docs will move to the official site next week.", "source_url": null, "timestamp": "2024-08-29T07:38:20.591000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 9, "a_start": 12, "a_end": 13}
{"id": "Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765|q59-a60-61", "thread_id": "Problem when `docker compose up -d` an Oasis|2024-08-02T08:23:26.436000Z|32f9d1c765", "title": "Problem when `docker compose up -d` an Oasis", "section": "issues", "question": "ok i found error thank you but how did you get this error? Did you just run the docker-compose file ?", "proposed_answer": "`docker logs nomad_oasis_app`", "source_url": null, "timestamp": "2024-08-02T08:23:26.436000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 59, "a_start": 60, "a_end": 61}
{"id": "Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8|q53-a56-57", "thread_id": "Weird GUI data info|2024-03-01T12:53:07.703000Z|30c3c3a9e8", "title": "Weird GUI data info", "section": "issues", "question": "Ok, when there is the time, I will revert the last change and instead add a possibility to have a custom label on both the quantities and subsections. I guess this should also satisfy ?", "proposed_answer": "Issue: https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1915", "source_url": null, "timestamp": "2024-03-01T12:53:07.703000Z", "method": "discord_thread_heuristic", "score": 0.68, "q_line": 53, "a_start": 56, "a_end": 57}
{"id": "np.nan and None in nomad client|2025-04-24T13:53:33.907000Z|b15340cffb|q3-a9-10", "thread_id": "np.nan and None in nomad client|2025-04-24T13:53:33.907000Z|b15340cffb", "title": "np.nan and None in nomad client", "section": "issues", "question": "I was told that a related np.nan problem has been discussed here (https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/2128). Can there also be a solution for the ArchiveQuery?", "proposed_answer": "Then call `m_from_dict`, reference code can be seen as attached.", "source_url": null, "timestamp": "2025-04-24T13:53:33.907000Z", "method": "discord_thread_heuristic", "score": 0.67, "q_line": 3, "a_start": 9, "a_end": 10}
{"id": "Problem with `nomad-distro-dev`|2024-10-24T15:21:28.021000Z|3f0f258a83|q0-a7-8", "thread_id": "Problem with `nomad-distro-dev`|2024-10-24T15:21:28.021000Z|3f0f258a83", "title": "Problem with `nomad-distro-dev`", "section": "issues", "question": "When I run `nomad parse <input_file>` or try to update the environment with `uv run poe setup`, I get the following error:", "proposed_answer": "a problem with Python 3.12 and the package `typing-extensions`. Some people have reported similar issues here: https://github.com/Azure/azure-sdk-for-python/issues/33442", "source_url": null, "timestamp": "2024-10-24T15:21:28.021000Z", "method": "discord_thread_heuristic", "score": 0.67, "q_line": 0, "a_start": 7, "a_end": 8}
{"id": "Uploading process continues forever!|2024-05-22T12:30:19.255000Z|9e206ccef9|q5-a7-8", "thread_id": "Uploading process continues forever!|2024-05-22T12:30:19.255000Z|9e206ccef9", "title": "Uploading process continues forever!", "section": "issues", "question": "Isn't it a bug? Or?", "proposed_answer": "shell command `nomad admin uploads stop mRGT2zAoRbGTmDeYiZZzDQ --kill` from container does not work. `mRGT2zAoRbGTmDeYiZZzDQ` is the upload_id.", "source_url": null, "timestamp": "2024-05-22T12:30:19.255000Z", "method": "discord_thread_heuristic", "score": 0.67, "q_line": 5, "a_start": 7, "a_end": 8}
{"id": "GPAW Parser Error: unsupported operand type(s) for *: 'Quantity' and 'NoneType'|2025-07-23T11:36:11.817000Z|911dc4eef0|q4-a8-9", "thread_id": "GPAW Parser Error: unsupported operand type(s) for *: 'Quantity' and 'NoneType'|2025-07-23T11:36:11.817000Z|911dc4eef0", "title": "GPAW Parser Error: unsupported operand type(s) for *: 'Quantity' and 'NoneType'", "section": "issues", "question": "```Could someone from the GPAW parser team take a look?", "proposed_answer": "https://nomad-lab.eu/prod/v1/gui/user/uploads/upload/id/5Hw04xz0RAm28GIZtSYS2w", "source_url": null, "timestamp": "2025-07-23T11:36:11.817000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 4, "a_start": 8, "a_end": 9}
{"id": "nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52|q41-a42-43", "thread_id": "nomad-oasis can not stablely be started|2025-07-17T08:58:19.106000Z|3151dcdf52", "title": "nomad-oasis can not stablely be started", "section": "issues", "question": "I don't fully understand how you deployed your Oasis right now (this looks like a Windows Terminal to me), maybe you could tell me more about it? Did you use a custom `api_port` other than the default )? Did you setup HTTPS (if so you're likely to get a 301 redirect https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/main/configs/nginx_https.conf#L15)?", "proposed_answer": "OK, let me ask you some questions. so in the app container you have a nginx proxy, right? my question is: is it possible we can specify our own http server either using reverse proxy or directly specifing? Currently I use reverse proxy forward the request to your nginx proxy..", "source_url": null, "timestamp": "2025-07-17T08:58:19.106000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 41, "a_start": 42, "a_end": 43}
{"id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q75-a76-77", "thread_id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab", "title": "Create multiple entry from parser or new entry in normalizer", "section": "issues", "question": "If I'm getting everything correctly, the file path provided to `archive.m_context.process_updated_raw_file(self, path: str, allow_modify: bool)` will be treated as another mainfile, thus creating a new EntryArchive. In addition, this function could be call in either parser and normalizer. The only downside is that nothing will happen if it's ran in client context, so it's hard to test, is that true?", "proposed_answer": "in nomad you can not set to public and still edit the data you can share with others and groups and you can publish it (but then it cant be changed anymore)", "source_url": null, "timestamp": "2025-06-14T07:31:20.113000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 75, "a_start": 76, "a_end": 77}
{"id": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007|q40-a44-46", "thread_id": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12|2025-06-06T14:58:44.249000Z|0e5db1e007", "title": "NOMAD Oasis v1.3.16 - Failed docker image build due to removed package 'distutils' in Python3.12", "section": "issues", "question": "With that way it'd possible to add new static files, and have a new URL for your impressum. But I suppose there should be a link to the impressum from the home page of the app, is that correct?", "proposed_answer": "new variable under meta config for `footer_links` that could be used to add additional links to the home page: https://nomad-lab.eu/prod/v1/develop/docs/reference/config.html#meta", "source_url": null, "timestamp": "2025-06-06T14:58:44.249000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 40, "a_start": 44, "a_end": 46}
{"id": "Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80|q4-a5-6", "thread_id": "Publishing DFT calculations from RSPt|2025-06-03T09:29:11.049000Z|34ffedfa80", "title": "Publishing DFT calculations from RSPt", "section": "issues", "question": "post. Indeed there is no development yet for a RSPt parser, however I will make a note of this code for our future development. Can you give me a rough idea of the scope and user base of this code?", "proposed_answer": "For your upcoming publication. You can still upload your calculations to nomad now, and utilize the base features (overarching metadata like authors, references, etc). You could even create a small eln entry to annotate your uploads with further metadata or descriptions.", "source_url": null, "timestamp": "2025-06-03T09:29:11.049000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 4, "a_start": 5, "a_end": 6}
{"id": "Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c|q53-a54-55", "thread_id": "Failing test in plugin|2025-04-14T08:48:46.049000Z|3daf285b7c", "title": "Failing test in plugin", "section": "issues", "question": "May I first ask which version of `nomad-simulations` are you using, and how urgent is this?", "proposed_answer": "**Update on the current state of affairs:**", "source_url": null, "timestamp": "2025-04-14T08:48:46.049000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 53, "a_start": 54, "a_end": 55}
{"id": "Error 500 when creating a new upload|2025-04-07T07:48:07.283000Z|f79556187d|q13-a17-18", "thread_id": "Error 500 when creating a new upload|2025-04-07T07:48:07.283000Z|f79556187d", "title": "Error 500 when creating a new upload", "section": "issues", "question": "Ok, when it comes to solve the conflict merge in the `uv.lock` file, how should I handle this ?", "proposed_answer": "So, the IT person answered me and sent me the logs of the Docker image for the NOMAD Oasis. I attach it here but I am not really familiar with it. Anyway, we tried to use the Oasis at around 9am on the 07/04/25 but I don't see any log concerning an issue in the logs around that date", "source_url": null, "timestamp": "2025-04-07T07:48:07.283000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 13, "a_start": 17, "a_end": 18}
{"id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q46-a55-56", "thread_id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4", "title": "distro-dev not running on latest develop", "section": "issues", "question": "eh, okay. Can I change the wheel version uv runs somehow?", "proposed_answer": "I am getting the same errors in our pipelines. Another option would be to update setuptools (see https://github.com/pypa/wheel/blob/main/README.rst), but I am not sure if that breaks many other things", "source_url": null, "timestamp": "2025-04-01T12:49:10.385000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 46, "a_start": 55, "a_end": 56}
{"id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q93-a102-103", "thread_id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4", "title": "distro-dev not running on latest develop", "section": "issues", "question": "This is a bit ridiculous. How about they test something like this with a dev release before breaking a fundamental thing like `wheel` for everyone?", "proposed_answer": "This should fix it but until the next release is out I guess the solution is to use build constraints - https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/merge_requests/2426", "source_url": null, "timestamp": "2025-04-01T12:49:10.385000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 93, "a_start": 102, "a_end": 103}
{"id": "docker problem when starting local nomad|2025-03-24T13:38:27.783000Z|4ba9059016|q2-a3-4", "thread_id": "docker problem when starting local nomad|2025-03-24T13:38:27.783000Z|4ba9059016", "title": "docker problem when starting local nomad", "section": "issues", "question": "There is no `_placeholder_quantity` anywhere in the code. The same versions (nomad-lab + pynxtools) run fine on another pc. It seems like something of the older version remained in the docker container, however `docker compose down + docker compose up` was not enough to fix the issue. What am I missing?", "proposed_answer": "Update: fresh installation of nomad seems to work. I am unsure if I did something wrong while updating previous installation, or if there is an issue with nomad", "source_url": null, "timestamp": "2025-03-24T13:38:27.783000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 2, "a_start": 3, "a_end": 4}
{"id": "Oasis crashes when uploading multiple NeXus .hdf5 files|2025-02-13T15:36:13.213000Z|31f7b310d4|q0-a1-2", "thread_id": "Oasis crashes when uploading multiple NeXus .hdf5 files|2025-02-13T15:36:13.213000Z|31f7b310d4", "title": "Oasis crashes when uploading multiple NeXus .hdf5 files", "section": "issues", "question": "We are currently trying to upload our data backlog to our local Oasis. The files are all converted to the NeXus format and correctly parsed. However, after rough 100-200 uploaded files, the server PC freezes and needs to be restarted manually. We noticed that during uploading the swap memory size gradually increases until it is full and the server freezes. This only happens when uploading nexus files and happens also with the example ellipsometry .nxs file (when uploaded multiple times under different names). Has anybody seen something similar or advice on how to handle this?", "proposed_answer": "Given that you use a local oasis, providing a little more context would be good to enable at all a search for answers to your observations. These are guiding questions:", "source_url": null, "timestamp": "2025-02-13T15:36:13.213000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 0, "a_start": 1, "a_end": 2}
{"id": "DEV Deployment stalls on upload|2025-01-21T10:41:48.261000Z|07f5df7e4e|q1-a14-15", "thread_id": "DEV Deployment stalls on upload|2025-01-21T10:41:48.261000Z|07f5df7e4e", "title": "DEV Deployment stalls on upload", "section": "issues", "question": "Can you tell a bit more? Do you mean the `develop` deployment (https://nomad-lab.eu/prod/v1/develop/gui) or your local dev installation?", "proposed_answer": "Typically when an upload hangs it is because of resources: the processing e.g. runs out of memory or crashes in some other unexpected reason. If possible, this should then be debugged locally. Also the `develop`-deployment is not really equitpped with very beefy machines, so trying to process anything very large there can be problematic.", "source_url": null, "timestamp": "2025-01-21T10:41:48.261000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 1, "a_start": 14, "a_end": 15}
{"id": "Is the shape of a Quantity preserved when overwriting?|2024-12-05T14:28:17.339000Z|f69823772f|q12-a13-14", "thread_id": "Is the shape of a Quantity preserved when overwriting?|2024-12-05T14:28:17.339000Z|f69823772f", "title": "Is the shape of a Quantity preserved when overwriting?", "section": "issues", "question": "Did you try to overwrite with `shape=[]`? maybe that would overwrite the shape. If you don't set one, I guess the original shape is still valid?", "proposed_answer": "Mh. I did not try that yet, but that would make some sense I guess. 👍", "source_url": null, "timestamp": "2024-12-05T14:28:17.339000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 12, "a_start": 13, "a_end": 14}
{"id": "Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd|q5-a6-7", "thread_id": "Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd", "title": "Uploads are not displayed, despite being in the volumes folder", "section": "issues", "question": "then this was a misunderstanding from my side. Is there a chance to keep the upload_id, because of references?", "proposed_answer": "Restoring data with the same `upload_id` (or other ids like `entry_id`) is quite tricky.", "source_url": null, "timestamp": "2024-10-09T19:25:25.125000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 5, "a_start": 6, "a_end": 7}
{"id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q52-a53-54", "thread_id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459", "title": "Adapting existing Oasis to new plugin mechanism", "section": "issues", "question": "But this is not the scalable approach we want for more users. What is it now that it special in your image? Is it just the button?", "proposed_answer": "But we will be removing the final image built step from `nomad-FAIR` though, so `nomad-FAIR` in the future will just release a python package, deployments, image releases etc will happen on `nomad-distro`", "source_url": null, "timestamp": "2024-10-01T12:39:26.289000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 52, "a_start": 53, "a_end": 54}
{"id": "dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db|q0-a1-2", "thread_id": "dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db", "title": "dynamic appearance of app widgets", "section": "issues", "question": "Would it somehow be possible to decide if a widget appears in an app depending if it is populated by any data points given the current set of filters? So e.g. to make empty widgets invisible? Maybe has an idea if this could be possible?", "proposed_answer": "A possible alternative (more straight forward) would be to have parallel apps with more locked filters? I was planning this for the solar cell ones. Then, I would day that the app navigation menu would need some update..", "source_url": null, "timestamp": "2024-09-19T10:21:05.589000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 0, "a_start": 1, "a_end": 2}
{"id": "Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7|q43-a44-45", "thread_id": "Publishing an entry from Oasis to Central NOMAD|2024-08-29T07:38:20.591000Z|c039d98db7", "title": "Publishing an entry from Oasis to Central NOMAD", "section": "issues", "question": "I am really interested in your workflow but have to study your explanation a bit further. Do you know if there is any chance to get the schemas out of the GUI using the \"<>\"-buttons saying \"show section data as JSON\"?", "proposed_answer": "Would you be available for a discussion in a zoom-call?", "source_url": null, "timestamp": "2024-08-29T07:38:20.591000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 43, "a_start": 44, "a_end": 45}
{"id": "Problems with data types after updating to NOMAD develop branch|2024-07-08T09:06:49.792000Z|923b00310a|q6-a7-8", "thread_id": "Problems with data types after updating to NOMAD develop branch|2024-07-08T09:06:49.792000Z|923b00310a", "title": "Problems with data types after updating to NOMAD develop branch", "section": "issues", "question": "This is due to the new metainfo datatypes merged recently. And yes they are less forgiving now. Auto converting strings into numbers is really a stretch. This conversion from string to float is really the parsers job. whats your opinion on this?", "proposed_answer": "The type system internally tries to convert the input data into the target type. In this particular case, when a string \"20\" is given, the type instance tries to check the following: \"20\" == str(numpy.float64(\"20\")), this results actual check to be something like \"20\" == \"\" due to the inexact nature of floats.", "source_url": null, "timestamp": "2024-07-08T09:06:49.792000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 6, "a_start": 7, "a_end": 8}
{"id": "TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference|2024-06-18T13:26:34.564000Z|d04b45f526|q15-a16-17", "thread_id": "TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference|2024-06-18T13:26:34.564000Z|d04b45f526", "title": "TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference", "section": "issues", "question": "Question though: Do we need `if self.archive.m_context:` in the new implemention? No right, we can just do a direct setting of the arrays into the quantities that are specified as HDF5Dataset?", "proposed_answer": "somehow did not get any notication but in the develop branch HDF5Dataset is already used . It is simply that the simulationworkflow schema submodule is not updated I will try to update", "source_url": null, "timestamp": "2024-06-18T13:26:34.564000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 15, "a_start": 16, "a_end": 17}
{"id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57|q26-a27-28", "thread_id": "HDF5 in NOMAD|2024-05-27T13:17:35.844000Z|5d2d5ecf57", "title": "HDF5 in NOMAD", "section": "issues", "question": "So for the 1st one, if I make an ELN field, and the user edits that ELN field, will the data in this hdf5 be written automatically? Does the Quantity object know the path I initially gave when creating the schema? Or is it that whenever we write or read these Quantities this path has to be specfied?", "proposed_answer": "the first does not do any writing. it is simply a glorified string. if you want to write the data in an auxialiary archive h5 file use 2. then the path is determined by the archive path to the quantity.", "source_url": null, "timestamp": "2024-05-27T13:17:35.844000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 26, "a_start": 27, "a_end": 28}
{"id": "Cache Pub Chem Queries|2024-04-16T12:43:43.375000Z|9bcd1deaf9|q18-a22-23", "thread_id": "Cache Pub Chem Queries|2024-04-16T12:43:43.375000Z|9bcd1deaf9", "title": "Cache Pub Chem Queries", "section": "issues", "question": "I missed this! Are you done already?", "proposed_answer": "There is a gitlan issue now and the discussion can continue there: <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1981>", "source_url": null, "timestamp": "2024-04-16T12:43:43.375000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 18, "a_start": 22, "a_end": 23}
{"id": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c|q49-a50-51", "thread_id": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis|2024-03-14T16:47:18.591000Z|c1766fe61c", "title": "Unable to pull `jboss/keycloak` when running `docker compose pull` on my NOMAD Oasis", "section": "issues", "question": "I removed the plugin in the meantime. Should run the command `docker compose run north python -m jupyterhub upgrade` again or the otherone you just posted: `jupyterhub upgrade-db`?", "proposed_answer": "`upgrade-db` is the right command to upgrade the, well, db", "source_url": null, "timestamp": "2024-03-14T16:47:18.591000Z", "method": "discord_thread_heuristic", "score": 0.66, "q_line": 49, "a_start": 50, "a_end": 51}
{"id": "Docker Issues with first start up of NOMAD Oasis|2025-04-23T21:33:05.024000Z|151e21ac26|q5-a7-8", "thread_id": "Docker Issues with first start up of NOMAD Oasis|2025-04-23T21:33:05.024000Z|151e21ac26", "title": "Docker Issues with first start up of NOMAD Oasis", "section": "issues", "question": "10 GB should in theory be enough but it depends on how many cores you are running. I think it should still work on startup though. Is this on the new apple silicon architecture? I just tried it on a Linux distribution (Fedora) and it started fine. Maybe can give it a try as I think he runs a Mac?", "proposed_answer": "I can also recommend creating your own repository, and removing the set of plugins that you don't need from the toml table. That should decrease the memory requirements.", "source_url": null, "timestamp": "2025-04-23T21:33:05.024000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 5, "a_start": 7, "a_end": 8}
{"id": "Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94|q119-a121-122", "thread_id": "Adding h5view of data file to entry overview|2025-04-03T09:05:50.630000Z|7c8e9fab94", "title": "Adding h5view of data file to entry overview", "section": "issues", "question": "I am sorry.. Where do we see this? Do we go the DATA tab?", "proposed_answer": "Well the is the pynxtools schema which does not use any H5WebAnnotation. It rather uses a Nexus Card solution which automatically renders this card on the overview page for Nexus files.. I do not know how this works exactly", "source_url": null, "timestamp": "2025-04-03T09:05:50.630000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 119, "a_start": 121, "a_end": 122}
{"id": "Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5|q15-a17-18", "thread_id": "Protected Oasis - Restrict GUI/API Access only for Login-User|2025-02-28T08:48:27.056000Z|a03af89fd5", "title": "Protected Oasis - Restrict GUI/API Access only for Login-User", "section": "issues", "question": "```to restrict the access on demand when deploying an oasis, if one need this extended authorization protection. What do you think?", "proposed_answer": "In principle what you suggest is doable, but we would need to investigate quite a lot of time to properly check and ensure that this flag correctly protects all of the endpoints etc.", "source_url": null, "timestamp": "2025-02-28T08:48:27.056000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 15, "a_start": 17, "a_end": 18}
{"id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c|q23-a25-26", "thread_id": "Unexpected dependency version on NOMAD develop|2025-01-24T11:39:53.165000Z|eb7ff63e5c", "title": "Unexpected dependency version on NOMAD develop", "section": "issues", "question": "- Does this also apply eventually to staging and production when the versions are bumped up?", "proposed_answer": "- So, if I update a plugin dependency, I need to also change the requirements file on nomad-FAIR?", "source_url": null, "timestamp": "2025-01-24T11:39:53.165000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 23, "a_start": 25, "a_end": 26}
{"id": "New plugin does not show up in a nomad-distro-dev|2025-01-21T16:08:15.456000Z|9f9ff6cfc7|q6-a8-9", "thread_id": "New plugin does not show up in a nomad-distro-dev|2025-01-21T16:08:15.456000Z|9f9ff6cfc7", "title": "New plugin does not show up in a nomad-distro-dev", "section": "issues", "question": "- if yes, did you scroll down the drop-down menu opening when you click \"create from schema\" and checking if your classes are in the \"other\" category?", "proposed_answer": "the repository is linked to my first posted message. For the first question I haven't created a parser just a schema with a normalize function that parse automatically a csv file and in the git repository I was able to create the json file normalize.archive. My class is not in any category so I cannot find it.", "source_url": null, "timestamp": "2025-01-21T16:08:15.456000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 6, "a_start": 8, "a_end": 9}
{"id": "When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702|q8-a10-11", "thread_id": "When registering a new account, the confirmation email does not arrive|2025-01-16T10:34:58.468000Z|13e1d3c702", "title": "When registering a new account, the confirmation email does not arrive", "section": "issues", "question": "So there still is an adress that needs verification? What is the adress?", "proposed_answer": "I manually validated you email. You should be able to use your account now.", "source_url": null, "timestamp": "2025-01-16T10:34:58.468000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 8, "a_start": 10, "a_end": 11}
{"id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q7-a9-10", "thread_id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9", "title": "Uploads are getting directly stuck in nomad-distro-dev", "section": "issues", "question": "If I adjust the log level, where do I get more information logged. I cannot access the Logs tab, because I have no entry in the upload. Is this logged directly in the terminal?", "proposed_answer": "This sounds like the worker has not correctly started. I would look at the docker logs and also check that the worker service has started correctly", "source_url": null, "timestamp": "2025-01-09T14:23:39.252000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 7, "a_start": 9, "a_end": 10}
{"id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q28-a33-34", "thread_id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d", "title": "Memory error when I build my container", "section": "issues", "question": "I can reproduce the issue, so it works if you comment out your own plugin?", "proposed_answer": "https://github.com/PauloGitHB/nomad-distro-template.git", "source_url": null, "timestamp": "2024-11-15T09:44:36.042000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 28, "a_start": 33, "a_end": 34}
{"id": "NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520|q9-a14-15", "thread_id": "NOMAD Oasis Requests Takes a lot of Time from Institute Intranet|2024-10-29T16:31:40.296000Z|5e184d2520", "title": "NOMAD Oasis Requests Takes a lot of Time from Institute Intranet", "section": "issues", "question": "I think to be really used in lab environment, this is a must because it is very common that lab computers mostly do not have access to public internet. I do not know how other institutes that using Oasis handles this. Any comment about IKZ maybe?", "proposed_answer": "So I do tests in offline local development environment (**client and server is same machine**) with clear memory/disk cache and hard reload. The result is that it works without any delays. There is only two requests that are done to public internet (https://unpkg.com/pace-js https://cdn.jsdelivr.net/npm/mathjax and failure of this does not effect the loading time or any functionality.", "source_url": null, "timestamp": "2024-10-29T16:31:40.296000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 9, "a_start": 14, "a_end": 15}
{"id": "Uploads to NOMAD never finish processing|2024-10-21T08:04:29.181000Z|ffe6f2bd9f|q27-a29-30", "thread_id": "Uploads to NOMAD never finish processing|2024-10-21T08:04:29.181000Z|ffe6f2bd9f", "title": "Uploads to NOMAD never finish processing", "section": "issues", "question": "Yes, it looks very good. All entries have been processed successfully. Thank you very much. Could you also reset and reprocess `hhKM5SazSY2ZA0ExJQRAaA`?", "proposed_answer": "Great, that one is now also reprocessed, please check. As you suggest, I think we will need a mechanism where the users can forcefully reset the processing state after some timeout.", "source_url": null, "timestamp": "2024-10-21T08:04:29.181000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 27, "a_start": 29, "a_end": 30}
{"id": "Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd|q16-a18-19", "thread_id": "Uploads are not displayed, despite being in the volumes folder|2024-10-09T19:25:25.125000Z|56d29d51fd", "title": "Uploads are not displayed, despite being in the volumes folder", "section": "issues", "question": "okay, but I think I can at least have a look at POST /uploads/bundle to move data from my real Oasis to my local test Oasis, right?", "proposed_answer": "Not sure, but I think I should be able to set it up.", "source_url": null, "timestamp": "2024-10-09T19:25:25.125000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 16, "a_start": 18, "a_end": 19}
{"id": "Failed publishing for some uploads|2024-09-19T13:10:32.860000Z|1a37e4d2da|q5-a10-11", "thread_id": "Failed publishing for some uploads|2024-09-19T13:10:32.860000Z|1a37e4d2da", "title": "Failed publishing for some uploads", "section": "issues", "question": "The develop distribution is using atomisticparser v1.0.1, was the upload processed using that version of the parser?", "proposed_answer": "https://nomad-lab.eu/prod/v1/develop/gui/user/uploads/upload/id/jHOZwqYFTpy3Ucc5YlSb0w", "source_url": null, "timestamp": "2024-09-19T13:10:32.860000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 5, "a_start": 10, "a_end": 11}
{"id": "More a question than an issue: data curation.|2024-09-18T08:20:59.171000Z|31ca795375|q15-a20-21", "thread_id": "More a question than an issue: data curation.|2024-09-18T08:20:59.171000Z|31ca795375", "title": "More a question than an issue: data curation.", "section": "issues", "question": "> * If you change affiliation, can you update it in your datasets?", "proposed_answer": "Author affiliation can be changed, since it is not tied to an upload/dataset. We are currently having some problems with the form that allows you to do it, this is discussed here: https://discord.com/channels///threads/", "source_url": null, "timestamp": "2024-09-18T08:20:59.171000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 15, "a_start": 20, "a_end": 21}
{"id": "inconsistencies between develop deployment and local development|2024-09-17T10:20:47.494000Z|5c8eda6b6f|q19-a21-22", "thread_id": "inconsistencies between develop deployment and local development|2024-09-17T10:20:47.494000Z|5c8eda6b6f", "title": "inconsistencies between develop deployment and local development", "section": "issues", "question": "I wonder though if it is already useful to announce these changes (even if they are subject to further change) in some way other then individually talking with each dev/domain expert who is trying to update things and runs into problems. E.g., you could make a post in #software-updates and then follow up on it when things change again, ?", "proposed_answer": "I think announcing major or breaking changes in #software-updates makes sense. But we also need to figure out a way how to create a filtered release log for the documentation that you can consult about deprecations, breaking changes, etc. I will put the topic on the agenda for the #plugins-taskforce meetings.", "source_url": null, "timestamp": "2024-09-17T10:20:47.494000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 19, "a_start": 21, "a_end": 22}
{"id": "TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference|2024-06-18T13:26:34.564000Z|d04b45f526|q4-a5-6", "thread_id": "TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference|2024-06-18T13:26:34.564000Z|d04b45f526", "title": "TypeError: can not serialize 'numpy.ndarray' object with HDF5Reference", "section": "issues", "question": "when I went to check the error I was assuming that the HDF5 utility needed to be updated, but then I saw at some point the Gromacs parser was partially updated (at least it was changed from my initial implementation), so I assumed you had updated it, but I guess it was somewhere in the middle of your implementation. Thanks, I will update now", "proposed_answer": "sometimes I forget to update nomad submodules whenever I do changes in the parsers, Recently I have to do this for the runschema", "source_url": null, "timestamp": "2024-06-18T13:26:34.564000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 4, "a_start": 5, "a_end": 6}
{"id": "improve usability of term widgets with many entries|2024-04-26T09:36:34.943000Z|bb02bfd4ac|q0-a2-3", "thread_id": "improve usability of term widgets with many entries|2024-04-26T09:36:34.943000Z|bb02bfd4ac", "title": "improve usability of term widgets with many entries", "section": "issues", "question": "I would like a way to explore the terms in a term widget that are not showing as the top x items. Currently the only way is to extend the size of the widget, which quickly becomes impractial. Is it possible to add a scrollbar to explore the less frequent appearing terms?", "proposed_answer": "How difficult would it be? Should I open an issue for this? Thanks!", "source_url": null, "timestamp": "2024-04-26T09:36:34.943000Z", "method": "discord_thread_heuristic", "score": 0.64, "q_line": 0, "a_start": 2, "a_end": 3}
{"id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d|q21-a25-26", "thread_id": "Memory error when I build my container|2024-11-15T09:44:36.042000Z|a465f5e68d", "title": "Memory error when I build my container", "section": "issues", "question": "can you try commenting out the plugins you might not need in the pyproject.toml file? the error seems valid though", "proposed_answer": "https://github.com/PauloGitHB/nomad-oasis.git", "source_url": null, "timestamp": "2024-11-15T09:44:36.042000Z", "method": "discord_thread_heuristic", "score": 0.63, "q_line": 21, "a_start": 25, "a_end": 26}
{"id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93|q101-a113-114", "thread_id": "FHIaims parsing issues|2025-07-24T12:22:12.739000Z|baa3e0af93", "title": "FHIaims parsing issues", "section": "issues", "question": "This seems to work, but I wonder if this is correct? However, if I update some dependencies I need to somehow update the uv.lock first, right? (this happens somehow automatically on the github, but I can't see through github workflow that much to understand what is exactly going on)? How do I do that step locally?", "proposed_answer": "So for example `nomad-lab[parsing, infrastructure]==1.3.16` works, but `nomad-lab[parsing, infrastructure] @ git+https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR.git does not even though it is the same version (will crash on start due to missing /opt/venv/lib/python3.12/site-packages/nomad/app/static/gui). So I assume I'm still missing some extra steps...", "source_url": null, "timestamp": "2025-07-24T12:22:12.739000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 101, "a_start": 113, "a_end": 114}
{"id": "failing plugin install-and-test workflow in github|2025-06-23T06:21:45.025000Z|ac78218ece|q0-a3-4", "thread_id": "failing plugin install-and-test workflow in github|2025-06-23T06:21:45.025000Z|ac78218ece", "title": "failing plugin install-and-test workflow in github", "section": "issues", "question": "suddenly the automated testing pipeline for my plugin fails. Any Idea why suddenly mongoengine is not installed any more during the install dependencies step?", "proposed_answer": "For future reference, I was using an outdated workflow file and still had old python requirement, which only started to fail now. using the updated workflow `actions.yml` from the cookiecutter-nomad-plugin repo fixed the issues.", "source_url": null, "timestamp": "2025-06-23T06:21:45.025000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 0, "a_start": 3, "a_end": 4}
{"id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab|q51-a57-59", "thread_id": "Create multiple entry from parser or new entry in normalizer|2025-06-14T07:31:20.113000Z|d0601e3fab", "title": "Create multiple entry from parser or new entry in normalizer", "section": "issues", "question": "Yes I'm working with spinbot! How did you get that?", "proposed_answer": "```This is required as precursor is an `entity` (NOMAD base sections definition), it shouldn't be created every time a new mainfile is parsed. Instead, should be added to the new entries as a reference. This cannot be done in`is_mainfile` as the function is not able to read other `EntryArchives`. Perhaps this gives you a bit of inspiration for that? https://github.com/FAIRmat-NFDI/nomad-perovskite-solar-cells-database/blob/main/src/perovskite_solar_cell_database/composition.py#L481", "source_url": null, "timestamp": "2025-06-14T07:31:20.113000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 51, "a_start": 57, "a_end": 59}
{"id": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271|q8-a66-67", "thread_id": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage|2025-05-16T07:42:17.401000Z|1f5ecee271", "title": "Multiple MD simulation Uploads to nomad-lab are stuck at the processing stage", "section": "issues", "question": "I have now reset and reprocessed the uploads you mentioned. Could you check if they are fine now?", "proposed_answer": "Note that the fixes are enabled only in our `develop` deployment, which contains nightly updates for our development purposes. So if you want to reprocess something yourself, you need to go to: http://nomad-lab.eu/prod/v1/develop/gui", "source_url": null, "timestamp": "2025-05-16T07:42:17.401000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 8, "a_start": 66, "a_end": 67}
{"id": "testing workflows|2025-05-06T15:35:10.871000Z|5bc10667ca|q2-a22-23", "thread_id": "testing workflows|2025-05-06T15:35:10.871000Z|5bc10667ca", "title": "testing workflows", "section": "issues", "question": "I saw a testing.py script from Chema in the neb repo, which looks like it is possible to resolve the references from the workflow through the terminal, but I am not sure how to use it, or how to do something similar. Looking at the testing_AlCo2S4.py file in [tests](https://github.com/FAIRmat-NFDI/nomad-neb-workflows/blob/develop/tests/test_AlCo2S4.py), is there a way to run this and inspect an output for the script? Maybe has some tips?", "proposed_answer": "```m_def: nomad_neb_workflows.schema_packages.neb.NEBWorkflow inputs: - name: Input structure for initial image section: '../upload/archive/mainfile/neb0.traj#/run/0/'```", "source_url": null, "timestamp": "2025-05-06T15:35:10.871000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 2, "a_start": 22, "a_end": 23}
{"id": "testing workflows|2025-05-06T15:35:10.871000Z|5bc10667ca|q23-a50-51", "thread_id": "testing workflows|2025-05-06T15:35:10.871000Z|5bc10667ca", "title": "testing workflows", "section": "issues", "question": "in the example you posted you are not referencing the mainfile, but an entry, and this is the name I give it in the parsing step before, right?", "proposed_answer": "i think the correct reference format is `/uploads/{upload_id}/archive/{entry_id}#/run/0/`. The first one `../upload/archive/mainfile/{mainfile}#/run/0` should work . It is possible that the mainfile is not correct, maybe have a look at the raw files under .volumes", "source_url": null, "timestamp": "2025-05-06T15:35:10.871000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 23, "a_start": 50, "a_end": 51}
{"id": "Plugin-Development: How to self-reference a class with ReferenceEditQuantity|2025-04-10T10:20:37.111000Z|50175a2089|q1-a19-20", "thread_id": "Plugin-Development: How to self-reference a class with ReferenceEditQuantity|2025-04-10T10:20:37.111000Z|50175a2089", "title": "Plugin-Development: How to self-reference a class with ReferenceEditQuantity", "section": "issues", "question": "During plugin development I stumbled about a question, and maybe somebody knows a solution for that: How to self-reference a class with ReferenceEditQuantity?", "proposed_answer": "You can see an example of this in the https://nomad-lab.eu/prod/v1/gui/analyze/metainfo/nomad/section_definitions which is a `CompositeSystem`", "source_url": null, "timestamp": "2025-04-10T10:20:37.111000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 1, "a_start": 19, "a_end": 20}
{"id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4|q10-a55-56", "thread_id": "distro-dev not running on latest develop|2025-04-01T12:49:10.385000Z|877264acb4", "title": "distro-dev not running on latest develop", "section": "issues", "question": "That is still not working for me 😦 Do you have any idea what could be wrong ?", "proposed_answer": "I am getting the same errors in our pipelines. Another option would be to update setuptools (see https://github.com/pypa/wheel/blob/main/README.rst), but I am not sure if that breaks many other things", "source_url": null, "timestamp": "2025-04-01T12:49:10.385000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 10, "a_start": 55, "a_end": 56}
{"id": "ArchiveQuery raising errors in nomad-lab v1.3.15|2025-03-26T15:19:44.146000Z|c02881671c|q26-a29-30", "thread_id": "ArchiveQuery raising errors in nomad-lab v1.3.15|2025-03-26T15:19:44.146000Z|c02881671c", "title": "ArchiveQuery raising errors in nomad-lab v1.3.15", "section": "issues", "question": "I think it was running with the older nomad-lab version after the 18th but I'm not sure. It could just be that no user on that Oasis used it between Tuesday the 18th and Friday the 21st when I updated nomad-lab. Maybe can say if he was running it succesfully in that time?", "proposed_answer": "It looks to me that `ArchiveQuery` tries to login as admin to retrieve data. To do this you have to have the admin password which is not available in any jupyterhub deployment. TLDR: ArchiveQuery should work without admin priviledges.", "source_url": null, "timestamp": "2025-03-26T15:19:44.146000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 26, "a_start": 29, "a_end": 30}
{"id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b|q97-a131-132", "thread_id": "Unexpected error in the research app|2025-02-28T07:57:57.339000Z|c363903d4b", "title": "Unexpected error in the research app", "section": "issues", "question": "We obtained these warnings in the worker at the beginning but successively the application actually started. So what could the problem be?", "proposed_answer": "I am using that same use case (headless server for nomad-distro-dev), and added a note to the readme about the required port forwardings: https://github.com/FAIRmat-NFDI/nomad-distro-dev/blob/21a1566fc1d8ced3757d2d2b60ecea64f24f4b58/README.md?plain=1#L311", "source_url": null, "timestamp": "2025-02-28T07:57:57.339000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 97, "a_start": 131, "a_end": 132}
{"id": "Oasis crashes when uploading multiple NeXus .hdf5 files|2025-02-13T15:36:13.213000Z|31f7b310d4|q4-a7-8", "thread_id": "Oasis crashes when uploading multiple NeXus .hdf5 files|2025-02-13T15:36:13.213000Z|31f7b310d4", "title": "Oasis crashes when uploading multiple NeXus .hdf5 files", "section": "issues", "question": "* Which version of Oasis and plugins are you using?", "proposed_answer": "for your response, i will try to provide a more detailed account of our setup and tests:", "source_url": null, "timestamp": "2025-02-13T15:36:13.213000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 4, "a_start": 7, "a_end": 8}
{"id": "Using HDF5|2025-01-09T17:23:00.110000Z|d6f076aaf8|q14-a24-25", "thread_id": "Using HDF5|2025-01-09T17:23:00.110000Z|d6f076aaf8", "title": "Using HDF5", "section": "issues", "question": "can you help here?", "proposed_answer": "p.parse('tests/data/example.out', a, logger=logging.getLogger())", "source_url": null, "timestamp": "2025-01-09T17:23:00.110000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 14, "a_start": 24, "a_end": 25}
{"id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9|q25-a44-45", "thread_id": "Uploads are getting directly stuck in nomad-distro-dev|2025-01-09T14:23:39.252000Z|7e44c678b9", "title": "Uploads are getting directly stuck in nomad-distro-dev", "section": "issues", "question": "that's odd, can you try running git describe locally?", "proposed_answer": "but this time, open the repo inside the container and run `git submodule update --init --recurisve` inside the container", "source_url": null, "timestamp": "2025-01-09T14:23:39.252000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 25, "a_start": 44, "a_end": 45}
{"id": "Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4|q22-a25-26", "thread_id": "Can't get dev installation to work|2024-12-16T09:34:40.791000Z|6dcdc7e5e4", "title": "Can't get dev installation to work", "section": "issues", "question": "Did you try `docker compose down --volumes` or do you have data in your local that you don't want to loose?", "proposed_answer": "this was also now on a computer that never run nomad before", "source_url": null, "timestamp": "2024-12-16T09:34:40.791000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 22, "a_start": 25, "a_end": 26}
{"id": "Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff|q8-a47-48", "thread_id": "Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff", "title": "Permission denied when creating new Jupyter Notebook in uploads folder", "section": "issues", "question": "Maybe somewhere there is the problem?", "proposed_answer": "Then it looks like your Jupyter image is somehow incorrectly using the root user. Is your Oasis based on [nomad-distro-template](https://github.com/FAIRmat-NFDI/nomad-distro-template)? Fekete: I see [in our Dockerfile](https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/39509dfe85640b8a1b25923a040668a8d92cdb98/Dockerfile#L203) that the user is set to $NB_UID in the Jupyter image. Does this default to 1000..?", "source_url": null, "timestamp": "2024-11-20T11:04:36.071000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 8, "a_start": 47, "a_end": 48}
{"id": "Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff|q28-a47-48", "thread_id": "Permission denied when creating new Jupyter Notebook in uploads folder|2024-11-20T11:04:36.071000Z|98457adeff", "title": "Permission denied when creating new Jupyter Notebook in uploads folder", "section": "issues", "question": "Any idea why this could be?", "proposed_answer": "Then it looks like your Jupyter image is somehow incorrectly using the root user. Is your Oasis based on [nomad-distro-template](https://github.com/FAIRmat-NFDI/nomad-distro-template)? Fekete: I see [in our Dockerfile](https://github.com/FAIRmat-NFDI/nomad-distro-template/blob/39509dfe85640b8a1b25923a040668a8d92cdb98/Dockerfile#L203) that the user is set to $NB_UID in the Jupyter image. Does this default to 1000..?", "source_url": null, "timestamp": "2024-11-20T11:04:36.071000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 28, "a_start": 47, "a_end": 48}
{"id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8|q89-a101-102", "thread_id": "The new oasis-template doesn't works|2024-10-04T13:34:29.120000Z|34f77729b8", "title": "The new oasis-template doesn't works", "section": "issues", "question": "Why not localhost?", "proposed_answer": "set this issue as \"resolved\" 👍", "source_url": null, "timestamp": "2024-10-04T13:34:29.120000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 89, "a_start": 101, "a_end": 102}
{"id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459|q57-a76-77", "thread_id": "Adapting existing Oasis to new plugin mechanism|2024-10-01T12:39:26.289000Z|224cd18459", "title": "Adapting existing Oasis to new plugin mechanism", "section": "issues", "question": "any reason why you don't create a MR to add that to nomad?", "proposed_answer": "I now commented on <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1610> and <https://gitlab.mpcdf.mpg.de/nomad-lab/nomad-FAIR/-/issues/1955>. The relevant people here, should be part of the respective issues. My idea would be to work on the \"button\" and on the action you do (e.g. creating entries, updating entries) together and to finally document such a use-base with a how-to in the documentation.", "source_url": null, "timestamp": "2024-10-01T12:39:26.289000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 57, "a_start": 76, "a_end": 77}
{"id": "dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db|q40-a43-44", "thread_id": "dynamic appearance of app widgets|2024-09-19T10:21:05.589000Z|d5eaa8d3db", "title": "dynamic appearance of app widgets", "section": "issues", "question": "I understand. do you think it is possible to have the result of a JMESPath query in the axis or widget title? e.g. reactants[0].name ?", "proposed_answer": "I would also not want to try and make the title dynamic: that is also another rabbithole that might never end.", "source_url": null, "timestamp": "2024-09-19T10:21:05.589000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 40, "a_start": 43, "a_end": 44}
{"id": "Failure to parse simulations from the newest GROMACS version|2024-09-03T11:41:26.426000Z|6495d4932f|q2-a5-6", "thread_id": "Failure to parse simulations from the newest GROMACS version|2024-09-03T11:41:26.426000Z|6495d4932f", "title": "Failure to parse simulations from the newest GROMACS version", "section": "issues", "question": "the report! Indeed that sounds like a problem we could solve with a library update. you know the GROMACS parser quite well: do you think we could simply try to update MDAnalysis to solve this?", "proposed_answer": "Then I will open an issue to investigate upgrading MDAnalysis, as this could cause issues downstream", "source_url": null, "timestamp": "2024-09-03T11:41:26.426000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 2, "a_start": 5, "a_end": 6}
{"id": "Tutorial 12 and nomad-as-a-data-management-framework-tutorial- example - few problems.|2024-08-26T06:49:00.797000Z|edcd5aaead|q5-a16-17", "thread_id": "Tutorial 12 and nomad-as-a-data-management-framework-tutorial- example - few problems.|2024-08-26T06:49:00.797000Z|edcd5aaead", "title": "Tutorial 12 and nomad-as-a-data-management-framework-tutorial- example - few problems.", "section": "issues", "question": "\"ModuleNotFoundError: No module named 'runschema'\" for example. So it could come back again to this *schema *package or function being deprecated?", "proposed_answer": "The main problems are now fixed on our \"alpha\" deployment: https://nomad-lab.eu/prod/v1/develop/gui/about/information If you want, you can try out the notebook there.", "source_url": null, "timestamp": "2024-08-26T06:49:00.797000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 5, "a_start": 16, "a_end": 17}
{"id": "Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.|2024-04-02T14:04:10.722000Z|8eb0f03f1d|q8-a36-37", "thread_id": "Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.|2024-04-02T14:04:10.722000Z|8eb0f03f1d", "title": "Cannot access uploads, because of error: Could not validate credentials. The given token is invalid.", "section": "issues", "question": "By the way, this are the logs of the app container for the Validation Error. Maybe this gives some more insights?", "proposed_answer": "`The token is not yet valid (iat)` sounds like a time(zone) issue. E.g. our user management and your oasis not running in sync. Your laptop being behind. When you open a new tab, you start a new instance of the gui and it will get a new (potentially out of time) token. So every time you open a new tab there is the potential for this to happen. Could you check your os time settings?", "source_url": null, "timestamp": "2024-04-02T14:04:10.722000Z", "method": "discord_thread_heuristic", "score": 0.62, "q_line": 8, "a_start": 36, "a_end": 37}
