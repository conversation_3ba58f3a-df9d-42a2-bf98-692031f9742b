# NOMAD Cheat Sheet for writing Custom Yaml Schemas

This Cheat Sheet features the basic ELN quantity types as well as basic schema structure to write custom yaml data schema for NOMAD. Use it to copy, paste and modify the snippets you would like to reuse in your own schema. Upload the schema file to explore the different qunatity types.

## Usage

Upload the schema file `nomad_cheat_sheet.data.archive.json` and the corresponding data file `nomad_cheat_sheet.data.archive.json` to a [NOMAD instance](https://nomad-lab.eu/prod/v1/staging/gui/about/information) and inspect the data entry.