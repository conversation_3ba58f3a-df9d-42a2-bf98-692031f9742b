## Multiformat data reader for Raman spectroscopy data
This is example data for writing a new data reader based on the [`MultiFormatReader`](https://github.com/FAIRmat-NFDI/pynxtools/blob/master/src/pynxtools/dataconverter/readers/multi/reader.py) from scratch. A how-to guide to implement this reader is available [here](https://fairmat-nfdi.github.io/pynxtools/how-tos/use-multi-format-reader.html)

## This file types are currently supported:
- Witec Alpha Raman microscope - via ELN file (.yaml) and .txt data file


## Examples
Examples can be found in the example folder in the root of this repository.