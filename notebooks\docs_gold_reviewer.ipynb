{"cells": [{"cell_type": "markdown", "id": "4a0b835c", "metadata": {}, "source": ["# NOMAD RAG — Gold Q&A Reviewer (v3)\n", "\n", "A cleaner UI for curating candidate Q&As into your gold set.\n", "\n", "**Improvements over v2**\n", "- Two-column layout (selector on the left, preview on the right) — no overlap.\n", "- Preview panel scrolls independently (won’t block the selector).\n", "- Adjustable preview length + \"Show full answers\" toggle.\n", "- Clear success banner with appended items.\n", "- Gold summary refreshes automatically.\n", "\n", "> Expected input: `eval/gold_review.csv` with columns: `question, proposed_answer, source_url, title, section, method, score, id`.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "2bcf24ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 69 candidates\n", "Loaded 0 existing gold entries from ../eval/gold_nomad.jsonl\n"]}], "source": ["import os, json, re\n", "from pathlib import Path\n", "import pandas as pd\n", "from IPython.display import display, Markdown, HTML\n", "import ipywidgets as W\n", "\n", "# ---- Paths (edit if needed) ----\n", "CANDIDATE_CSV = Path(\"../eval/gold_review.csv\")\n", "GOLD_JSONL    = Path(\"../eval/gold_nomad.jsonl\")\n", "GOLD_JSONL.parent.mkdir(parents=True, exist_ok=True)\n", "\n", "if not CANDIDATE_CSV.exists():\n", "    raise FileNotFoundError(f\"Candidate CSV not found: {CANDIDATE_CSV.resolve()}\")\n", "\n", "df_all = pd.read_csv(CANDIDATE_CSV)\n", "\n", "required = {\"question\",\"proposed_answer\",\"source_url\",\"title\",\"section\",\"method\",\"score\",\"id\"}\n", "missing = required - set(df_all.columns)\n", "if missing:\n", "    raise ValueError(f\"Candidate CSV missing columns: {missing}\")\n", "\n", "def load_gold(path: Path):\n", "    if not path.exists(): return []\n", "    with path.open(\"r\", encoding=\"utf-8\") as f:\n", "        return [json.loads(line) for line in f if line.strip()]\n", "\n", "gold_list = load_gold(GOLD_JSONL)\n", "\n", "def norm_question(q: str) -> str:\n", "    q = (q or \"\").lower().strip()\n", "    q = re.sub(r\"\\s+\", \" \", q)\n", "    q = re.sub(r\"[^\\w\\s\\?\\-]\", \"\", q)  # keep ? and -\n", "    return q\n", "\n", "existing_keys = {(norm_question(r.get(\"question\",\"\")), r.get(\"source_url\",\"\")) for r in gold_list}\n", "\n", "print(f\"Loaded {len(df_all)} candidates\")\n", "print(f\"Loaded {len(gold_list)} existing gold entries from {GOLD_JSONL}\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "e2f58e0b", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "694e969a2f1c453b8a133f3787075c25", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(FloatSlider(value=0.55, continuous_update=False, description='Min score', layout=Layout(width='…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "df34bbeb53084bc680ad562cfc7d36fa", "version_major": 2, "version_minor": 0}, "text/plain": ["GridBox(children=(VBox(children=(SelectMultiple(description='Select', layout=Layout(height='560px', overflow='…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "75ac302052d64c6ba5a1ae5d5bc44ec3", "version_major": 2, "version_minor": 0}, "text/plain": ["HTML(value='<hr/>')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cd6eea1a15cf41f88a451ea12c1991a0", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# --- LOCKED LEFT PANE UI (replace your layout/widgets cell with this) ---\n", "import ipywidgets as W\n", "from IPython.display import display, Markdown, HTML\n", "import re, json, pandas as pd\n", "\n", "# Filters\n", "min_score = <PERSON><PERSON>(value=0.55, min=0.0, max=1.0, step=0.05,\n", "                          description=\"Min score\", continuous_update=False,\n", "                          layout=W.Layout(width=\"320px\"))\n", "method_opts = [\"(any)\"] + sorted(df_all[\"method\"].dropna().unique().tolist())\n", "method_dd = W.Dropdown(options=method_opts, value=\"(any)\", description=\"Method\",\n", "                       layout=W.Layout(width=\"260px\"))\n", "search_box = W.Text(value=\"\", description=\"Search\",\n", "                    placeholder=\"text in question/answer/title/section\",\n", "                    layout=W.Layout(width=\"360px\"))\n", "refresh_btn = <PERSON><PERSON>(description=\"Refresh\", layout=<PERSON><PERSON>Layout(width=\"120px\"))\n", "top_bar = <PERSON><PERSON>([min_score, method_dd, search_box, refresh_btn])\n", "\n", "# LEFT: selector column (LOCKED width + its own scroll)\n", "select_box = <PERSON><PERSON>(\n", "    options=[], rows=24, description=\"Select\",\n", "    layout=W.Layout(width=\"520px\", height=\"560px\", overflow=\"auto\")\n", ")\n", "add_btn = <PERSON><PERSON>(description=\"Add to Gold\", button_style=\"success\",\n", "                   layout=W.Layout(width=\"160px\"))\n", "status_out = W.Output(layout=W.Layout(width=\"520px\"))\n", "\n", "left_col = W.<PERSON>(\n", "    [select_box, <PERSON><PERSON>([add_btn]), status_out],\n", "    layout=<PERSON><PERSON>Layout(\n", "        width=\"540px\", min_width=\"540px\", max_width=\"540px\",\n", "        overflow=\"hidden\"  # left column itself doesn't scroll horizontally\n", "    )\n", ")\n", "\n", "# RIGHT: preview column (independent scroll)\n", "preview_len = W.IntSlider(value=1200, min=200, max=6000, step=100,\n", "                          description=\"Preview chars\", readout=True,\n", "                          layout=W.Layout(width=\"360px\"))\n", "full_toggle = W.Checkbox(value=False, description=\"Show full answers\")\n", "preview_header = <PERSON><PERSON>([preview_len, full_toggle])\n", "\n", "preview_out = W.Output(\n", "    layout=W.Layout(border=\"1px solid #ddd\", padding=\"8px\",\n", "                    height=\"560px\", overflow=\"auto\", width=\"100%\")\n", ")\n", "right_col = <PERSON>.<PERSON>(\n", "    [preview_header, preview_out],\n", "    layout=W.Layout(min_width=\"600px\", overflow=\"hidden\")\n", ")\n", "\n", "# Use a GRID (prevents overlap)\n", "main_area = <PERSON><PERSON>(\n", "    children=[left_col, right_col],\n", "    layout=<PERSON><PERSON>Layout(\n", "        grid_template_columns=\"540px 1fr\",   # left fixed, right flexible\n", "        grid_gap=\"12px\",\n", "        width=\"100%\"\n", "    )\n", ")\n", "\n", "gold_summary_out = W.Output()\n", "\n", "display(top_bar, main_area, W.HTML(\"<hr/>\"), gold_summary_out)\n", "\n", "def format_option(row):\n", "    q_short = (row[\"question\"][:96] + \"…\") if len(row[\"question\"]) > 100 else row[\"question\"]\n", "    return f\"[{row['score']:.2f}] {row['method']}: {q_short}  —  ({row['title']} › {row['section']})\"\n", "\n", "def filtered_df():\n", "    df = df_all.copy()\n", "    df = df[df[\"score\"] >= float(min_score.value)]\n", "    if method_dd.value != \"(any)\":\n", "        df = df[df[\"method\"] == method_dd.value]\n", "    q = search_box.value.strip().lower()\n", "    if q:\n", "        mask = (\n", "            df[\"question\"].str.lower().str.contains(q, na=False) |\n", "            df[\"proposed_answer\"].str.lower().str.contains(q, na=False) |\n", "            df[\"title\"].str.lower().str.contains(q, na=False) |\n", "            df[\"section\"].str.lower().str.contains(q, na=False)\n", "        )\n", "        df = df[mask]\n", "    return df\n", "\n", "def apply_filter(_=None):\n", "    df = filtered_df()\n", "    select_box.options = [(format_option(row), row[\"id\"]) for _, row in df.iterrows()]\n", "    with status_out:\n", "        status_out.clear_output()\n", "        print(f\"Filtered candidates: {len(df)}\")\n", "    update_preview()\n", "\n", "def shorten(text: str, limit: int) -> str:\n", "    return text if len(text) <= limit else text[:limit] + \" …\"\n", "\n", "def update_preview(_=None):\n", "    df = filtered_df()\n", "    idx = {row[\"id\"]: row for _, row in df.iterrows()}\n", "    chosen = list(select_box.value)[:8]\n", "    with preview_out:\n", "        preview_out.clear_output()\n", "        if not chosen:\n", "            display(Markdown(\"> Select candidates on the left to preview…\"))\n", "            return\n", "        for cid in chosen:\n", "            row = idx.get(cid)\n", "            if row is None:\n", "                continue\n", "            ans = row[\"proposed_answer\"] if full_toggle.value else shorten(row[\"proposed_answer\"], int(preview_len.value))\n", "            html = f\"\"\"\n", "            <div style='border:1px solid #eee; padding:10px; margin:8px 0; border-radius:8px'>\n", "              <div style='font-weight:600'>Question:</div>\n", "              <div style='margin:4px 0 8px 0'>{row['question']}</div>\n", "              <div style='font-weight:600'>Proposed answer:</div>\n", "              <div style='margin:4px 0 8px 0'>{ans}</div>\n", "              <div style='margin-top:6px'>\n", "                <b>Source</b>: <a href='{row['source_url']}' target='_blank'>{row['title']} › {row['section']}</a>\n", "                &nbsp; | &nbsp; <i>{row['method']}</i> &nbsp; score={row['score']:.2f}\n", "              </div>\n", "            </div>\n", "            \"\"\"\n", "            display(HTML(html))\n", "\n", "def show_gold_summary():\n", "    if GOLD_JSONL.exists():\n", "        with GOLD_JSONL.open(\"r\", encoding=\"utf-8\") as f:\n", "            gold = [json.loads(line) for line in f if line.strip()]\n", "    else:\n", "        gold = []\n", "    with gold_summary_out:\n", "        gold_summary_out.clear_output()\n", "        display(Markdown(f\"**Gold entries:** {len(gold)}\"))\n", "        if gold:\n", "            gdf = pd.<PERSON><PERSON><PERSON>e(gold)\n", "            display(gdf[[\"question\",\"gold_urls\"]].head(10))\n", "\n", "def add_selected_to_gold(_):\n", "    df = filtered_df()\n", "    by_id = {row[\"id\"]: row for _, row in df.iterrows()}\n", "    picked_ids = list(select_box.value)\n", "    if not picked_ids:\n", "        with status_out:\n", "            status_out.clear_output()\n", "            display(Markdown(\"⚠️ **No candidates selected.**\"))\n", "        return\n", "\n", "    added, new_recs = [], []\n", "    for cid in picked_ids:\n", "        row = by_id.get(cid)\n", "        if row is None:\n", "            continue\n", "        key = (norm_question(row[\"question\"]), row[\"source_url\"])\n", "        if key in existing_keys:\n", "            continue\n", "        rec = {\n", "            \"question\": row[\"question\"],\n", "            \"gold_answer\": row[\"proposed_answer\"],\n", "            \"gold_urls\": [row[\"source_url\"]],\n", "            \"title\": row.get(\"title\",\"\"),\n", "            \"section\": row.get(\"section\",\"\"),\n", "            \"source_url\": row[\"source_url\"],\n", "            \"meta\": {\"method\": row.get(\"method\",\"\"), \"score\": float(row.get(\"score\",0)), \"id\": row.get(\"id\",\"\")}\n", "        }\n", "        new_recs.append(rec)\n", "        added.append(row[\"question\"])\n", "        existing_keys.add(key)\n", "\n", "    if new_recs:\n", "        with GOLD_JSONL.open(\"a\", encoding=\"utf-8\") as f:\n", "            for r in new_recs:\n", "                f.write(json.dumps(r, ensure_ascii=False) + \"\\n\")\n", "\n", "    with status_out:\n", "        status_out.clear_output()\n", "        if new_recs:\n", "            items = \"<br/>\".join(\"• \" + q for q in added[:6])\n", "            more = \"\" if len(added) <= 6 else f\"<br/>… and {len(added)-6} more\"\n", "            display(HTML(f\"<div style='background:#e9f7ef;border-left:6px solid #2ecc71;padding:10px'>\"\n", "                         f\"<b>Added {len(added)}</b> entries to <code>{GOLD_JSONL}</code>.\"\n", "                         f\"<div style='margin-top:6px'>{items}{more}</div></div>\"))\n", "        else:\n", "            display(HTML(\"<div style='background:#fdecea;border-left:6px solid #e74c3c;padding:10px'>Nothing added (all duplicates).</div>\"))\n", "    show_gold_summary()\n", "\n", "# wire events\n", "refresh_btn.on_click(apply_filter)\n", "select_box.observe(update_preview, names=\"value\")\n", "add_btn.on_click(add_selected_to_gold)\n", "preview_len.observe(update_preview, names=\"value\")\n", "full_toggle.observe(update_preview, names=\"value\")\n", "\n", "# render\n", "apply_filter()\n", "show_gold_summary()\n"]}, {"cell_type": "code", "execution_count": null, "id": "87e40020", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON> (nomad-ragbot)", "language": "python", "name": "nomad-ragbot"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}