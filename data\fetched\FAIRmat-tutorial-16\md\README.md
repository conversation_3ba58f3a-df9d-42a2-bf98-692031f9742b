# FAIRmat Tutorial 16: Getting started with NOMAD for materials-science research data management

**Date:** February 26, 2025  
**Time:** 13:00–16:00 CET  
**Speakers:** <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>  
**Location:** Online  
**Web:** <a href="https://events.fairmat-nfdi.eu/event/34/" target="_blank" rel="noopener noreferrer">https://events.fairmat-nfdi.eu/event/34/</a>  
**Slides and files:** Download <a href="https://github.com/FAIRmat-NFDI/FAIRmat-tutorial-16/raw/refs/heads/main/tutorial_16_materials.zip" target="_blank" rel="noopener noreferrer">here</a>

In **FAIRmat tutorial 16**, you'll learn how to use **NOMAD** to manage and organize your materials-science research data. Whether you are working with computational or experimental data, NOMAD's graphical user interface makes it easy to upload, share, explore, and analyze your data. We will also focus on using NOMAD to improve collaboration on your research projects and to ensure that your data align with the FAIR data principles (Findable, Accessible, Interoperable, and Reusable).

## What you'll do:

- 🌐 [**Explore NOMAD entries**](https://fairmat-nfdi.github.io/FAIRmat-tutorial-16/T16_2/T16_2_explore_data_records.html) and gain insights from the rich metadata structured in NOMAD's data model.
- 📥 [**Upload raw data files**](https://fairmat-nfdi.github.io/FAIRmat-tutorial-16/T16_3/T16_3_upload_raw_data_files.html) and inspect the extracted data structured in NOMAD’s data model.
- 🗂️ [**Create a dataset**](https://fairmat-nfdi.github.io/FAIRmat-tutorial-16/T16_5/T16_5_create_a_dataset.html) and simulate publishing it with a DOI.
- 📖 [**Document your research**](https://fairmat-nfdi.github.io/FAIRmat-tutorial-16/T16_6/T16_6_document_your_research.html) using NOMAD’s built-in ELN templates.  
- 🛠️ [**Create custom ELN templates**](https://fairmat-nfdi.github.io/FAIRmat-tutorial-16/T16_7/T16_7_create_custom_eln_templates.html) (demo).  
- 🛠️ [**Use the tabular parser**](https://fairmat-nfdi.github.io/FAIRmat-tutorial-16/T16_8/T16_8_write_a_custom_parser.html) for tabular data, such as `.csv` or `.xls` files (demo).  

## At the end of this tutorial, you will be able to:
- Efficiently **search, filter, and explore materials-science data** in NOMAD.  
- Manage, upload, and share your research data using NOMAD.  
- Publish your datasets with DOIs to ensure **accessibility and reusability**.  
- Use the **NOMAD ELN functionality** to document your research.  
- Understand the essentials of creating custom templates and parsers for your research data.  
