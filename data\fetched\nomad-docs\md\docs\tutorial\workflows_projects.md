# Managing workflows and projects

[NOMAD Tutorial Workflow](https://fairmat-nfdi.github.io/nomad-tutorial-workflows/latest){:target="\_blank"} provides a stand-alone tutorial on workflow and project management with NOMAD. It utilizes the `nomad-utility-workflows` Python module to lower the entry barrier for advanced NOMAD usage.

## What you will learn

- Organize and manage complex research workflows using NOMAD
- Integrate diverse data sources into a single, reproducible project
- Track data provenance and metadata
- Interface with the NOMAD repository programmatically for automation and high-throughput use

## Recommended tools and background (for efficiency and automation)

- **Terminal environment:**
  Install the workflow utility module via <PERSON><PERSON> (Linux/macOS) or PowerShell (Windows)

- **Basic Python knowledge:**
  Utilize workflow utility tools using provided Jupyter notebooks
