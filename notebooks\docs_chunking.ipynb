from pathlib import Path
from datetime import datetime, timezone
import re, json, math
import pandas as pd
from collections import Counter

# Adjust if needed
DOCS_ROOT = Path("/home/<USER>/llm-hackathon/nomad-docs")
OUT_DIR   = Path("/home/<USER>/llm-hackathon/data/docs")  # chunked outputs
BASE_URL  = "https://github.com/FAIRmat-NFDI/nomad-docs/blob/main"

OUT_DIR.mkdir(parents=True, exist_ok=True)

def slugify(s: str) -> str:
    s = (s or "").strip().lower()
    s = re.sub(r"[^a-z0-9]+", "-", s)
    s = re.sub(r"-+", "-", s).strip("-")
    return s or "untitled"

def file_mtime_utc_iso(path: Path) -> str:
    dt = datetime.fromtimestamp(path.stat().st_mtime, tz=timezone.utc)
    return dt.isoformat().replace("+00:00", "Z")

def list_md(root: Path):
    return sorted([p for p in root.rglob("*") if p.suffix.lower() in {".md", ".mdx"} and p.is_file()])


def read_markdown(path: Path) -> str:
    return path.read_text(encoding="utf-8", errors="ignore")

def split_markdown_by_headings(md: str):
    """
    Returns list of (section_title, section_text) using ATX headings.
    """
    lines = (md or "").splitlines()
    sections = []
    current_title, buf = "Introduction", []
    for line in lines:
        m = re.match(r"^(#{1,6})\s+(.*)$", line)
        if m:
            if buf:
                txt = "\n".join(buf).strip()
                if txt:
                    sections.append((current_title, txt))
                buf = []
            current_title = m.group(2).strip()
        else:
            buf.append(line)
    if buf:
        txt = "\n".join(buf).strip()
        if txt:
            sections.append((current_title, txt))
    return sections

md_files = list_md(DOCS_ROOT)
len(md_files), md_files[:5]


def tokenize_words(text: str):
    # Simple alphanumeric tokenizer; language-agnostic
    return re.findall(r"[A-Za-zÄÖÜäöüß0-9]+", text or "")

def split_sentences(text: str):
    # Basic sentence split heuristic for EN/DE (split on .!? followed by uppercase)
    parts = re.split(r"(?<=[.!?])\s+(?=[A-ZÄÖÜ])", (text or "").strip())
    return [p.strip() for p in parts if p.strip()] or [text.strip()]


def chunk_fixed(text: str, max_words: int = 400, overlap: int = 50):
    toks = tokenize_words(text)
    if not toks:
        return []
    chunks, i, N = [], 0, len(toks)
    while i < N:
        j = min(i + max_words, N)
        chunks.append(" ".join(toks[i:j]))
        if j == N:
            break
        i = max(0, j - overlap)
    return chunks

def chunk_sentence(text: str, target_words: int = 350):
    sents = split_sentences(text)
    chunks, buf, count = [], [], 0
    for s in sents:
        n = len(tokenize_words(s))
        if count + n > target_words and buf:
            chunks.append(" ".join(buf))
            buf, count = [], 0
        buf.append(s)
        count += n
    if buf:
        chunks.append(" ".join(buf))
    return chunks

def bow_vector(text: str) -> Counter:
    return Counter(tokenize_words(text.lower()))

def cosine_sim(a: Counter, b: Counter) -> float:
    if not a or not b:
        return 0.0
    inter = set(a) & set(b)
    num = sum(a[t]*b[t] for t in inter)
    da = math.sqrt(sum(v*v for v in a.values()))
    db = math.sqrt(sum(v*v for v in b.values()))
    if da == 0 or db == 0:
        return 0.0
    return num / (da * db)

def chunk_semantic(text: str, target_words: int = 350, min_sim: float = 0.25):
    sents = split_sentences(text)
    if not sents:
        return []
    chunks, buf, centroid, count = [], [], Counter(), 0
    for s in sents:
        sv = bow_vector(s)
        sim = cosine_sim(centroid, sv) if centroid else 1.0
        n = len(tokenize_words(s))
        if (sim < min_sim and buf) or (count + n > target_words and buf):
            chunks.append(" ".join(buf))
            buf, centroid, count = [], Counter(), 0
        buf.append(s)
        centroid.update(sv)
        count += n
    if buf:
        chunks.append(" ".join(buf))
    return chunks


def doc_title_from_md(md: str, path: Path) -> str:
    m = re.search(r"^#\s+(.*)$", md, flags=re.MULTILINE)
    return m.group(1).strip() if m else path.stem.replace("-", " ").title()

def write_jsonl(recs, path: Path):
    path.parent.mkdir(parents=True, exist_ok=True)
    with path.open("w", encoding="utf-8") as f:
        for r in recs:
            f.write(json.dumps(r, ensure_ascii=False) + "\n")

# Parameters (you can tweak)
FIX_MAX, FIX_OVERLAP     = 400, 50
SENT_TARGET              = 350
SEM_TARGET, SEM_MINSIM   = 350, 0.25

out_fixed    = OUT_DIR / "nomad_docs.fixed.jsonl"
out_sentence = OUT_DIR / "nomad_docs.sentence.jsonl"
out_semantic = OUT_DIR / "nomad_docs.semantic.jsonl"

fixed_rows, sentence_rows, semantic_rows = [], [], []

for p in md_files:
    md  = read_markdown(p)
    if not md.strip():
        continue
    title = doc_title_from_md(md, p)
    rel   = p.relative_to(DOCS_ROOT).as_posix()
    url   = f"{BASE_URL}/{rel}"
    ts    = file_mtime_utc_iso(p)
    sections = split_markdown_by_headings(md)

    for sec_title, sec_text in sections:
        # fixed
        for i, ch in enumerate(chunk_fixed(sec_text, max_words=FIX_MAX, overlap=FIX_OVERLAP)):
            fixed_rows.append({
                "id": f"docs:{slugify(title)}:{slugify(sec_title)}:fixed:{i}",
                "source": "docs",
                "title": title,
                "section": sec_title,
                "text": ch,
                "url": url,
                "timestamp": ts
            })
        # sentence
        for i, ch in enumerate(chunk_sentence(sec_text, target_words=SENT_TARGET)):
            sentence_rows.append({
                "id": f"docs:{slugify(title)}:{slugify(sec_title)}:sentence:{i}",
                "source": "docs",
                "title": title,
                "section": sec_title,
                "text": ch,
                "url": url,
                "timestamp": ts
            })
        # semantic
        for i, ch in enumerate(chunk_semantic(sec_text, target_words=SEM_TARGET, min_sim=SEM_MINSIM)):
            semantic_rows.append({
                "id": f"docs:{slugify(title)}:{slugify(sec_title)}:semantic:{i}",
                "source": "docs",
                "title": title,
                "section": sec_title,
                "text": ch,
                "url": url,
                "timestamp": ts
            })

write_jsonl(fixed_rows, out_fixed)
write_jsonl(sentence_rows, out_sentence)
write_jsonl(semantic_rows, out_semantic)

len(fixed_rows), len(sentence_rows), len(semantic_rows), out_fixed.as_posix(), out_sentence.as_posix(), out_semantic.as_posix()


def chunk_content(text: str):
        """
        Chunk the content into smaller pieces.

        Args:
            content: The content to chunk

        Returns:
            A list of content chunks
        """
        # Split the content into paragraphs
        paragraphs = [p for p in text.split("\n\n") if p.strip()]

        chunks = []
        current_chunk = ""
        section_heading = None
        subsection_heading = None

        for paragraph in paragraphs:
            # Check if this is a heading
            is_main_heading = paragraph.strip().startswith('# ')
            is_section_heading = paragraph.strip().startswith('## ')
            is_subsection_heading = paragraph.strip().startswith('### ')

            # If we have a new main heading or section heading, start a new chunk
            if is_main_heading or is_section_heading:
                # Save the current chunk if it's not empty and meets the minimum size
                if current_chunk and len(current_chunk) >= min_chunk_size:
                    chunks.append(current_chunk.strip())

                # Start a new chunk with this heading
                current_chunk = paragraph
                section_heading = paragraph
                subsection_heading = None
                continue

            # If we have a new subsection heading, keep it with the current section if possible
            if is_subsection_heading:
                subsection_heading = paragraph

                # If the current chunk is already large, start a new one
                if len(current_chunk) >= self.max_chunk_size * 0.7:
                    if current_chunk and len(current_chunk) >= self.min_chunk_size:
                        chunks.append(current_chunk.strip())

                    # Start a new chunk with the section heading and this subsection
                    if section_heading and section_heading != subsection_heading:
                        current_chunk = section_heading + "\n\n" + subsection_heading
                    else:
                        current_chunk = subsection_heading
                else:
                    # Add the subsection to the current chunk
                    if current_chunk:
                        current_chunk += "\n\n" + subsection_heading
                    else:
                        current_chunk = subsection_heading
                continue

            # If adding this paragraph would make the chunk too large, save the current chunk
            if len(current_chunk) + len(paragraph) > self.max_chunk_size and len(current_chunk) >= self.min_chunk_size:
                chunks.append(current_chunk.strip())

                # Start a new chunk with context from previous headings
                if subsection_heading:
                    # If we have a subsection, include both section and subsection headings
                    if section_heading and section_heading != subsection_heading:
                        current_chunk = section_heading + "\n\n" + subsection_heading + "\n\n"
                    else:
                        current_chunk = subsection_heading + "\n\n"
                elif section_heading:
                    # Otherwise just include the section heading
                    current_chunk = section_heading + "\n\n"
                else:
                    current_chunk = ""

            # Add the paragraph to the current chunk
            if current_chunk:
                current_chunk += "\n\n" + paragraph
            else:
                current_chunk = paragraph

        # Add the last chunk if it's not empty and meets the minimum size
        if current_chunk and len(current_chunk) >= self.min_chunk_size:
            chunks.append(current_chunk.strip())

        return chunks