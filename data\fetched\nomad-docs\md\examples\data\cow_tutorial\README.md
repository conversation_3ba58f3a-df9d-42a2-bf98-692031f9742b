# NOMAD as a Data Management Framework Tutorial

This upload provides a notebook as a tutorial that demonstrates how to use NOMAD
for managing custom data and file types. Based on a simple *Countries of the World*
dataset, it shows how to model the data in a schema, do parsing and normalization,
process data, access existing data with NOMAD's API for analysis, and how to
add visualization to your data.

- Click on the **FILES** tab and open `Tutorial.ipynb`.
- Start *jupyterlab*. If juypterlab is already running, please press stop and start a new one.
- Follow the notebook for the rest of the tutorial.
