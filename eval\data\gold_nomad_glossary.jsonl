{"question": "What does Annotation ¶ mean in NOMAD?", "gold_answer": "Annotations are part of data schemas and they describe aspects that are not directly defining the type or shape of data. They often allow to alter how certain data is managed, represented, or edited. See annotations in the schema documentation .", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#annotation"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#annotation", "title": "Glossary", "section": "Annotation ¶", "meta": {"type": "glossary", "term": "Annotation ¶"}}
{"question": "What does App ¶ mean in NOMAD?", "gold_answer": "Apps allow you to build customized user interfaces for specific research domains, making it easier to navigate and understand the data. This typically means that certain domain-specific properties are highlighted, different units may be used for physical properties, and specialized dashboards may be presented. This becomes crucial for NOMAD installations to be able to scale with data that contains a mixture of experiments and simulations, different techniques, and physical properties spanning different time and length scales.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#app"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#app", "title": "Glossary", "section": "App ¶", "meta": {"type": "glossary", "term": "App ¶"}}
{"question": "What does Archive ¶ mean in NOMAD?", "gold_answer": "NOMAD processes (parses and normalizes) all data. The entirety of all processed data is referred to as the Archive . Sometimes the term archive is used to refer to the processed data of a particular entry , e.g. \"the archive of that entry\".", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#archive"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#archive", "title": "Glossary", "section": "Archive ¶", "meta": {"type": "glossary", "term": "Archive ¶"}}
{"question": "What does Author ¶ mean in NOMAD?", "gold_answer": "An author is typically a natural person that has uploaded a piece of data into NOMAD and has authorship over it. Often authors are users , but not always. Therefore, we have to distinguish between authors and users.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#author"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#author", "title": "Glossary", "section": "Author ¶", "meta": {"type": "glossary", "term": "Author ¶"}}
{"question": "What does Dataset ¶ mean in NOMAD?", "gold_answer": "Users can organize entries into datasets . Datasets are not created automatically, don't confuse them with uploads . Datasets can be compared to albums, labels, or tags on other platforms. Datasets are used to reference a collection of data and users can get a DOI for their datasets.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#dataset"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#dataset", "title": "Glossary", "section": "Dataset ¶", "meta": {"type": "glossary", "term": "Dataset ¶"}}
{"question": "What does Deployment ¶ mean in NOMAD?", "gold_answer": "NOMAD Deployment refers to a live instance of a NOMAD distribution running on a machine. This machine can be a cloud-based virtual machine or a local computer.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#deployment"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#deployment", "title": "Glossary", "section": "Deployment ¶", "meta": {"type": "glossary", "term": "Deployment ¶"}}
{"question": "What does Distribution / distro ¶ mean in NOMAD?", "gold_answer": "NOMAD Distribution is a Git repository containing the configuration for instantiating a customized NOMAD instance. Distributions define the plugins that should be installed, the configurations files (e.g. nomad.yaml ) to use, CI pipeline steps for building final Docker images and a docker-compose.yaml file that can be used to launch the instance.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#distribution-distro"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#distribution-distro", "title": "Glossary", "section": "Distribution / distro ¶", "meta": {"type": "glossary", "term": "Distribution / distro ¶"}}
{"question": "What does ELN ¶ mean in NOMAD?", "gold_answer": "Electronic Lab Notebooks ( ELNs ) are a specific kind of entry in NOMAD. These entries can be edited in NOMAD, in contrast to entries that are created by uploading and processing data. ELNs offer form fields and other widgets to modify the contents of an entry. As all entries, ELNs are based on a schema ; how quantities are edited (e.g. which type of widget) can be controlled through annotations .", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#eln"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#eln", "title": "Glossary", "section": "ELN ¶", "meta": {"type": "glossary", "term": "ELN ¶"}}
{"question": "What does Entry ¶ mean in NOMAD?", "gold_answer": "Data in NOMAD is organized in entries (as in \"database entry \"). Entries have an entry id . Entries can be searched for and entries have individual pages on the NOMAD GUI. Entries are always associated with raw files , where one of these files is the mainfile . Raw files are processed to create the processed data (or the archive ) for an entry.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#entry"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#entry", "title": "Glossary", "section": "Entry ¶", "meta": {"type": "glossary", "term": "Entry ¶"}}
{"question": "What does Example upload ¶ mean in NOMAD?", "gold_answer": "Example uploads are pre-prepared uploads containing data that typically showcases certain features of a plugin. The contents of example uploads can be fixed, created programmatically or fetched from online sources. Example uploads can be instantiated by using the \"Example uploads\" -button in the \"Uploads\" -page of the GUI. Example uploads can be defined by creating an example upload plugin entry point .", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#example-upload"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#example-upload", "title": "Glossary", "section": "Example upload ¶", "meta": {"type": "glossary", "term": "Example upload ¶"}}
{"question": "What does Mainfile ¶ mean in NOMAD?", "gold_answer": "Each entry has one raw file that defines it. This is called the mainfile of that entry. Typically most, if not all, processed data of an entry is retrieved from that mainfile.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#mainfile"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#mainfile", "title": "Glossary", "section": "Mainfile ¶", "meta": {"type": "glossary", "term": "Mainfile ¶"}}
{"question": "What does Metadata ¶ mean in NOMAD?", "gold_answer": "In NOMAD metadata refers to a specific technical sub-set of processed data . The metadata of an entry comprises ids, timestamps, hashes, authors, datasets, references, used schema, and other information.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#metadata"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#metadata", "title": "Glossary", "section": "Metadata ¶", "meta": {"type": "glossary", "term": "Metadata ¶"}}
{"question": "What does Metainfo ¶ mean in NOMAD?", "gold_answer": "The term metainfo refers to the sum of all schemas . In particular it is associated with all pre-defined schemas that are used to represent all processed data in a standardized way. Similar to an ontology , the metainfo provides additional meaning by associated in each piece of data with name, description, categories, type, shape, units, and more.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#metainfo"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#metainfo", "title": "Glossary", "section": "Metainfo ¶", "meta": {"type": "glossary", "term": "Metainfo ¶"}}
{"question": "What does Normalizer ¶ mean in NOMAD?", "gold_answer": "A normalizer is a small tool that can refine the processed data of an entry . Normalizers can read and modify processed data and thereby either normalize (change) some of the data or add normalized derived data. Normalizers are run after parsers and are often used to do processing steps that need to be applied to the outcome of many parsers and are therefore not part of the parsers themselves.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#normalizer"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#normalizer", "title": "Glossary", "section": "Normalizer ¶", "meta": {"type": "glossary", "term": "Normalizer ¶"}}
{"question": "What does Parser ¶ mean in NOMAD?", "gold_answer": "A parser is a small program that takes a mainfile as input and produces processed data . Parsers transform information from a particular source format into NOMAD's structured schema-based format. Parsers start with a mainfile, but can open and read data from other files (e.g. those referenced in the mainfile). Typically, a parser is associated with a certain file-format and is only applied to files of that format.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#parser"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#parser", "title": "Glossary", "section": "Parser ¶", "meta": {"type": "glossary", "term": "Parser ¶"}}
{"question": "What does Plugin ¶ mean in NOMAD?", "gold_answer": "NOMAD installations can be customized through plugins, which are Git repositories containing an installable python package that will add new features upon being installed. Plugins can contain one or many plugin entry points, which represent individual customizations.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#plugin"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#plugin", "title": "Glossary", "section": "Plugin ¶", "meta": {"type": "glossary", "term": "Plugin ¶"}}
{"question": "What does Plugin entry point ¶ mean in NOMAD?", "gold_answer": "Plugin entry points are used to configure and load different types of NOMAD customizations. There are several entry point types, including entry points for parsers, schema packages and apps. A single plugin may contain multiple entry points.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#plugin-entry-point"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#plugin-entry-point", "title": "Glossary", "section": "Plugin entry point ¶", "meta": {"type": "glossary", "term": "Plugin entry point ¶"}}
{"question": "What does Processed data ¶ mean in NOMAD?", "gold_answer": "NOMAD processes (parses and normalizes) all data. The processed data is the outcome of this process. Therefore, each NOMAD entry is associated with processed data that contains all the parsed and normalized information. Processed data always follows a schema . Processed data can be retrieved (via API) or downloaded as .json data.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#processed-data"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#processed-data", "title": "Glossary", "section": "Processed data ¶", "meta": {"type": "glossary", "term": "Processed data ¶"}}
{"question": "What does Processing ¶ mean in NOMAD?", "gold_answer": "NOMAD processes (parses and normalizes) all data. During processing, all provided files are considered. First, files are matched to parsers . Second, files that match with a parser, become mainfiles and an entry is created. Third, we run the parser to create processed data . Fourth, the processed data is further refined by running normalizers . Last, the processed data is saved and indexed. The exact processing time depends on the size of the uploaded data and users can track the processing state of each entry in the GUI.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#processing"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#processing", "title": "Glossary", "section": "Processing ¶", "meta": {"type": "glossary", "term": "Processing ¶"}}
{"question": "What does Quantity ¶ mean in NOMAD?", "gold_answer": "All processed data is structured into sections and quantities. Sections provide hierarchy and organization, quantities refer to the actual pieces of data. In NOMAD, a quantity is the smallest referable unit of processed data . Quantities can have many types and shapes; examples are strings, numbers, lists, or matrices.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#quantity"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#quantity", "title": "Glossary", "section": "Quantity ¶", "meta": {"type": "glossary", "term": "Quantity ¶"}}
{"question": "What does Raw file ¶ mean in NOMAD?", "gold_answer": "A raw file is any file that was provided by a NOMAD user. A raw-file might produce an entry , if it is of a supported file-format, but does not have to. Raw files always belong to an upload and might be associated with an entry (in this case, raw-files are also mainfiles ).", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#raw-file"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#raw-file", "title": "Glossary", "section": "Raw file ¶", "meta": {"type": "glossary", "term": "Raw file ¶"}}
{"question": "What does Results (section results ) ¶ mean in NOMAD?", "gold_answer": "The results are a particular section of processed data . They comprise a summary of the most relevant data for an entry .", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#results-section-results"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#results-section-results", "title": "Glossary", "section": "Results (section results ) ¶", "meta": {"type": "glossary", "term": "Results (section results ) ¶"}}
{"question": "What does Schema ¶ mean in NOMAD?", "gold_answer": "Schemas define possible data structures for processed data . Like a book they organize data hierarchically in sections and subsections . Schemas are similar to ontologies as they define possible relationships between data organized within them.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#schema"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#schema", "title": "Glossary", "section": "Schema ¶", "meta": {"type": "glossary", "term": "Schema ¶"}}
{"question": "What does Schema package ¶ mean in NOMAD?", "gold_answer": "Schema packages contain a collection of schema definitions. Schema packages may be defined as YAML files or in Python as plugin entry points .", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#schema-package"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#schema-package", "title": "Glossary", "section": "Schema package ¶", "meta": {"type": "glossary", "term": "Schema package ¶"}}
{"question": "What does Section and Subsection ¶ mean in NOMAD?", "gold_answer": "All processed data is structured into sections and quantities. Sections provide hierarchy and organization, quantities refer to the actual pieces of data.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#section-and-subsection"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#section-and-subsection", "title": "Glossary", "section": "Section and Subsection ¶", "meta": {"type": "glossary", "term": "Section and Subsection ¶"}}
{"question": "What does Upload ¶ mean in NOMAD?", "gold_answer": "NOMAD organizes raw-files (and all entries created from them) in uploads . Uploads consist of a directory structure of raw-files and a list of respective entries.", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#upload"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#upload", "title": "Glossary", "section": "Upload ¶", "meta": {"type": "glossary", "term": "Upload ¶"}}
{"question": "What does User ¶ mean in NOMAD?", "gold_answer": "A user is anyone with a NOMAD account. It is different from an author as all users can be authors, but not all authors have to be users. All data in NOMAD is always owned by a single user (others can be collaborators and co-authors).", "gold_urls": ["https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#user"], "source_url": "https://fairmat-nfdi.github.io/nomad-docs/reference/glossary.html#user", "title": "Glossary", "section": "User ¶", "meta": {"type": "glossary", "term": "User ¶"}}
