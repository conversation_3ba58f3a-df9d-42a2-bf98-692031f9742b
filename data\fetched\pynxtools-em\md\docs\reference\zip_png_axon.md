# AXON Protochips Portable Network Graphics PNG

The pynxtools-em parser and normalizer reads the following content and maps them on respective NeXus concepts that are defined in the NXem application definition:

| Concept | NeXus/HDF5 |
| --------------- | --------------  |
| MicroscopeControlImageMetadata.ActivePositionerSettings.PositionerSettings.[*].Stage.Name | :heavy_check_mark: |
| MicroscopeControlImageMetadata.AuxiliaryData.AuxiliaryDataCategory.[*].DataValues.AuxiliaryDataValue.[*].HeatingCurrent | :heavy_check_mark: |
| MicroscopeControlImageMetadata.AuxiliaryData.AuxiliaryDataCategory.[*].DataValues.AuxiliaryDataValue.[*].HeatingPower | :heavy_check_mark: |
| MicroscopeControlImageMetadata.AuxiliaryData.AuxiliaryDataCategory.[*].DataValues.AuxiliaryDataValue.[*].HeatingVoltage | :heavy_check_mark: |
| MicroscopeControlImageMetadata.AuxiliaryData.AuxiliaryDataCategory.[*].DataValues.AuxiliaryDataValue.[*].HolderPressure | :heavy_check_mark: |
| MicroscopeControlImageMetadata.AuxiliaryData.AuxiliaryDataCategory.[*].DataValues.AuxiliaryDataValue.[*].HolderTemperature | :heavy_check_mark: |
| MicroscopeControlImageMetadata.MicroscopeSettings.AcceleratingVoltage | :heavy_check_mark: |
| MicroscopeControlImageMetadata.MicroscopeSettings.BeamBlankerState | :heavy_check_mark: |
| MicroscopeControlImageMetadata.MicroscopeSettings.CameraLengthValue | :heavy_check_mark: |
| MicroscopeControlImageMetadata.MicroscopeSettings.MagnificationValue | :heavy_check_mark: |
