**The key elements in NOMAD**

To get started in the journey of uploading raw files to publishing datasets with DOI in NOMAD, you will need to understand the key elements of this process in NOMAD:

1. NOMAD Users
2. Uploads
3. Entries
4. Datasets

In order to upload data into NOMAD, a **user account** is required. 
You can create a user account by following these [steps](materials/CreateaNOMADaccount.pdf).

All files and data created by users are organized in NOMAD as **Uploads**, which function as directories within the user’s account. You can create an upload for each project and structure it into nested folder directories for better organization.

Research data in the form of raw files are added to uploads. If NOMAD has a built-in parser for the file formats, it automatically processes the files and creates **Entries**. These entries represent structured data extracted from the raw files.

Entries can be grouped into **Datasets**, which allow for better organization of related data. Once the dataset is complete, it can be published with a DOI, making the research data findable, accessible, interoperable, and reusable (FAIR).

??? info "Key features of NOMAD **Uploads**"
    - **Creation:** Uploads are created and managed by users.
    - **Structure:** Uploads are associated to a user account and are listed in the *my uploads* page in the GUI. They serve as containers for multiple files and entries, preserving their directory structure and metadata. 
    - **Organization:** Each upload can contain multiple files organized into directories.
    - **Sharing & Publishing:** Entries and files can only be shared, transferred, or published as complete uploads.

??? info "Key features of NOMAD **Entries**"
    - **Creation:** Entries are automatically generated by NOMAD from uploaded raw files or instantiated schemas.
    - **Structure:** Entries are distinct data entities that can be searched and viewed through dedicated pages in the NOMAD GUI.  
    - **Organization:** Entries belong to specific uploads and are linked to a raw file that NOMAD has recognized and parsed.  
    - **Sharing & Publishing:** Entries can be shared as part of an upload, and individual entries can be added to datasets, which can be published with a DOI.  

??? info "Key features of NOMAD **Datasets**"
    - **Creation:** Datasets are created and managed by users.
    - **Structure:** Datasets are associated to a user account and are listed in the *Your datasets* page in the GUI. They serve as containers for curated and related data entires. 
    - **Organization:** Datasets conatins several entries from various uploads. One entry can be contained in many dataset.
    - **Sharing & Publishing:** Datasets are published independent of uploads and you can get a DOI for your datasets.

For more detailed explanation, you can refer to the [NOMAD documentation page](https://nomad-lab.eu/prod/v1/docs/explanation/basics.html). 

### Task 1: Create new upload

The uploads exist in the *Your uploads* page. You can reach there by clicking on **Uploads** in the **PUBLISH** menu.

Here you can view a list of all your uploads with their relevant information. 

You can also create new uploads or add an example upload prepared by others.

![Navigate to "Your uploads" page by hovering over the Publish menu, then clicking on uploads](images/navigating_to_uploads_cropped.png)
To create a new upload, click on the `CREATE A NEW UPLOAD` button. 

A new page will open and an upload ID will be automatically assigned.

![Create a new upload by clicking on the blue button](images/create_new_upload.png)



??? info "Two different views of the **upload** page"
    At the very top of the upload page, you can toggle between two different views for this page:
    
    - **Overview:** This view includes all the tools needed to manage your upload, along with guided steps to start uploading your files to NOMAD. It will also show all the processed files (entries) that you will upload. 
    ![a screenshot of the uploads overview page](images/upload_overview_page.png)
    - **Files:** This view shows all the files included in upload, whether they are raw files or processed files. You can also organize these files into folders as needed. 
    ![A screenshot of the uploads files page](images/uploads_files_page.png)

When you create a new upload, it is automatically assigned the default name *unnamed upload*.

To rename it, click on the <img src="images/change_name_icon.png" alt="Change Name Icon" width="20"> icon next to the name. 

This will make the name field editable, allowing you to enter a clear and descriptive name. Once you're done, click `SAVE` to apply the changes.
![Alt text](images/create_upload_step_4.png)
![Alt text](images/create_upload_step_5_6.png)

### Task 2: Share your upload with a colleague

Uploads in NOMAD can be shared and published.

When you share/publish an upload, all included entries and files will be shared/published.

??? info "What is the difference between sharing and publishing an Upload?"
    **Sharing an upload** allows you to grant access to colleagues or collaborators while working on it. 
    
    - This facilitates collaboration on projects or enables progress reviews during the project. 
    - You can invite individuals with either read/write access (coauthors) or read-only access (reviewers).

    **Publishing an upload** makes it searchable, findable, and accessible on NOMAD for everyone. 
    
    - Once published, the upload becomes immutable, meaning its contents can no longer be modified. 
    - You also have the option to publish with an embargo period, temporarily restricting public access until the embargo expires. 

A NOMAD upload can have four states based on sharing and publishing:

|Status    | Icon   | Description                         |
|----------|--------|-------------------------------------|
|Private   |<img src="images/icon_unpublished.png" alt="Icon of private upload" width="20"> |The upload is private and is visible to the uploader only.|
|Shared    |<img src="images/icon_shared.png" alt="Icon of shared upload" width="20">       |The upload is accessible to selected users but not publicly available.|
|Published |<img src="images/icon_published.png" alt="Icon of published upload" width="20"> |The upload is publicly available to everyone.|
|Visible   |<img src="images/icon_visible.png" alt="Icon of visible upload" width="20">     |The upload is unpublished but accessible to everyone.|

You can manage upload sharing in the *Edit upload members* menu. To access it, click on <img src="images/edit_upload_members_icon.png" alt="Edit upload members Icon" width="20"> available on the top of the upload page. at the top of the upload page. 

Alternatively, you can click the `EDIT UPLOAD MEMBERS` button below the list of entries on your upload page.

??? task "Share your upload"
    <img src="images/icon_shared.png" alt="Icon of shared upload" width="30">

    **Step 1:** Open the *Edit upload members* window, but clicking on the `EDIT UPLOAD MEMBERS` button.

    **Step 2:** Start typing the name of the NOMAD user you want to share the upload with. A list of matching users will appear—select the correct name from the list.

    **Step 3:** Assign a role to the user by selecting either Coauthor or Reviewer from the dropdown menu.
        - Coauthors can edit and add files to the upload.
        - Reviewers can only view the contents of the upload (no editing rights).

    **Step 4:** Click on *submit*. 

    ![screenshots of the steps of sharing your upload](images/sharing_an_upload.png)

??? task "Make your upload visible to everyone"
    <img src="images/icon_visible.png" alt="Icon of shared upload" width="30">

    To make your upload visible to everyone on NOMAD, simply check the box in the *Edit visibility and access* section, located under the list of your entries. 

    This setting allows all users, including guests without an account, to view the upload even before it is published.

    You can still modify the upload’s access settings and edit its contents while it remains visible.

    ![screenshot of the steps of making an upload visible](images/making_an_upload_visible.png)
??? task "Publish your upload"
    <img src="images/icon_published.png" alt="Icon of shared upload" width="30">

    !!! warning "Once an upload is published, it can not be deleted, and the files and entries can not be changed"

    **Step 1:** Select an embargo period (if needed) from the dropdown menu, located in the *publish* section located at the bottom of the upload page. 

    If you would like to publish immediatly, select *No embargo*.

    **Step 2:** Click on the `PUBLISH` button.

    **Step 3:** A prompt for confirmation appears on the screen. Click on PUBLISH. 

    ![screenshots of the steps to publish an upload](images/publishing_an_upload.png)

    Having an embarog on your data means:
        - Raw files and entries remain hidden, except to you and users you share the data with.
        - Some metadata (e.g., chemical formula, system type, spacegroup) will be public.
        - Embargoed entires can be added to datasets and be assigned DOIs.
        - The embargo can be lifted eariler than the assigned duration by the user. 

    The following image shows an example of an embargoed upload and the option to lift the embargo by clicking the `LIFT EMBARGO` button.

    ![screenshot of an embargoed upload](images/embargoed_upload.png)

### Task 3: Add files to your upload

Lets start adding files to your NOMAD upload. We will explore three different examples:

1. Miscellaneous Files.
2. Files of DFT calculation on Iron(III) Oxide.
3. Files of x-ray photoelectron spectroscopy (XPS) measurement on polymers. 

Files can be added to an upload individually, or you can group them into a compressed file in `.zip` or `.tar` formats. 

??? info "Different ways and senarious to upload files to NOMAD"
    In NOMAD, files can be uploaded in different ways, but whether they are processed into Entries or not depends on their format and NOMAD’s built-in parsers. Here’s how it works: 

    **Files that are processed by NOMAD**
    
    - These are files that are recognized by NOMAD, meaning that **a built-in parser exists** for the file you uploaded, the file will be processed according to a data schemas. 
    
    - It means that NOMAD reads the file, extracts data and organizes them based on the data schema which allows for generating visualizations, and analysis automations. 
    
    - Raw files that are processed in NOMAD become **Entries**
    
    - This is common for simulation files since they follow standardized structures. Also a variety of experimental data organized in NeXus format `.nxs` are recognized by NOMAD.

    **Files that are not processed by NOMAD**

    - These are files that **do not have a built-in parser**, meaning NOMAD cannot automatically extract and structure their data.

    - They are still stored in NOMAD as part of your upload, and can be downloaded or shared, but they do not become entries in the database.

    - NOMAD allows you to preview common file formats that are not automatically parsed. This means you can view the contents of the file directly within the platform without having to download it. Examples include: `.txt`, `.csv`, `.pdf`, `.png`, `.jpg`.

#### Task 3.1: Uploading miscellaneous files

??? example "Download the example files for this excercise"
    We have prepared a set of files for this task that can be downloaded from this [link](https://github.com/FAIRmat-NFDI/FAIRmat-tutorial-16/raw/refs/heads/main/tutorial_16_materials/part_3_files/example_files_upload/miscellaneous_data/miscellaneous_data.zip).
    
    Download the folder, then uncompress it on your local machine at you preferred directory.

    The folder contains files of the following formats: `.zip`, `.pdf`, `.jpg`, `.txt`, and `.csv`.

    | file name                      | format | description                                                                 |
    |--------------------------------|--------|-----------------------------------------------------------------------------|
    | FAIRmat_graphics               | .zip   | Compressed file that contains several FAIRmat logos in `.png` format        |
    | JOSS_2023                      | .pdf   | A publication of NOMAD in the Journal of Open Source Software               |
    | Nature_2022                    | .pdf   | A publication of FAIRmat in Nature journal                                  |
    | P3HT_optical_absorption        | .csv   | Results of absorption measurement on P3HT from PerkinElmer spectrometer     |
    | note_properties_of_good_dopants| .txt   | Notes recorded during a conference talk                                     |
    | experiment_polymer_doping      | .jpg   | A photograph of an experiment of preparing doped polymer solutions          |



Note that these files will not create entires in NOMAD, because a built-in parser for them doesn't exist. 

They will be stored in your upload and can be accessed and shared with your colleagues, however, they will not be searchable within NOMAD. In this case, NOMAD acts as a typical storage cloud!

You can add these files to your NOMAD upload. Do so by simply drag and drop the file or by opening the dialog to browse the files in your device.

??? task "Uploading images, pdf files, text files, and tabular data"
    **Drag and drop**

    Start with uploading the file `FAIRmat_graphics.zip`. Lets use the drag and drop method as shown in the animation below. 

    ![An animatation demonstrating the drag-and-drop files in NOMAD ](images/example_3-1_drag_drop_files.gif)

    When a compressed file is uploaded to NOMAD, it will be extracted automatically and the included files will be added to your upload.

    **Open the file browser dialog**

    Upload the remaining files using the file browser dialog as shown in the animation below. 

    ![An animatation demonstrating the open from file browser dialog in NOMAD ](images/example_3-1_file_browser_dialog.gif)


#### Task 3.2: Uploading computations data files

??? example "Download the example files for this excercise"
    We have prepared a set of files for this task that can be downloaded from this [link](https://github.com/FAIRmat-NFDI/FAIRmat-tutorial-16/raw/refs/heads/main/tutorial_16_materials/part_3_files/example_files_upload/computations_data/FHI-aims.zip). 
    
    Download the folder to your local machine at you preferred directory.

    This folder contains the input and output files of a DFT calculation for Iron(III) Oxide using the *FHI-aims* code.

    FHI-aims* an all-electron density-functional-theory package that employs numeric atom-centered basis functions. It is designed for accurate and efficient simulations of molecules, clusters, surfaces, and bulk materials across the periodic table. Its advanced treatment of electronic structure allows for precise calculations of material properties, including band structures, total energies, and magnetic properties. [More information in this link](https://fhi-aims.org/)


The calculations in this example were preformed using a code that is supported by NOMAD, i.e. the *FHI-aims* code.  

NOMAD has a parser for the *FHI-aims* code. This means it will create an **Entry** for these data. 

In other words, NOMAD will read the input and output files and provide all information in NOMAD's unified *metainfo* data schema.

??? task "Uploading input and output files `.zip` of a DFT calculation"

    **Uploading the files**

    Start with uploading the file `FHI-aims.zip`. Lets use the drag and drop method as shown in the animation below. 

    ![Animation of darg and droping a compressed file in NOMAD](images/example_3-2_drag_drop_files.gif)

    After uploading files, processing is automatically triggered. This involves identifying supported file formats in NOMAD and extracting relevant (meta)data. The exact processing steps depend on the use case.
    For example, you can learn more about the [processing of computational data on this link](https://nomad-lab.eu/prod/v1/docs/examples/computational_data/uploading.html#processing-of-computational-data).

    Once processing is complete, NOMAD generates an Entry page that presents the data in a structured, hierarchical format based on the NOMAD *metainfo* schema. 

    **Opening and exploring your Entry**
    
    To go to the entry page, click on the <img src="images/go_to_icon.png" alt="Go to arrow Icon" width="15"> icon next to the entry. Here you can view the metadata and useful visulaization of your data, check the uploaded files, and explore your entry in details. 

    - **OVERVIEW** tab:

        1- On the left, core metadata are displayed.

        2- On the right, various cards present the available information. 

        <img src="images/example_3-2_overview.png" alt="screenshot of the overview page" width="800">
        
        The cards you see depend on the properties or data available for that entry.
    
        For *FHI-aims* files, three main cards appear in the overview page: 

        - *Materials* card: shows key information and visulization of the composition and conventional cell.
        <img src="images/example_3-2_materials_card.png" alt="screenshot of electronic properties card" width="250">

        - *Electronic properties* card: shows the calculated band structure, density of states, and Brillouin zone.
        <img src="images/example_3-2_electronic_properties_card.png" alt="screenshot of materials card" width="250">

        - *Workflow Graph* card: illustrates the various steps in the calculation, including their inputs and outputs. 
        <img src="images/example_3-2_workflow_card.png" alt="screenshot of workflow card" width="250">

    - **FILES** tab:

        This tab shows the uploaded files. NOMAD lists all files in the same directory, as they usually belong together.

        ![screenshot of the files tab](images/example_3-2_files_tab.png)

    - **DATA** tab:

        Also known as the "processed data" tab, this shows the results of the parsing and normalization process done by NOMAD. 
    
        NOMAD puts all the data in a unified, hierarchical, and machine-processable format, following the NOMAD metainfo.

        ![Animation of the data tab](images/example_3-2_data_tab.gif)

#### Task 3.3: Uploading experiments data files

??? example "Download the example files for this excercise"
    We have prepared a set of files for this task that can be downloaded from this [link](https://github.com/FAIRmat-NFDI/FAIRmat-tutorial-16/raw/refs/heads/main/tutorial_16_materials/part_3_files/example_files_upload/experiments_data/xps_nexus_data.zip). 
    
    Download the folder, then uncompress it to your local machine at you preferred directory.

    This folder contains files related to an x-ray photoelectron spectroscopy (XPS) on the polymer PBTTT using SPECS spectrometer. 
    
    It contains the data file in two different formats (`.nxs` and `.xml`), in addtion to an electronic lab notebook (ELN) file (`.yaml`) documenting additional details of the experiment. 

    | file name             | format | description                                                      |
    |-----------------------|--------|------------------------------------------------------------------|
    | PBTTT_XPS_SPECS       | .nxs   | XPS data file in the standard NeXuS file format                  |
    | PBTTT_XPS_SPECS_raw   | .xml   | XPS data in a raw file format as produced by the spectrometer    |
    | ELN_data_xml          | .yaml  | An ELN file used to record additional metadata of the experiment |


NOMAD supports experimental data files in the `.nxs` format.

These files can be uploaded directly to NOMAD, where they are processed, and structured **Entries** are created.

??? task "Uploading experimental data in the `.nxs` format"

    **Uploading the file** 

    Start with uploading the file `PBTTT_XPS_SPECS.nxs`. Lets use the drag and drop method as shown in the animation below.

    ![An animatation demonstrating the drag-and-drop of NeXuS files in NOMAD](images/example_3-3_drag_drop_files_nexus.gif)

    After uploading files, processing is automatically triggered. This involves identifying supported file formats in NOMAD and extracting relevant (meta)data. The exact processing steps depend on the use case.

    Once processing is complete, NOMAD generates an Entry page that presents the data in a structured, hierarchical format based on the NOMAD *metainfo* schema. 

    **Opening and exploring the Entry**

    Click on the <img src="images/go_to_icon.png" alt="Go to arrow Icon" width="15"> icon next to an entry navigates you to the respective entry page.

    Here you can view the metadata and useful visulaization of your data, check the uploaded files, and explore your entry in details. 

    - **OVERVIEW Tab:**

        1- On the left, core metadata are displayed.

        2- On the right, various cards present the available information. 
        
        The cards you see depend on the properties or data available for that entry.
        
        For `.nxs` files, two main cards appear in the overview page: the data viewer and the materials card.

        ![screenshot of the NeXuS entry overview page](images/example_3-3_overview_entry_nexus.png)

    - **FILES Tab:** 
    
        This tab shows the uploaded files. NOMAD lists all files in the same directory, as they usually belong together.

    - **DATA Tab:**

        Also known as the "processed data" tab, this shows the results of the parsing and normalization process done by NOMAD. 
        
        NOMAD puts all the data in a unified, hierarchical, and machine-processable format, following the NOMAD metainfo.

        ![Animation of the DATA Tab in NeXuS entry page](images/example_3-3_data_tab_entry_page_nexus.gif)


Most scientific instruments generate experimental results in formats other than `.nxs`. NOMAD still supports these files by providing a conversion process using the **NexusDataConverter** built-in schema, which transforms raw data into the `.nxs` format.

??? info "NexusDataConverter readers and the NeXuS application definitions"
    **A Reader** is a program designed to interpret and extract data from a specific experimental technique or file format. 

    The reader understands the structure and encoding of the particular data format and provides methods for accessing its contents in a programmatically friendly way.
    It acts as a bridge between raw experimental data and NOMAD by converting the data into the structured file format according to domain-specific application definitions. 

    A list of available readers can be found [here](https://fairmat-nfdi.github.io/pynxtools/reference/plugins.html)

    **A NeXus application definition** provides a structured specification of the terms and metadata required in an `.nxs` data file for a particular scientific application. These definitions outline the minimum set of terms that must be included in the data file for it to be considered valid according to the NeXus format.

    A list of NeXuS application definitions developed by FAIRmat can be found [here](https://fairmat-nfdi.github.io/nexus_definitions/)

    NexusDataConverter uses **readers** to interpret the raw data files, and then structures them according to the outlines of the **application definitions**

In the following examples, you will learn how to upload a raw file from a SPECS instrument in `.xml` format by using the **NexusDataConverter**. You will do this in two ways:

1. Uploading only the raw file.
2. Uploading both the raw file and an ELN file, enriching your data with metadata and ensuring compliance with community standards.

??? task "Uploading experimental data in the `.xml` format"

    **Step 1:** Click on the `CREATE FROM SCHEMA` button in your upload page.
    ![Screenshot of step 1](images/example_3-3_create_from_schema.png)

    **Step 2:** In the *create new entry from schema* window, click on the drop-down menu of the built-in schema, and select `NexusDataConverter`

    **Step 3:** Give a descriptive name for the entry.

    **Step 4:** Click on CREATE. This will take you the NexusDataConverter Entry page. 

    ![Screenshot of steps 2 - 4](images/example_3-3_NexusDataConverter.png)

    **Step 5:** From the reader drop-down menu, choose the approperiate reader for your files. For this exercise select *xps*.

    **Step 6:** From the nxdl drop-down menu, choose the approperiate application definition for your experiment. For this exercise select *NXxps*

    **Step 7:** Upload the raw data file `PBTTT_XPS_SPECS_raw.xml`.

    **Step 8:** Give a descriptive name for the generated `.nxs` file.

    **Step 9:** Click on the save icon to start the conversion process. 

    ![Screenshots of steps 5 - 9](images/example_3-3_NexusDataConverter_2.png)

    Check the overview page of your upload. There you will fine two newly created entries; one for the NexusDataConverter and one for the generated `.nxs` file from you your raw file. 

    NOMAD still stores your `.xml` raw file in the upload directory. 

    ![Screenshot of the upload page after nexus conversion.](images/example_3-3_NexusDataConverter_3.png)


??? task "Uploading experimental data in the `.xml` format with additional ELN data"

    Often, raw files generated by instruments from various vendors lack complete metadata and essential details about the experiment.

    To address this, scientists document all experimental details in an Electronic Lab Notebook (ELN). Combining data from raw files with information from ELNs ensures that experiments are comprehensively described with rich metadata and conform to community standards.
    
    NOMAD’s **NexusDataConverter** allows you to merge experimental raw data files with ELN entries in `.yaml` format, producing `.nxs` files that meet community standards and ensure your data are richly-described.

    The ELN `.yaml` file can be generated by various ELN software tools or created manually using a text editor.

    For each supported experimental raw file format, FAIRmat provides a template ELN `.yaml` file containing all necessary attributes and parameters to complement the raw file’s data. These templates can be found [here](https://github.com/FAIRmat-NFDI/pynxtools-xps/tree/a2e9524ae8479ffa9cde79daf2010161d8ae75c3/examples)

    While these files can be edited with any text editor, we recommend using **VS Code** for an optimized editing experience.

    **Open the `eln_data_xml.yaml` file and edit its contents**

    - Modify the start_time and end_time of your experiment. 
    - Write your information in the `users` section.
    - Explore the other fields available in the ELN file. 
    - Save the file.
    
    ![Alt text](images/example_3-3_modifying_ELN.gif)

    **Upload your data file `.xml` and your ELN data `.yaml` using NexusDataConverter**

    - **Step 1:** Click on the `CREATE FROM SCHEMA` button in your upload page.

    ![Screenshot of step 1](images/example_3-3_create_from_schema.png)

    - **Step 2:** In the *create new entry from schema* window, click on the drop-down menu of the built-in schema, and select `NexusDataConverter`

    - **Step 3:** Give a descriptive name for the entry.

    - **Step 4:** Click on CREATE. This will take you the NexusDataConverter Entry page.

    ![Screenshot of steps 2 - 4](images/example_3-3_NexusDataConverter.png)

    - **Step 5:** From the reader drop-down menu, choose the approperiate reader for your files. For this exercise select *xps*.

    - **Step 6:** From the nxdl drop-down menu, choose the approperiate application definition for your experiment. For this exercise select *NXxps*

    - **Step 7:** Upload the raw data file `PBTTT_XPS_SPECS_raw.xml` as well as the ELN data file `eln_data_xml.yaml`.

    - **Step 8:** Give a descriptive name for the generated `.nxs` file.

    - **Step 9:** Click on the save icon to start the conversion process. 

    ![Screenshots of steps 5 - 9](images/example_3-3_NexusDataConverter_2(with_ELN).png)