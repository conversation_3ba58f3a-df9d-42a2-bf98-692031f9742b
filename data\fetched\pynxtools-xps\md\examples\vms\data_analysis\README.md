# Example for parsing of data analysis performed in CasaXPS

This is an example for extracting data and the description of the data analysis (i.e., peak fitting), which was done in the [CasaXPS data analysis software](http://www.casaxps.com/), from VAMAS (.vms) files, the ISO standard data transfer format ([ISO 14976](https://www.iso.org/standard/24269.html)) for X-ray photoelectron spectroscopy.

See [here](https://fairmat-nfdi.github.io/pynxtools-xps/reference/vms.html#data-analysis-and-peak-fitting) for documentation of how the example conversion can be run.
