# Float Zone experiment
## Max-Planck-Institut für Chemische Physik fester Stoffe - Dresden

#### drag and drop "schema file", "data file", "data set", and "base classes subfolder" into the nomad upload page 

for more info on archive files structure, check out the [README.md](https://github.com/FAIRmat-Experimental/Area_A_application_definitions/blob/main/README.md) file in the root folder of this repo

## schema file (.schema.archive.yaml)
  
a yaml file containing the ELN schema that will be used to structure user's data. 

## data file (.data.archive.yaml)

a yaml file built with the corresponding schema file structure. 

## data set 

files are in preparation

 ## base classes subfolder

this folder is placed in the root level of the repo, the user have to zip it and drag and drop together to the other files. The user finds a zipped copy of base_classes in each experiment folder.
