# Data from SPECS instruments

The reader supports [SpecsLabProdigy](https://www.specs-group.com/nc/specs/products/detail/prodigy/) files, which is the propietary format of SPECS GmbH. Currently, the following file extensions are supported:

See [here](https://fairmat-nfdi.github.io/pynxtools-xps/reference/specs.html) for documentation of how this data format is structured and how the example conversion can be run.