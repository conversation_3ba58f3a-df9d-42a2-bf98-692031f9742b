# Example for .vms data

This is an example for VAMAS (.vms) files, the ISO standard data transfer format ([ISO 14976](https://www.iso.org/standard/24269.html)) for X-ray photoelectron spectroscopy. The data has been stored both in REGULAR (i.e, with an equally spaced energy axis) as well as IRREGULAR mode.

See [here](https://fairmat-nfdi.github.io/pynxtools-xps/reference/vms.html) for documentation of how this data format is structured and how the example conversion can be run.
