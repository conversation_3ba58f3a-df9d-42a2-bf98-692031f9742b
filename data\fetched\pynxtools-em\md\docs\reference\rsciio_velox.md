# ThermoFisher Velox EMD

The pynxtools-em parser and normalizer reads the following content and maps them on respective NeXus concepts that are defined in the NXem application definition:

| Concept | NeXus/HDF5 |
| --------------- | --------------  |
| Acquisition/AcquisitionStartDatetime/DateTime | :heavy_check_mark: |
| Acquisition/SourceType | :heavy_check_mark: |
| Instrument/ControlSoftwareVersion | :heavy_check_mark: |
| Instrument/InstrumentId | :heavy_check_mark: |
| Instrument/InstrumentModel | :heavy_check_mark: |
| Instrument/Manufacturer | :heavy_check_mark: |
| Optics/AccelerationVoltage | :heavy_check_mark: |
| Optics/BeamConvergence | :heavy_check_mark: |
| Optics/CameraLength | :heavy_check_mark: |
| Optics/Defocus | :heavy_check_mark: |
| Optics/DoseRate | :heavy_check_mark: |
| Optics/NominalMagnification | :heavy_check_mark: |
| Optics/OperatingMode | :heavy_check_mark: |
| Optics/ScanRotation | :heavy_check_mark: |
| Optics/TemOperatingSubMode | :heavy_check_mark: |
| Scan/DwellTime | :heavy_check_mark: |
| Stage/AlphaTilt | :heavy_check_mark: |
| Stage/BetaTilt | :heavy_check_mark: |
| Stage/HolderType | :heavy_check_mark: |
| Stage/Position/x | :heavy_check_mark: |
| Stage/Position/y | :heavy_check_mark: |
| Stage/Position/z | :heavy_check_mark: |
| electron_source/ | :heavy_check_mark: |
