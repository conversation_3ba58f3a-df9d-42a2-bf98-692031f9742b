# List of NOMAD tutorials

<!--
Please follow the format:
- Date(dd.mm.yyy) Event: Tutorial Title
    + short list of topics covered
 -->

!!! warning "Attention"
    This page is still being updated with older NOMAD tutorials.

- 17.10.2024 [FAIRmat Tutorial 15: Use of pynxtools with Examples from Optical Spectroscopy](https://events.fairmat-nfdi.eu/event/26/){:target="_blank"}
    + Demonstrates how to use the pynxtools package along with NOMAD to faciliate FAIR research data management for experimental data in materials science. Ellipsometry and Raman spectroscopy data are used as representative examples.

- 10.07.2024 [FAIRmat Tutorial 14: Developing schemas and parsers for FAIR computational data storage using NOMAD-Simulations](https://fairmat-nfdi.github.io/fairmat-tutorial-14-computational-plugins/){:target="_blank"}
    + Provides foundational knowledge for customizing NOMAD to fit the specific needs of your computational research project using the `nomad-simulations` schema plugin.

- 15.05.2024 [FAIRmat Tutorial 13: Getting started with NOMAD and NOMAD Oasis for research data management (RDM)](https://github.com/FAIRmat-NFDI/AreaA-Examples/tree/main/tutorial13){:target="_blank"}
    + Introduces NOMAD and NOMAD Oasis as essential tools for research data management (RDM), and demonstrates how to utilize these tools for managing experimental materials science data, with a particular focus on synthesis data.

- 14.02.2024 [FAIRmat Tutorial 12: Getting started with NOMAD and NOMAD Oasis for research data management (RDM)](https://events.fairmat-nfdi.eu/event/10/){:target="_blank"}
    + A practical session to go through a Jupyter notebook that demonstrates how to use NOMAD for managing custom data and file types. Based on a simple given dataset, we show how to model the data in a schema, do parsing and normalization, process data, access existing data with NOMAD's API for analysis, and how to add visualization to your data.

- 25.10.2023 [FAIRmat Tutorial 11: Research data management, from fundamentals to implementation](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-11/tutorial-11-materials){:target="_blank"}
    + Introduction to the FAIR data principles, Research data management practices, and data management plans.

- 29.09.2023 [CECAM workshop: An Introduction to the NOMAD repository for soft matter simulators](https://fairmat-nfdi.github.io/AreaC-Tutorial-CECAM-2023/){:target="_blank"}
    + Basic NOMAD usage with molecular dynamics simulations, custom workflows, python API

- 14.06.2023 [FAIRmat Tutorial 10: FAIR electronic-structure data in NOMAD](https://fairmat-nfdi.github.io/AreaC-Tutorial10_2023/){:target="_blank"}
    + Basic NOMAD usage for computational data, numerical precision filtering, custom workflows, knowledge-based XC functionals exploration

- 26.04.2023 [FAIRmat Tutorial 9: Plugins: Python schemas and parsers](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-9/tutorial-9-materials){:target="_blank"}
    + Introduction the new plugin mechanism in NOMAD: how to develop and integrate their own Python schemas and parsers to a NOMAD Oasis.

- 15.03.2023 [FAIRmat Tutorial 8: Using NOMAD as an Electronic lab notebook (ELN) for FAIR data](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-8/tutorial-8-materials){:target="_blank"}
    + Using NOMAD as an ELN which enables the users to generate data following the FAIR principles.

- 15.02.2023 [FAIRmat Tutorial 7: Molecular Dynamics Trajectories and Workflows in NOMAD](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-7/tutorial-7-materials){:target="_blank"}
    + Uploading MD data, examining metadata, overview page, workflow visualizer, extracting MD data for trajectory analysis

- 06.11.2022 [FAIRmat Tutorial 6: Experimental data management in NOMAD](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-6/tutorial-6-materials){:target="_blank"}
    + Follow research data lifecycle from planning and running an experiment to collecting and annotating data according to international community standards for searchability, and reuse.

- 05.10.2022 [FAIRmat Tutorial 5: NOMAD Encyclopedia](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-5/tutorial-5-materials){:target="_blank"}
    + Navigate the materials space using the Encyclopedia GUI, and using the Encyclopedia API.

- 11.05.2022 [FAIRmat Tutorial 4: NOMAD Oasis and FAIR Data Collaboration and Sharing](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-4/tutorial-4-materials){:target="_blank"}
    + How to get started with NOMAD Oasis and adapt it to your research.

- 06.04.2022 [FAIRmat Tutorial 3: Introduction to the Artificial Intelligence Toolkit](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-3/tutorial-3-materials){:target="_blank"}
    + NOMAD AI-toolkit: query over the NOMAD Archive via the NOMAD API and basic notebooks for learning AI methods.

- 09.03.2022 [FAIRmat Tutorial 2: Electronic Lab Notebooks and FAIR Data Management](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-2/tutorial-2-materials){:target="_blank"}
    + ELN in FAIR data management, NOMAD ELN, and integrating other ELN tools with NOMAD.

- 09.02.2022 [FAIRmat Tutorial 1: Publish and Explore Data with NOMAD](https://www.fairmat-nfdi.eu/events/fairmat-tutorial-1/tutorial-1-materials){:target="_blank"}
    + Basic NOMAD guide to prepare, upload, publish data, and reference them with a DOI.
