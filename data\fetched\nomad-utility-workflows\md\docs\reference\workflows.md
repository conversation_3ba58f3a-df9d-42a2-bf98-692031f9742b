# Workflow References

## Stand-alone utilities

### `nodes_to_graph()`
::: nomad_utility_workflows.utils.workflows.nodes_to_graph

## User-relevant classes

### `NodeAttributes`
::: nomad_utility_workflows.utils.workflows.NodeAttributes

### `NodeAttributesUniverse`
::: nomad_utility_workflows.utils.workflows.NodeAttributesUniverse

### `NomadWorkflow`
::: nomad_utility_workflows.utils.workflows.NomadWorkflow